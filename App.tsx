import React, { useEffect } from 'react';
import 'react-native-url-polyfill/auto';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import Toast from 'react-native-toast-message';
import { AppNavigator } from './src/navigation';
import { RecordingProvider } from './src/contexts/RecordingContext';
import { GlobalRecordingCapsule } from './src/components/recording/GlobalRecordingCapsule';
import { useFloatingWindow } from './src/hooks/useFloatingWindow';
import {
  DialogProvider,
  GlobalDialogInitializer,
} from './src/components/common/Dialog';
import { toastConfig } from './src/components/common/Toast';
import { initUserStorageWithRetry } from './src/utils/initUserStorage';

// 应用内容组件，使用浮窗Hook
function AppContent() {
  useFloatingWindow(); // 初始化浮窗功能

  // 初始化用户存储（带重试机制，解决原子性问题）
  useEffect(() => {
    const initStorage = async () => {
      try {
        await initUserStorageWithRetry(2); // 最多重试2次
        console.log('✅ 应用启动时初始化用户存储成功');
      } catch (error) {
        console.error('❌ 应用启动时初始化用户存储失败:', error);
        // 即使初始化失败，应用仍然可以继续运行
        // 用户可能需要重新登录来修复存储问题
      }
    };

    initStorage();
  }, []);

  return (
    <>
      <AppNavigator />
      <GlobalRecordingCapsule />
      <StatusBar style="auto" />
      <Toast config={toastConfig} />
    </>
  );
}

export default function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <DialogProvider>
          <RecordingProvider>
            <AppContent />
            <GlobalDialogInitializer />
          </RecordingProvider>
        </DialogProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
