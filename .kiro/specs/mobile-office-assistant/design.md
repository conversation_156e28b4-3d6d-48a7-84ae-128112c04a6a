# 设计文档

## 概述

移动办公助手是一个基于React Native + Expo架构的跨平台移动应用，采用现代化的技术栈和最佳实践。应用将从一个经过验证的技术栈开始，确保在Android模拟器和Web平台上的稳定运行，然后采用渐进式开发方式逐步添加办公功能。

### 核心设计原则

1. **稳定性优先** - 使用经过验证的技术栈版本，确保项目可靠运行
2. **渐进式开发** - 小步快跑，逐步添加功能模块
3. **跨平台一致性** - 在Android和Web平台提供一致的用户体验
4. **模块化架构** - 清晰的代码组织和模块分离
5. **类型安全** - 全面使用TypeScript确保代码质量

## 架构

### 整体架构图

```mermaid
graph TB
    A[App.tsx - 应用入口] --> B[Navigation - 导航层]
    B --> C[Screens - 页面层]
    C --> D[Components - 组件层]
    D --> E[Services - 服务层]
    E --> F[Stores - 状态管理]
    F --> G[Utils - 工具层]
    
    H[AsyncStorage] --> F
    I[API Services] --> E
    J[Toast Messages] --> D
    
    subgraph "外部依赖"
        K[Expo SDK 52.0.0]
        L[React Native 0.76.6]
        M[React Navigation 6.x]
        N[Zustand 4.4.1]
        O[Axios 1.10.0]
    end
```

### 技术栈架构

#### 核心框架层
- **Expo SDK 52.0.0** - 提供跨平台开发能力和原生功能访问
- **React Native 0.76.6** - 核心UI框架
- **TypeScript 5.3.3** - 类型安全和开发体验

#### 导航层
- **React Navigation 6.x** - 应用导航管理
  - Bottom Tabs Navigator - 主要功能模块切换
  - Stack Navigator - 页面堆栈管理
  - 手势处理和屏幕适配

#### 状态管理层
- **Zustand 4.4.1** - 轻量级状态管理
- **AsyncStorage 2.1.0** - 数据持久化
- **状态分片** - 按功能模块组织状态

#### 网络层
- **Axios 1.10.0** - HTTP客户端
- **请求拦截器** - 统一请求处理
- **错误处理** - 统一错误管理

## 组件和接口

### 目录结构设计

```
src/
├── components/           # 可复用组件
│   ├── common/          # 通用组件
│   │   ├── Button/      # 按钮组件
│   │   ├── Input/       # 输入框组件
│   │   ├── Loading/     # 加载组件
│   │   └── Toast/       # 消息提示组件
│   ├── forms/           # 表单组件
│   └── modals/          # 模态框组件
├── screens/             # 页面组件
│   ├── auth/           # 认证相关页面
│   ├── home/           # 首页
│   ├── recording/      # 录音功能
│   ├── knowledge/      # 知识库管理
│   ├── writing/        # AI写作
│   ├── calendar/       # 日程管理 [二期]
│   └── settings/       # 设置页面
├── navigation/          # 导航配置
│   ├── AppNavigator.tsx # 主导航器
│   ├── TabNavigator.tsx # 底部标签导航
│   └── types.ts        # 导航类型定义
├── services/           # 服务层
│   ├── api/           # API服务
│   ├── auth/          # 认证服务
│   └── storage/       # 存储服务
├── stores/            # 状态管理
│   ├── authStore.ts   # 认证状态
│   ├── appStore.ts    # 应用状态
│   └── index.ts       # 状态导出
├── hooks/             # 自定义Hooks
├── utils/             # 工具函数
├── styles/            # 样式定义
└── types/             # 类型定义
```

### 核心组件接口

#### 1. 通用按钮组件
```typescript
interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
}
```

#### 2. 输入框组件
```typescript
interface InputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  secureTextEntry?: boolean;
  multiline?: boolean;
}
```

#### 3. 导航类型定义
```typescript
type RootStackParamList = {
  Home: undefined;
  Recording: undefined;
  Knowledge: undefined;
  Writing: undefined;
  Settings: undefined;
  Calendar: undefined; // [二期功能]
};

type TabParamList = {
  HomeTab: undefined;
  RecordingTab: undefined;
  KnowledgeTab: undefined;
  WritingTab: undefined;
  SettingsTab: undefined;
};
```

### 服务层接口

#### 1. API服务接口
```typescript
interface ApiService {
  get<T>(url: string, config?: AxiosRequestConfig): Promise<T>;
  post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
  put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
  delete<T>(url: string, config?: AxiosRequestConfig): Promise<T>;
}
```

#### 2. 存储服务接口
```typescript
interface StorageService {
  setItem(key: string, value: string): Promise<void>;
  getItem(key: string): Promise<string | null>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
}
```

## 数据模型

### 用户模型
```typescript
interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### 录音模型
```typescript
interface Recording {
  id: string;
  title: string;
  filePath: string;
  duration: number;
  size: number;
  transcription?: string;
  summary?: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}
```

### 知识库文档模型
```typescript
interface KnowledgeDocument {
  id: string;
  title: string;
  content: string;
  type: 'text' | 'audio' | 'pdf' | 'word' | 'other';
  size: number;
  filePath: string;
  categoryId?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}
```

### 知识库分类模型
```typescript
interface KnowledgeCategory {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  order: number;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}
```

### AI写作内容模型
```typescript
interface WritingContent {
  id: string;
  title: string;
  content: string;
  prompt: string;
  type: 'article' | 'optimization' | 'template';
  wordCount: number;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}
```

### 日程模型 [二期功能]
```typescript
interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  location?: string;
  attendees?: string[];
  userId: string;
}
```

## 状态管理设计

### Zustand Store结构

#### 1. 应用状态Store
```typescript
interface AppState {
  isLoading: boolean;
  error: string | null;
  theme: 'light' | 'dark';
  language: 'zh' | 'en';
  
  // Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  setLanguage: (language: 'zh' | 'en') => void;
}
```

#### 2. 认证状态Store
```typescript
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  token: string | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (user: Partial<User>) => void;
}
```

#### 3. 录音状态Store
```typescript
interface RecordingState {
  recordings: Recording[];
  currentRecording: Recording | null;
  isRecording: boolean;
  
  // Actions
  setRecordings: (recordings: Recording[]) => void;
  addRecording: (recording: Recording) => void;
  updateRecording: (id: string, updates: Partial<Recording>) => void;
  deleteRecording: (id: string) => void;
  setCurrentRecording: (recording: Recording | null) => void;
  setIsRecording: (isRecording: boolean) => void;
}
```

#### 4. 知识库状态Store
```typescript
interface KnowledgeState {
  documents: KnowledgeDocument[];
  categories: KnowledgeCategory[];
  currentCategory: KnowledgeCategory | null;
  
  // Actions
  setDocuments: (documents: KnowledgeDocument[]) => void;
  addDocument: (document: KnowledgeDocument) => void;
  updateDocument: (id: string, updates: Partial<KnowledgeDocument>) => void;
  deleteDocument: (id: string) => void;
  
  setCategories: (categories: KnowledgeCategory[]) => void;
  addCategory: (category: KnowledgeCategory) => void;
  updateCategory: (id: string, updates: Partial<KnowledgeCategory>) => void;
  deleteCategory: (id: string) => void;
  setCurrentCategory: (category: KnowledgeCategory | null) => void;
}
```

#### 5. AI写作状态Store
```typescript
interface WritingState {
  contents: WritingContent[];
  currentContent: WritingContent | null;
  isGenerating: boolean;
  
  // Actions
  setContents: (contents: WritingContent[]) => void;
  addContent: (content: WritingContent) => void;
  updateContent: (id: string, updates: Partial<WritingContent>) => void;
  deleteContent: (id: string) => void;
  setCurrentContent: (content: WritingContent | null) => void;
  setIsGenerating: (isGenerating: boolean) => void;
}
```

#### 6. 日程状态Store [二期功能]
```typescript
interface CalendarState {
  events: CalendarEvent[];
  currentEvent: CalendarEvent | null;
  
  // Actions
  setEvents: (events: CalendarEvent[]) => void;
  addEvent: (event: CalendarEvent) => void;
  updateEvent: (id: string, updates: Partial<CalendarEvent>) => void;
  deleteEvent: (id: string) => void;
  setCurrentEvent: (event: CalendarEvent | null) => void;
}
```

## 错误处理

### 错误处理策略

#### 1. 网络错误处理
```typescript
interface ApiError {
  code: string;
  message: string;
  details?: any;
}

class ApiErrorHandler {
  static handle(error: AxiosError): ApiError {
    if (error.response) {
      // 服务器响应错误
      return {
        code: error.response.status.toString(),
        message: error.response.data?.message || '服务器错误',
        details: error.response.data
      };
    } else if (error.request) {
      // 网络错误
      return {
        code: 'NETWORK_ERROR',
        message: '网络连接失败，请检查网络设置',
      };
    } else {
      // 其他错误
      return {
        code: 'UNKNOWN_ERROR',
        message: '未知错误，请稍后重试',
      };
    }
  }
}
```

#### 2. 全局错误边界
```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  // 错误捕获和恢复逻辑
}
```

#### 3. Toast消息系统
```typescript
interface ToastConfig {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
}

class ToastService {
  static show(config: ToastConfig): void;
  static hide(): void;
}
```

## 测试策略

### 测试层级

#### 1. 单元测试
- **组件测试** - 使用React Native Testing Library
- **工具函数测试** - 使用Jest
- **状态管理测试** - 测试Zustand stores
- **服务层测试** - 模拟API调用

#### 2. 集成测试
- **导航测试** - 测试页面间跳转
- **状态集成测试** - 测试组件与状态的集成
- **API集成测试** - 测试网络请求流程

#### 3. E2E测试
- **关键用户流程** - 使用Detox进行端到端测试
- **跨平台测试** - 确保Android和Web平台一致性

### 测试工具配置
```json
{
  "jest": {
    "preset": "react-native",
    "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"],
    "transformIgnorePatterns": [
      "node_modules/(?!(react-native|@react-native|expo|@expo|@react-navigation)/)"
    ]
  }
}
```

## 性能优化

### 优化策略

#### 1. 代码分割和懒加载
- **屏幕级懒加载** - 使用React.lazy()
- **组件级优化** - 使用React.memo()
- **状态选择器优化** - 避免不必要的重渲染

#### 2. 图片和资源优化
- **图片压缩** - 使用Expo Image Picker的压缩选项
- **资源缓存** - 利用Expo的资源缓存机制
- **SVG图标** - 使用react-native-svg减少包大小

#### 3. 内存管理
- **列表虚拟化** - 使用FlatList的优化选项
- **定时器清理** - 及时清理定时器和监听器
- **图片内存管理** - 合理控制图片加载和释放

#### 4. 启动性能
- **启动屏优化** - 合理的启动屏设计
- **初始化优化** - 延迟非关键功能的初始化
- **Bundle分析** - 使用Metro Bundle Analyzer分析包大小

## 安全考虑

### 安全措施

#### 1. 数据安全
- **敏感数据加密** - 使用Expo SecureStore存储敏感信息
- **网络传输加密** - 强制使用HTTPS
- **输入验证** - 客户端和服务端双重验证

#### 2. 认证安全
- **Token管理** - 安全的Token存储和刷新机制
- **生物识别** - 支持指纹和面部识别
- **会话管理** - 合理的会话超时设置

#### 3. 代码安全
- **代码混淆** - 生产环境代码混淆
- **API密钥保护** - 使用环境变量管理敏感配置
- **权限最小化** - 只请求必要的系统权限

## 部署和构建

### 构建配置

#### 1. 开发环境
```bash
# 启动开发服务器
npx expo start

# Web开发
npx expo start --web

# Android开发
npx expo run:android
```

#### 2. 生产构建
```bash
# 构建Android APK
npx expo build:android

# 构建Web版本
npx expo export:web
```

#### 3. 发布流程
- **版本管理** - 使用语义化版本控制
- **自动化构建** - 集成CI/CD流程
- **测试部署** - 先发布到测试环境验证

### 环境配置
```typescript
interface AppConfig {
  API_BASE_URL: string;
  APP_VERSION: string;
  ENVIRONMENT: 'development' | 'staging' | 'production';
}
```

## 国际化支持

### 多语言设计
- **语言包结构** - 按功能模块组织翻译文件
- **动态语言切换** - 运行时语言切换支持
- **RTL支持** - 为阿拉伯语等RTL语言预留支持

### 本地化考虑
- **日期时间格式** - 根据地区显示合适的格式
- **数字格式** - 支持不同地区的数字显示习惯
- **文化适配** - 考虑不同文化的UI设计差异