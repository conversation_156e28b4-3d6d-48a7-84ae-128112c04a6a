{"enabled": true, "name": "Auto Conventional Commit", "description": "Automatically creates git commits following Conventional Commits specification when staged changes are detected after task completion", "version": "1", "when": {"type": "fileEdited", "patterns": ["mobile-office-assistant/src/**/*.ts", "mobile-office-assistant/src/**/*.tsx", "mobile-office-assistant/src/**/*.js", "mobile-office-assistant/src/**/*.jsx", "mobile-office-assistant/*.json", "mobile-office-assistant/*.js", "mobile-office-assistant/*.ts"]}, "then": {"type": "askAgent", "prompt": "Check if there are staged git changes in the repository. If there are staged changes, analyze the changes and create a git commit following the Conventional Commits specification (format: type(scope): description). Common types include: feat, fix, docs, style, refactor, test, chore. Determine the appropriate type and scope based on the changes, then execute the git commit command."}}