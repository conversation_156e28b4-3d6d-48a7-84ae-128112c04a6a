# 胶囊协调系统集成指南

## 概述

这个胶囊协调系统实现了原生胶囊和RN胶囊之间的高级协调功能，包括：

1. **互斥显示**：APP在前台时显示RN胶囊，后台时显示原生胶囊
2. **位置同步**：两个胶囊之间的位置保持实时同步
3. **点击唤醒**：点击原生胶囊可以唤醒APP并切换到RN胶囊
4. **状态同步**：录音状态在两个胶囊之间保持一致

## 核心组件

### 1. CapsulePositionManager
- 负责胶囊位置的持久化存储和同步
- 使用SharedPreferences存储位置数据
- 提供坐标转换功能（原生像素 ↔ RN DP）

### 2. CapsuleCoordinator
- 中央协调器，管理所有胶囊状态
- 监听APP前后台状态变化
- 协调原生胶囊和RN胶囊的显示/隐藏
- 处理位置同步和点击事件

### 3. FloatingWindowModule (扩展版)
- React Native桥接模块
- 提供新的API方法支持协调功能
- 处理RN端的事件通信

### 4. FloatingWindowManager (扩展版)
- 原生浮窗管理器
- 集成CapsuleCoordinator支持
- 支持位置同步和点击唤醒

### 5. RecordingCapsule (扩展版)
- RN胶囊组件
- 支持位置同步事件监听
- 自动与原生胶囊保持位置一致

## 使用方法

### 1. 开始录音

```javascript
import { NativeModules } from 'react-native';
const { FloatingWindowModule } = NativeModules;

// 使用新的API开始录音
await FloatingWindowModule.startRecording({
  duration: 0,
  isRecording: true,
  isPaused: false
});
```

### 2. 更新录音状态

```javascript
await FloatingWindowModule.updateRecording({
  duration: currentDuration,
  isRecording: true,
  isPaused: false
});
```

### 3. 停止录音

```javascript
await FloatingWindowModule.stopRecording();
```

### 4. 监听胶囊事件

```javascript
import { DeviceEventEmitter } from 'react-native';

// 监听位置同步事件
const positionSyncListener = DeviceEventEmitter.addListener(
  'onCapsulePositionSync',
  (data) => {
    console.log('位置同步:', data.x, data.y, data.isCollapsed);
  }
);

// 监听原生胶囊停止事件
const stopListener = DeviceEventEmitter.addListener(
  'onNativeCapsuleStop',
  () => {
    // 处理停止录音逻辑
    handleStopRecording();
  }
);

// 记得清理监听器
return () => {
  positionSyncListener.remove();
  stopListener.remove();
};
```

### 5. 获取当前位置

```javascript
const position = await FloatingWindowModule.getCurrentPosition();
console.log('当前位置:', position.x, position.y, position.isCollapsed);
```

### 6. 重置位置

```javascript
await FloatingWindowModule.resetPosition();
```

## 事件类型

### RN接收的事件

- `onShowRNCapsule`: 显示RN胶囊（APP进入前台时）
- `onHideRNCapsule`: 隐藏RN胶囊（APP进入后台时）
- `onCapsuleUpdate`: 胶囊状态更新
- `onCapsulePositionSync`: 位置同步事件
- `onNativeCapsuleStop`: 原生胶囊停止按钮被点击

## 工作流程

### APP前台 → 后台
1. CapsuleCoordinator检测到APP进入后台
2. 隐藏RN胶囊（发送`onHideRNCapsule`事件）
3. 显示原生胶囊（从位置管理器获取同步位置）

### APP后台 → 前台
1. CapsuleCoordinator检测到APP进入前台
2. 隐藏原生胶囊
3. 显示RN胶囊（发送`onShowRNCapsule`事件）
4. 同步位置到RN胶囊

### 位置拖拽同步
1. 用户拖拽任一胶囊
2. 拖拽结束时，位置信息保存到SharedPreferences
3. 通过CapsuleCoordinator通知另一个胶囊更新位置

### 点击原生胶囊唤醒APP
1. 用户点击原生胶囊
2. CapsuleCoordinator创建启动Intent
3. APP被唤醒并进入前台
4. 自动触发前台切换流程

## 调试和状态查看

### 获取状态信息

```javascript
const statusInfo = await FloatingWindowModule.getStatusInfo();
console.log('协调器状态:', statusInfo);
```

### 日志标签
- `CapsuleCoordinator`: 协调器相关日志
- `CapsulePositionManager`: 位置管理相关日志
- `FloatingWindowManager`: 原生浮窗相关日志
- `FloatingWindowModule`: RN桥接模块相关日志

## 注意事项

### 1. 权限要求
- 需要`SYSTEM_ALERT_WINDOW`权限用于原生浮窗
- 需要在`AndroidManifest.xml`中添加权限声明

### 2. 生命周期管理
- CapsuleCoordinator会自动管理生命周期
- 组件销毁时会自动清理资源

### 3. 兼容性
- 保持了与旧版本API的兼容性
- 旧方法会输出警告并重定向到新方法

### 4. 性能考虑
- 位置同步使用防抖机制避免频繁更新
- 事件发送使用异步处理避免阻塞UI

## 故障排除

### 1. 胶囊不显示
- 检查浮窗权限是否已授予
- 查看日志确认CapsuleCoordinator是否正确初始化

### 2. 位置同步不工作
- 确认位置事件监听器已正确设置
- 检查SharedPreferences是否有写入权限

### 3. 点击唤醒不工作
- 确认APP的启动Intent配置正确
- 检查是否有其他应用阻止了Intent

### 4. 前后台切换异常
- 查看ActivityLifecycleCallbacks是否正确注册
- 检查应用权限和后台运行策略

## 测试建议

1. **基本功能测试**
   - 开始/停止录音
   - 前后台切换
   - 胶囊拖拽

2. **同步功能测试**
   - 位置同步准确性
   - 状态同步一致性
   - 事件响应及时性

3. **边界情况测试**
   - 快速切换前后台
   - 权限被撤销的情况
   - 低内存环境下的表现

4. **性能测试**
   - 长时间录音的稳定性
   - 频繁拖拽的性能表现
   - 内存使用情况