package com.gzai168.zipco.floating

import android.app.Application
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import com.facebook.react.bridge.*
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.modules.core.DeviceEventManagerModule

/**
 * React Native桥接模块（扩展版）
 * 提供浮窗功能的JavaScript接口，集成CapsuleCoordinator实现高级协调功能
 * 
 * 新增功能：
 * 1. 与CapsuleCoordinator集成
 * 2. 支持胶囊位置同步
 * 3. 支持前后台切换时的胶囊互斥显示
 * 4. 支持点击原生胶囊唤醒APP
 */
@ReactModule(name = FloatingWindowModule.NAME)
class FloatingWindowModule(private val reactContext: ReactApplicationContext) : 
    ReactContextBaseJavaModule(reactContext), LifecycleEventListener {
    
    private val tag = "FloatingWindowModule"
    private var capsuleCoordinator: CapsuleCoordinator? = null
    private var isInitialized = false
    
    companion object {
        const val NAME = "FloatingWindowModule"
    }
    
    override fun getName(): String = NAME
    
    init {
        // 添加生命周期监听
        reactContext.addLifecycleEventListener(this)
        initializeCapsuleCoordinator()
    }
    
    /**
     * 初始化胶囊协调器
     */
    private fun initializeCapsuleCoordinator() {
        Log.d(tag, "🚀 开始初始化胶囊协调器")
        
        try {
            val application = reactContext.applicationContext as Application
            Log.d(tag, "📱 获取Application实例: ${application != null}")
            
            capsuleCoordinator = CapsuleCoordinator.getInstance(application)
            Log.d(tag, "🎯 获取CapsuleCoordinator实例: ${capsuleCoordinator != null}")
            
            capsuleCoordinator?.initialize()
            Log.d(tag, "✅ CapsuleCoordinator初始化调用完成")
            
            // 设置FloatingWindowModule引用，实现双向通信
            capsuleCoordinator?.setFloatingWindowModule(this)
            Log.d(tag, "🔗 FloatingWindowModule双向引用设置完成")
            
            isInitialized = true
            Log.d(tag, "🎉 CapsuleCoordinator初始化成功")
            
            // 输出状态信息
            Log.d(tag, "📊 初始化完成状态: ${capsuleCoordinator?.getStatusInfo()}")
        } catch (e: Exception) {
            Log.e(tag, "❌ CapsuleCoordinator初始化失败", e)
            isInitialized = false
        }
    }
    
    override fun onHostResume() {
        // React Native模块恢复时
        Log.d(tag, "FloatingWindowModule恢复")
    }
    
    override fun onHostPause() {
        // React Native模块暂停时
        Log.d(tag, "FloatingWindowModule暂停")
    }
    
    override fun onHostDestroy() {
        // React Native模块销毁时
        Log.d(tag, "FloatingWindowModule销毁")
        capsuleCoordinator?.destroy()
        reactContext.removeLifecycleEventListener(this)
    }
    
    /**
     * 检查是否有浮窗权限
     */
    @ReactMethod
    fun hasOverlayPermission(promise: Promise) {
        try {
            val hasPermission = capsuleCoordinator?.getFloatingWindowManager()?.hasOverlayPermission() ?: false
            promise.resolve(hasPermission)
        } catch (e: Exception) {
            promise.reject("PERMISSION_CHECK_ERROR", e.message, e)
        }
    }
    
    /**
     * 请求浮窗权限
     */
    @ReactMethod
    fun requestOverlayPermission(promise: Promise) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!Settings.canDrawOverlays(reactContext)) {
                    val intent = Intent(
                        Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                        Uri.parse("package:${reactContext.packageName}")
                    ).apply {
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    }
                    reactContext.startActivity(intent)
                    promise.resolve(false) // 用户需要手动授权
                } else {
                    promise.resolve(true) // 已有权限
                }
            } else {
                promise.resolve(true) // Android 6.0以下默认有权限
            }
        } catch (e: Exception) {
            promise.reject("PERMISSION_REQUEST_ERROR", e.message, e)
        }
    }
    
    /**
     * 开始录音（新方法）
     * 使用CapsuleCoordinator来管理胶囊显示
     */
    @ReactMethod
    fun startRecording(data: ReadableMap, promise: Promise) {
        try {
            if (!isInitialized) {
                promise.reject("NOT_INITIALIZED", "CapsuleCoordinator未初始化")
                return
            }
            
            val duration = if (data.hasKey("duration")) data.getInt("duration") else 0
            val isRecording = if (data.hasKey("isRecording")) data.getBoolean("isRecording") else true
            val isPaused = if (data.hasKey("isPaused")) data.getBoolean("isPaused") else false
            
            capsuleCoordinator?.startRecording(duration, isRecording, isPaused)
            promise.resolve(true)
            
            Log.d(tag, "录音开始 - duration: $duration, isRecording: $isRecording, isPaused: $isPaused")
        } catch (e: Exception) {
            promise.reject("START_RECORDING_ERROR", e.message, e)
        }
    }
    
    /**
     * 停止录音（新方法）
     */
    @ReactMethod
    fun stopRecording(promise: Promise) {
        try {
            capsuleCoordinator?.stopRecording()
            promise.resolve(true)
            Log.d(tag, "录音停止")
        } catch (e: Exception) {
            promise.reject("STOP_RECORDING_ERROR", e.message, e)
        }
    }
    
    /**
     * 更新录音状态（新方法）
     */
    @ReactMethod
    fun updateRecording(data: ReadableMap, promise: Promise) {
        try {
            Log.d(tag, "📞 收到RN更新录音请求")
            
            if (!isInitialized) {
                Log.e(tag, "❌ CapsuleCoordinator未初始化")
                promise.reject("NOT_INITIALIZED", "CapsuleCoordinator未初始化")
                return
            }
            
            val duration = data.getInt("duration")
            val isRecording = data.getBoolean("isRecording")
            val isPaused = data.getBoolean("isPaused")
            
            Log.d(tag, "📦 RN更新数据 - duration: ${duration}s, isRecording: $isRecording, isPaused: $isPaused")
            
            capsuleCoordinator?.updateRecording(duration, isRecording, isPaused)
            promise.resolve(true)
            
            Log.d(tag, "✅ RN更新录音请求处理完成")
        } catch (e: Exception) {
            Log.e(tag, "❌ RN更新录音失败", e)
            promise.reject("UPDATE_RECORDING_ERROR", e.message, e)
        }
    }
    
    /**
     * 更新RN胶囊位置（新方法）
     * 当RN胶囊被拖拽时调用
     */
    @ReactMethod
    fun updateRNCapsulePosition(data: ReadableMap, promise: Promise) {
        try {
            if (!isInitialized) {
                promise.reject("NOT_INITIALIZED", "CapsuleCoordinator未初始化")
                return
            }
            
            val x = data.getDouble("x").toFloat()
            val y = data.getDouble("y").toFloat()
            val isCollapsed = if (data.hasKey("isCollapsed")) data.getBoolean("isCollapsed") else false
            
            capsuleCoordinator?.onRNCapsulePositionUpdate(x, y, isCollapsed)
            promise.resolve(true)
            
            Log.d(tag, "RN胶囊位置更新: ($x, $y), collapsed: $isCollapsed")
        } catch (e: Exception) {
            promise.reject("UPDATE_POSITION_ERROR", e.message, e)
        }
    }
    
    /**
     * 获取当前胶囊位置（新方法）
     */
    @ReactMethod
    fun getCurrentPosition(promise: Promise) {
        try {
            val positionManager = capsuleCoordinator?.getPositionManager()
            val position = positionManager?.getCurrentPosition()
            
            if (position != null) {
                val (rnX, rnY) = positionManager.convertToRNPosition(position.x, position.y)
                
                val result = Arguments.createMap().apply {
                    putDouble("x", rnX.toDouble()) 
                    putDouble("y", rnY.toDouble())
                    putBoolean("isCollapsed", position.isCollapsed)
                    putDouble("timestamp", position.timestamp.toDouble())
                }
                
                promise.resolve(result)
            } else {
                promise.reject("GET_POSITION_ERROR", "无法获取当前位置")
            }
        } catch (e: Exception) {
            promise.reject("GET_POSITION_ERROR", e.message, e)
        }
    }
    
    /**
     * 重置胶囊位置为默认值（新方法）
     */
    @ReactMethod
    fun resetPosition(promise: Promise) {
        try {
            capsuleCoordinator?.getPositionManager()?.resetToDefault()
            promise.resolve(true)
            Log.d(tag, "胶囊位置已重置为默认值")
        } catch (e: Exception) {
            promise.reject("RESET_POSITION_ERROR", e.message, e)
        }
    }
    
    /**
     * 获取状态信息（调试用，新方法）
     */
    @ReactMethod
    fun getStatusInfo(promise: Promise) {
        try {
            val statusInfo = capsuleCoordinator?.getStatusInfo() ?: "CapsuleCoordinator未初始化"
            promise.resolve(statusInfo)
        } catch (e: Exception) {
            promise.reject("GET_STATUS_ERROR", e.message, e)
        }
    }
    
    /**
     * 显示浮窗（兼容旧版本，已弃用）
     * 建议使用 startRecording() 方法
     */
    @ReactMethod
    fun showFloatingWindow(data: ReadableMap, promise: Promise) {
        Log.w(tag, "showFloatingWindow() 已弃用，建议使用 startRecording()")
        startRecording(data, promise)
    }
    
    /**
     * 隐藏浮窗（兼容旧版本，已弃用）
     * 建议使用 stopRecording() 方法
     */
    @ReactMethod
    fun hideFloatingWindow(promise: Promise) {
        Log.w(tag, "hideFloatingWindow() 已弃用，建议使用 stopRecording()")
        stopRecording(promise)
    }
    
    /**
     * 更新浮窗内容（兼容旧版本，已弃用）
     * 建议使用 updateRecording() 方法
     */
    @ReactMethod
    fun updateFloatingWindow(data: ReadableMap, promise: Promise) {
        Log.w(tag, "updateFloatingWindow() 已弃用，建议使用 updateRecording()")
        updateRecording(data, promise)
    }
    
    /**
     * 发送事件到React Native
     */
    private fun sendEvent(eventName: String, data: WritableMap?) {
        reactContext
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            .emit(eventName, data ?: Arguments.createMap())
    }
    
    /**
     * 处理来自CapsuleCoordinator的事件
     * 这些方法将被CapsuleCoordinator回调使用
     */
    fun onShowRNCapsule(data: WritableMap) {
        sendEvent("onShowRNCapsule", data)
        Log.d(tag, "发送显示RN胶囊事件")
    }
    
    fun onHideRNCapsule(data: WritableMap) {
        sendEvent("onHideRNCapsule", data)
        Log.d(tag, "发送隐藏RN胶囊事件")
    }
    
    fun onCapsuleUpdate(data: WritableMap) {
        sendEvent("onCapsuleUpdate", data)
    }
    
    fun onCapsulePositionSync(data: WritableMap) {
        sendEvent("onCapsulePositionSync", data)
        Log.d(tag, "发送胶囊位置同步事件")
    }
    
    fun onNativeCapsuleStop(data: WritableMap) {
        sendEvent("onNativeCapsuleStop", data)
        Log.d(tag, "发送原生胶囊停止事件")
    }
    
    /**
     * 获取支持的事件列表和常量
     */
    override fun getConstants(): MutableMap<String, Any> {
        return hashMapOf(
            "EVENTS" to mapOf(
                // 原有事件（兼容性）
                "FLOATING_WINDOW_PRESS" to "onFloatingWindowPress",
                "FLOATING_WINDOW_STOP" to "onFloatingWindowStop",
                
                // 新增事件
                "SHOW_RN_CAPSULE" to "onShowRNCapsule",
                "HIDE_RN_CAPSULE" to "onHideRNCapsule", 
                "CAPSULE_UPDATE" to "onCapsuleUpdate",
                "CAPSULE_POSITION_SYNC" to "onCapsulePositionSync",
                "NATIVE_CAPSULE_STOP" to "onNativeCapsuleStop"
            ),
            "POSITION_CONSTANTS" to mapOf(
                "CAPSULE_WIDTH" to 200,
                "CAPSULE_HEIGHT" to 50,
                "WAVEFORM_WIDTH" to 44
            ),
            "STATUS" to mapOf(
                "INITIALIZED" to isInitialized
            )
        )
    }
    
    /**
     * 模块清理
     */
    override fun onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy()
        capsuleCoordinator?.destroy()
    }
}