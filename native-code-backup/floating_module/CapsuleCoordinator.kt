package com.gzai168.zipco.floating

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.util.Log
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReactContext
import com.facebook.react.bridge.WritableMap
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.gzai168.zipco.audio.AudioRecordingService
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 胶囊协调器
 * 负责管理原生胶囊和RN胶囊之间的互斥显示、位置同步和状态协调
 * 
 * 核心功能：
 * 1. 互斥显示：APP在前台时显示RN胶囊，后台时显示原生胶囊
 * 2. 位置同步：两个胶囊之间的位置保持同步
 * 3. 点击唤醒：点击原生胶囊可以唤醒APP
 * 4. 状态管理：统一管理录音状态和胶囊显示状态
 */
class CapsuleCoordinator private constructor(private val application: Application) {
    
    private val tag = "CapsuleCoordinator"
    
    // 核心管理器实例
    private var floatingWindowManager: FloatingWindowManager? = null
    private var positionManager: CapsulePositionManager? = null
    private var floatingWindowModule: FloatingWindowModule? = null
    
    // 状态管理
    private val isAppInForeground = AtomicBoolean(true)
    private val isRecordingActive = AtomicBoolean(false)
    private val isNativeCapsuleShowing = AtomicBoolean(false)
    private val isRNCapsuleShowing = AtomicBoolean(false)
    
    // 当前录音数据
    private var currentRecordingData: FloatingWindowData? = null
    
    // 生命周期监听器
    private val lifecycleCallbacks = object : Application.ActivityLifecycleCallbacks {
        private var startedActivityCount = 0
        private var resumedActivityCount = 0
        
        override fun onActivityStarted(activity: Activity) {
            startedActivityCount++
            Log.d(tag, "🔄 Activity启动: ${activity.javaClass.simpleName}, 启动数: $startedActivityCount")
            if (startedActivityCount == 1) {
                // APP进入前台（第一个Activity启动）
                Log.d(tag, "✨ 触发APP进入前台")
                onAppForegrounded(activity)
            }
        }
        
        override fun onActivityStopped(activity: Activity) {
            startedActivityCount--
            // 防止计数器变为负数
            if (startedActivityCount < 0) {
                Log.w(tag, "⚠️ Activity启动计数器异常，重置为0")
                startedActivityCount = 0
            }
            Log.d(tag, "🔄 Activity停止: ${activity.javaClass.simpleName}, 启动数: $startedActivityCount")
            if (startedActivityCount == 0) {
                // APP进入后台（所有Activity都停止）
                Log.d(tag, "✨ 触发APP进入后台")
                onAppBackgrounded()
            }
        }
        
        override fun onActivityResumed(activity: Activity) {
            resumedActivityCount++
            Log.d(tag, "▶️ Activity恢复: ${activity.javaClass.simpleName}, 恢复数: $resumedActivityCount")
            
            // 当有Activity恢复时，APP进入前台
            if (resumedActivityCount == 1) {
                Log.d(tag, "✨ 通过Resume触发APP进入前台")
                onAppForegrounded(activity)
            }
        }
        
        override fun onActivityPaused(activity: Activity) {
            resumedActivityCount--
            if (resumedActivityCount < 0) {
                resumedActivityCount = 0
            }
            Log.d(tag, "⏸️ Activity暂停: ${activity.javaClass.simpleName}, 恢复数: $resumedActivityCount")
            
            // 当所有Activity都暂停时，APP进入后台
            if (resumedActivityCount == 0) {
                Log.d(tag, "✨ 通过Pause触发APP进入后台")
                onAppBackgrounded()
            }
        }
        
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            Log.d(tag, "🆕 Activity创建: ${activity.javaClass.simpleName}")
        }
        
        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            Log.d(tag, "💾 Activity保存状态: ${activity.javaClass.simpleName}")
        }
        
        override fun onActivityDestroyed(activity: Activity) {
            Log.d(tag, "💀 Activity销毁: ${activity.javaClass.simpleName}")
        }
    }
    
    companion object {
        @Volatile
        private var INSTANCE: CapsuleCoordinator? = null
        
        fun getInstance(application: Application): CapsuleCoordinator {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CapsuleCoordinator(application).also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 初始化协调器
     */
    fun initialize() {
        Log.d(tag, "开始初始化CapsuleCoordinator")
        
        try {
            // 注册生命周期监听
            application.registerActivityLifecycleCallbacks(lifecycleCallbacks)
            Log.d(tag, "✅ 生命周期监听器注册成功")
            
            // 初始化位置管理器
            positionManager = CapsulePositionManager.getInstance(application)
            Log.d(tag, "✅ 位置管理器初始化成功: ${positionManager != null}")
            
            // 设置位置变化监听器
            positionManager?.setPositionListener { position ->
                onPositionChanged(position)
            }
            Log.d(tag, "✅ 位置变化监听器设置成功")
            
            Log.d(tag, "🎉 CapsuleCoordinator初始化完成")
            
            // 输出初始状态
            Log.d(tag, "📊 初始状态: ${getStatusInfo()}")
        } catch (e: Exception) {
            Log.e(tag, "❌ CapsuleCoordinator初始化失败", e)
            throw e
        }
    }
    
    /**
     * 设置FloatingWindowModule实例（新增）
     * 用于与RN通信
     */
    fun setFloatingWindowModule(module: FloatingWindowModule?) {
        this.floatingWindowModule = module
        Log.d(tag, "FloatingWindowModule已设置: ${module != null}")
    }
    
    /**
     * 销毁协调器
     */
    fun destroy() {
        Log.d(tag, "销毁CapsuleCoordinator")
        
        // 隐藏所有胶囊
        hideAllCapsules()
        
        // 注销生命周期监听
        application.unregisterActivityLifecycleCallbacks(lifecycleCallbacks)
        
        // 清理资源
        positionManager?.removePositionListener()
        floatingWindowManager = null
        positionManager = null
        
        Log.d(tag, "CapsuleCoordinator销毁完成")
    }
    
    /**
     * 开始录音
     * 根据当前APP状态决显示哪个胶囊，并启动前台服务确保后台录音
     */
    fun startRecording(duration: Int = 0, isRecording: Boolean = true, isPaused: Boolean = false) {
        Log.d(tag, "开始录音 - APP前台状态: ${isAppInForeground.get()}")
        
        isRecordingActive.set(true)
        currentRecordingData = FloatingWindowData(duration, isRecording, isPaused)
        
        // 启动前台服务确保后台录音
        startForegroundRecordingService(duration, isRecording, isPaused)
        
        if (isAppInForeground.get()) {
            // APP在前台：显示RN胶囊，隐藏原生胶囊
            showRNCapsule()
            hideNativeCapsule()
        } else {
            // APP在后台：显示原生胶囊，隐藏RN胶囊
            showNativeCapsule()
            hideRNCapsule()
        }
    }
    
    /**
     * 停止录音
     * 隐藏所有胶囊并停止前台服务
     */
    fun stopRecording() {
        Log.d(tag, "停止录音")
        
        isRecordingActive.set(false)
        currentRecordingData = null
        
        // 停止前台服务
        stopForegroundRecordingService()
        
        hideAllCapsules()
    }
    
    /**
     * 更新录音状态
     */
    fun updateRecording(duration: Int, isRecording: Boolean, isPaused: Boolean) {
        Log.d(tag, "🔄 更新录音状态 - duration: ${duration}s, isRecording: $isRecording, isPaused: $isPaused")
        Log.d(tag, "📊 当前状态 - 录音活跃: ${isRecordingActive.get()}, 原生胶囊显示: ${isNativeCapsuleShowing.get()}, RN胶囊显示: ${isRNCapsuleShowing.get()}")
        
        if (!isRecordingActive.get()) {
            Log.w(tag, "⚠️ 录音未活跃，跳过更新")
            return
        }
        
        currentRecordingData = FloatingWindowData(duration, isRecording, isPaused)
        
        // 更新前台服务
        updateForegroundRecordingService(duration, isRecording, isPaused)
        
        // 更新当前显示的胶囊
        if (isNativeCapsuleShowing.get()) {
            Log.d(tag, "🎯 更新原生胶囊显示 - 时长: ${duration}s")
            floatingWindowManager?.updateFloatingWindow(currentRecordingData!!)
        } else {
            Log.d(tag, "⚠️ 原生胶囊未显示，跳过更新")
        }
        
        if (isRNCapsuleShowing.get()) {
            Log.d(tag, "📱 更新RN胶囊显示 - 时长: ${duration}s")
            sendEventToRN("onCapsuleUpdate", createRecordingDataMap(currentRecordingData!!))
        } else {
            Log.d(tag, "⚠️ RN胶囊未显示，跳过更新")
        }
    }
    
    /**
     * APP进入前台
     */
    private fun onAppForegrounded(activity: Activity) {
        Log.d(tag, "🌅 APP进入前台 - Activity: ${activity.javaClass.simpleName}")
        isAppInForeground.set(true)
        
        // 初始化原生浮窗管理器（如果还没有的话）
        if (floatingWindowManager == null) {
            Log.d(tag, "🏗️ 初始化FloatingWindowManager")
            floatingWindowManager = FloatingWindowManager(activity)
            // 设置CapsuleCoordinator引用，实现双向通信
            floatingWindowManager?.setCapsuleCoordinator(this)
            Log.d(tag, "✅ FloatingWindowManager初始化完成")
        } else {
            Log.d(tag, "♻️ FloatingWindowManager已存在，跳过初始化")
        }
        
        Log.d(tag, "🎙️ 检查录音状态: ${isRecordingActive.get()}")
        if (isRecordingActive.get()) {
            Log.d(tag, "🔄 录音进行中，切换胶囊显示: 隐藏原生 -> 显示RN")
            
            // 获取AudioRecordingService的准确时间，避免使用可能错误的currentRecordingData
            try {
                val serviceIntent = Intent(application, AudioRecordingService::class.java)
                val serviceConnection = object : android.content.ServiceConnection {
                    override fun onServiceConnected(name: android.content.ComponentName?, service: android.os.IBinder?) {
                        val audioService = (service as? AudioRecordingService.AudioRecordingBinder)?.getService()
                        if (audioService != null) {
                            val accurateState = audioService.getCurrentRecordingState()
                            Log.d(tag, "📊 获取准确录音状态: duration=${accurateState.duration}s")
                            
                            // 使用准确的时间更新currentRecordingData
                            currentRecordingData = FloatingWindowData(
                                accurateState.duration, 
                                accurateState.isRecording, 
                                currentRecordingData?.isPaused ?: false
                            )
                            
                            // 解绑服务
                            application.unbindService(this)
                        }
                        
                        // 无论是否获取到准确时间，都继续切换胶囊
                        hideNativeCapsule()
                        showRNCapsule()
                    }
                    
                    override fun onServiceDisconnected(name: android.content.ComponentName?) {
                        // 服务连接失败时，仍然切换胶囊
                        hideNativeCapsule()
                        showRNCapsule()
                    }
                }
                
                // 尝试绑定服务获取准确时间
                val bindSuccess = application.bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE)
                if (!bindSuccess) {
                    Log.w(tag, "⚠️ 无法绑定AudioRecordingService，使用当前数据切换胶囊")
                    hideNativeCapsule()
                    showRNCapsule()
                }
            } catch (e: Exception) {
                Log.e(tag, "❌ 获取准确录音状态失败，使用当前数据", e)
                hideNativeCapsule()
                showRNCapsule()
            }
        } else {
            Log.d(tag, "⏹️ 当前无录音活动")
        }
        
        Log.d(tag, "📊 前台切换完成状态: ${getStatusInfo()}")
    }
    
    /**
     * APP进入后台
     */
    private fun onAppBackgrounded() {
        Log.d(tag, "🔄 APP进入后台 - 开始处理")
        isAppInForeground.set(false)
        
        Log.d(tag, "📊 后台切换时状态 - 录音活跃: ${isRecordingActive.get()}, 原生胶囊显示: ${isNativeCapsuleShowing.get()}, RN胶囊显示: ${isRNCapsuleShowing.get()}")
        Log.d(tag, "🔧 浮窗管理器状态: ${floatingWindowManager != null}")
        Log.d(tag, "🔧 录音数据状态: ${currentRecordingData != null}")
        
        if (isRecordingActive.get()) {
            Log.d(tag, "🎙️ 检测到录音活跃，开始切换胶囊显示")
            
            // 如果正在录音：隐藏RN胶囊，显示原生胶囊
            hideRNCapsule()
            showNativeCapsule()
            
            Log.d(tag, "✅ 胶囊切换完成 - 原生胶囊: ${isNativeCapsuleShowing.get()}, RN胶囊: ${isRNCapsuleShowing.get()}")
        } else {
            Log.d(tag, "⚠️ 录音未活跃，跳过胶囊显示")
        }
        
        Log.d(tag, "📊 处理完成后状态: ${getStatusInfo()}")
    }
    
    /**
     * 显示原生胶囊
     */
    private fun showNativeCapsule() {
        Log.d(tag, "🎯 尝试显示原生胶囊")
        
        currentRecordingData?.let { data ->
            Log.d(tag, "📦 录音数据: duration=${data.duration}, isRecording=${data.isRecording}, isPaused=${data.isPaused}")
            
            // 检查浮窗管理器状态
            if (floatingWindowManager == null) {
                Log.e(tag, "❌ 浮窗管理器为null，无法显示原生胶囊")
                return
            }
            
            // 检查权限
            val hasPermission = floatingWindowManager!!.hasOverlayPermission()
            Log.d(tag, "🔐 浮窗权限状态: $hasPermission")
            
            if (!hasPermission) {
                Log.e(tag, "❌ 缺少浮窗权限，无法显示原生胶囊")
                return
            }
            
            // 尝试显示浮窗
            Log.d(tag, "🚀 开始显示原生浮窗...")
            val success = floatingWindowManager!!.showFloatingWindow(data)
            Log.d(tag, "📊 浮窗显示结果: $success")
            
            if (success) {
                isNativeCapsuleShowing.set(true)
                Log.d(tag, "✅ 原生胶囊显示成功")
                
                // 同步位置
                syncPositionToNative()
            } else {
                Log.e(tag, "❌ 原生胶囊显示失败")
            }
        } ?: run {
            Log.e(tag, "❌ 录音数据为null，无法显示原生胶囊")
        }
    }
    
    /**
     * 隐藏原生胶囊
     */
    private fun hideNativeCapsule() {
        if (isNativeCapsuleShowing.get()) {
            Log.d(tag, "隐藏原生胶囊")
            floatingWindowManager?.hideFloatingWindow()
            isNativeCapsuleShowing.set(false)
        }
    }
    
    /**
     * 显示RN胶囊
     */
    private fun showRNCapsule() {
        currentRecordingData?.let { data ->
            Log.d(tag, "显示RN胶囊")
            
            // 通知RN显示胶囊
            val eventData = createRecordingDataMap(data).apply {
                putBoolean("visible", true)
            }
            sendEventToRN("onShowRNCapsule", eventData)
            isRNCapsuleShowing.set(true)
            
            // 同步位置
            syncPositionToRN()
        }
    }
    
    /**
     * 隐藏RN胶囊
     */
    private fun hideRNCapsule() {
        if (isRNCapsuleShowing.get()) {
            Log.d(tag, "隐藏RN胶囊")
            
            val eventData = Arguments.createMap().apply {
                putBoolean("visible", false)
            }
            sendEventToRN("onHideRNCapsule", eventData)
            isRNCapsuleShowing.set(false)
        }
    }
    
    /**
     * 隐藏所有胶囊
     */
    private fun hideAllCapsules() {
        hideNativeCapsule()
        hideRNCapsule()
    }
    
    /**
     * 位置发生变化时的处理
     */
    private fun onPositionChanged(position: CapsulePositionManager.CapsulePosition) {
        Log.d(tag, "位置变化: (${position.x}, ${position.y}), collapsed: ${position.isCollapsed}")
        
        // 同步位置到当前显示的胶囊
        if (isNativeCapsuleShowing.get()) {
            // 如果原生胶囊正在显示，更新其位置
            syncPositionToNative()
        }
        
        if (isRNCapsuleShowing.get()) {
            // 如果RN胶囊正在显示，通知RN更新位置
            syncPositionToRN()
        }
    }
    
    /**
     * 同步位置到原生胶囊
     * TODO: 位置同步存在时序问题，需要优化
     * 问题：RN胶囊位置更新后，原生胶囊可能不会立即同步到正确位置
     * 原因：GlobalRecordingCapsule.tsx缺少onCapsulePositionSync事件监听
     */
    private fun syncPositionToNative() {
        positionManager?.getCurrentPosition()?.let { position ->
            floatingWindowManager?.updatePosition(position.x, position.y, true)
            Log.d(tag, "同步位置到原生胶囊: (${position.x}, ${position.y})")
        }
    }
    
    /**
     * 同步位置到RN胶囊
     */
    private fun syncPositionToRN() {
        positionManager?.getCurrentPosition()?.let { position ->
            val (rnX, rnY) = positionManager!!.convertToRNPosition(position.x, position.y)
            
            val eventData = Arguments.createMap().apply {
                putDouble("x", rnX.toDouble())
                putDouble("y", rnY.toDouble())
                putBoolean("isCollapsed", position.isCollapsed)
            }
            
            sendEventToRN("onCapsulePositionSync", eventData)
            Log.d(tag, "同步位置到RN胶囊: ($rnX, $rnY)")
        }
    }
    
    /**
     * 处理RN胶囊位置更新
     */
    fun onRNCapsulePositionUpdate(x: Float, y: Float, isCollapsed: Boolean) {
        val (pixelX, pixelY) = positionManager?.convertFromRNPosition(x, y) ?: return
        
        Log.d(tag, "RN胶囊位置更新: ($x, $y) -> ($pixelX, $pixelY)")
        positionManager?.updatePosition(pixelX, pixelY, isCollapsed)
    }
    
    /**
     * 处理原生胶囊位置更新
     */
    fun onNativeCapsulePositionUpdate(x: Int, y: Int, isCollapsed: Boolean) {
        Log.d(tag, "原生胶囊位置更新: ($x, $y)")
        positionManager?.updatePosition(x, y, isCollapsed)
    }
    
    /**
     * 处理原生胶囊点击事件（唤醒APP）
     */
    fun onNativeCapsuleClick() {
        Log.d(tag, "原生胶囊被点击，尝试唤醒APP")
        
        try {
            // 创建启动Intent
            val packageManager = application.packageManager
            val launchIntent = packageManager.getLaunchIntentForPackage(application.packageName)
            
            launchIntent?.let { intent ->
                // 添加更多flag确保能正确唤醒APP
                intent.addFlags(
                    Intent.FLAG_ACTIVITY_NEW_TASK or 
                    Intent.FLAG_ACTIVITY_CLEAR_TOP or
                    Intent.FLAG_ACTIVITY_SINGLE_TOP or
                    Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                )
                application.startActivity(intent)
                Log.d(tag, "APP唤醒成功")
            } ?: run {
                Log.e(tag, "无法获取启动Intent")
            }
        } catch (e: Exception) {
            Log.e(tag, "唤醒APP失败", e)
        }
    }
    
    /**
     * 处理原生胶囊停止按钮点击
     */
    fun onNativeCapsuleStop() {
        Log.d(tag, "原生胶囊停止按钮被点击")
        
        // 通知RN停止录音
        sendEventToRN("onNativeCapsuleStop", Arguments.createMap())
        
        // 停止录音
        stopRecording()
    }
    
    /**
     * 创建录音数据的RN事件映射
     */
    private fun createRecordingDataMap(data: FloatingWindowData): WritableMap {
        return Arguments.createMap().apply {
            putInt("duration", data.duration)
            putBoolean("isRecording", data.isRecording)
            putBoolean("isPaused", data.isPaused)
        }
    }
    
    /**
     * 向RN发送事件（修改为使用FloatingWindowModule）
     */
    private fun sendEventToRN(eventName: String, data: WritableMap) {
        try {
            when (eventName) {
                "onShowRNCapsule" -> floatingWindowModule?.onShowRNCapsule(data)
                "onHideRNCapsule" -> floatingWindowModule?.onHideRNCapsule(data)
                "onCapsuleUpdate" -> floatingWindowModule?.onCapsuleUpdate(data)
                "onCapsulePositionSync" -> floatingWindowModule?.onCapsulePositionSync(data)
                "onNativeCapsuleStop" -> floatingWindowModule?.onNativeCapsuleStop(data)
                else -> Log.w(tag, "未知事件类型: $eventName")
            }
            Log.d(tag, "发送事件到RN: $eventName")
        } catch (e: Exception) {
            Log.e(tag, "发送RN事件失败: $eventName", e)
        }
    }
    
    /**
     * 获取当前状态信息（调试用）
     */
    fun getStatusInfo(): String {
        val position = positionManager?.getCurrentPosition()
        return """
            CapsuleCoordinator Status:
            - App Foreground: ${isAppInForeground.get()}
            - Recording Active: ${isRecordingActive.get()}
            - Native Capsule Showing: ${isNativeCapsuleShowing.get()}
            - RN Capsule Showing: ${isRNCapsuleShowing.get()}
            - Current Position: ${position?.let { "(${it.x}, ${it.y})" } ?: "null"}
            - Position Collapsed: ${position?.isCollapsed ?: false}
        """.trimIndent()
    }
    
    /**
     * 获取浮窗管理器实例（供外部模块使用）
     */
    fun getFloatingWindowManager(): FloatingWindowManager? = floatingWindowManager
    
    /**
     * 获取位置管理器实例（供外部模块使用）
     */
    fun getPositionManager(): CapsulePositionManager? = positionManager
    
    /**
     * 检查APP是否在前台（供AudioRecordingService使用）
     */
    fun isAppInForeground(): Boolean = isAppInForeground.get()
    
    /**
     * 检查原生胶囊是否正在显示（供AudioRecordingService使用）
     */
    fun isNativeCapsuleShowing(): Boolean = isNativeCapsuleShowing.get()
    
    /**
     * 启动前台录音服务
     */
    private fun startForegroundRecordingService(duration: Int, isRecording: Boolean, isPaused: Boolean) {
        try {
            val intent = Intent(application, AudioRecordingService::class.java).apply {
                action = AudioRecordingService.ACTION_START_RECORDING
                putExtra(AudioRecordingService.EXTRA_DURATION, duration)
                putExtra(AudioRecordingService.EXTRA_IS_RECORDING, isRecording)
                putExtra(AudioRecordingService.EXTRA_IS_PAUSED, isPaused)
            }
            
            application.startForegroundService(intent)
            Log.d(tag, "前台录音服务已启动")
        } catch (e: Exception) {
            Log.e(tag, "启动前台录音服务失败", e)
        }
    }
    
    /**
     * 更新前台录音服务
     */
    private fun updateForegroundRecordingService(duration: Int, isRecording: Boolean, isPaused: Boolean) {
        try {
            val intent = Intent(application, AudioRecordingService::class.java).apply {
                action = AudioRecordingService.ACTION_UPDATE_RECORDING
                putExtra(AudioRecordingService.EXTRA_DURATION, duration)
                putExtra(AudioRecordingService.EXTRA_IS_RECORDING, isRecording)
                putExtra(AudioRecordingService.EXTRA_IS_PAUSED, isPaused)
            }
            
            application.startService(intent)
            Log.d(tag, "前台录音服务已更新")
        } catch (e: Exception) {
            Log.e(tag, "更新前台录音服务失败", e)
        }
    }
    
    /**
     * 停止前台录音服务
     */
    private fun stopForegroundRecordingService() {
        try {
            val intent = Intent(application, AudioRecordingService::class.java).apply {
                action = AudioRecordingService.ACTION_STOP_RECORDING
            }
            
            application.startService(intent)
            Log.d(tag, "前台录音服务已停止")
        } catch (e: Exception) {
            Log.e(tag, "停止前台录音服务失败", e)
        }
    }
}