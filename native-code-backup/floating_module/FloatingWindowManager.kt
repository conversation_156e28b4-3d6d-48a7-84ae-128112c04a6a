package com.gzai168.zipco.floating

import android.content.Context
import android.graphics.PixelFormat
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Build
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReactContext
import com.facebook.react.bridge.WritableMap
import com.facebook.react.modules.core.DeviceEventManagerModule

/**
 * Android系统级浮窗管理器（扩展版）
 * 实现录音胶囊的系统级显示，支持跨应用浮窗
 * 新增功能：与CapsuleCoordinator集成，支持位置同步和点击唤醒
 */
class FloatingWindowManager(private val context: Context) {
    
    private val logTag = "FloatingWindowManager"
    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var layoutParams: WindowManager.LayoutParams? = null
    private var isShowing = false
    
    // 浮窗拖拽相关
    private var initialX = 0
    private var initialY = 0
    private var initialTouchX = 0f
    private var initialTouchY = 0f
    private var isDragging = false
    private var downTime = 0L
    
    // UI组件引用
    private var statusText: TextView? = null
    private var waveformContainer: ViewGroup? = null  // 支持LinearLayout和FrameLayout
    private var stopButton: ImageView? = null
    
    // CapsuleCoordinator集成（新增）
    private var capsuleCoordinator: CapsuleCoordinator? = null
    private var positionManager: CapsulePositionManager? = null
    
    companion object {
        // 基于Figma精确设计规格的DP值，调整宽度确保"录音中"文字完整显示
        private const val FLOATING_WINDOW_WIDTH_DP = 200  // 胶囊总宽度（增加20dp确保文字显示完整）
        private const val FLOATING_WINDOW_HEIGHT_DP = 50  // 胶囊总高度（保持不变）
        private const val WAVEFORM_WIDTH_DP = 44           // 波形区域宽度（24px图标 + 8px左边距 + 12px间距）
        private const val CORNER_RADIUS_DP = 24           // 左侧圆角半径
        private const val PADDING_LEFT_DP = 16            // 左边距
        private const val PADDING_RIGHT_DP = 8            // 右边距  
        private const val PADDING_VERTICAL_DP = 8         // 上下内边距
        private const val GAP_DP = 8                      // 元素间距
        private const val STOP_BUTTON_SIZE_DP = 32        // 停止按钮尺寸
        private const val WAVEFORM_BAR_WIDTH = 2          // 波形条宽度
        private const val WAVEFORM_BAR_SPACING = 4.5f     // 波形条间距
        private const val ELEVATION_DP = 12               // 阴影高度
    }
    
    /**
     * DP转像素辅助函数
     */
    private fun dpToPx(dp: Int): Int {
        val density = context.resources.displayMetrics.density
        return (dp * density + 0.5f).toInt()
    }
    
    /**
     * 设置CapsuleCoordinator（新增）
     * 用于集成位置同步和点击唤醒功能
     */
    fun setCapsuleCoordinator(coordinator: CapsuleCoordinator?) {
        this.capsuleCoordinator = coordinator
        this.positionManager = coordinator?.getPositionManager()
        Log.d(logTag, "CapsuleCoordinator已设置: ${coordinator != null}")
    }
    
    /**
     * 更新浮窗位置（新增）
     * 支持从外部同步位置
     */
    fun updatePosition(x: Int, y: Int, animate: Boolean = true) {
        if (!isShowing || layoutParams == null) return
        
        layoutParams?.apply {
            this.x = x
            this.y = y
        }
        
        try {
            windowManager?.updateViewLayout(floatingView, layoutParams)
            Log.d(logTag, "浮窗位置已更新: ($x, $y)")
        } catch (e: Exception) {
            Log.e(logTag, "更新浮窗位置失败", e)
        }
    }
    
    /**
     * 获取当前浮窗位置（新增）
     */
    fun getCurrentPosition(): Pair<Int, Int>? {
        return if (isShowing && layoutParams != null) {
            Pair(layoutParams!!.x, layoutParams!!.y)
        } else {
            null
        }
    }
    
    /**
     * 检查是否有浮窗权限
     */
    fun hasOverlayPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true // Android 6.0以下默认有权限
        }
    }
    
    /**
     * 显示浮窗
     */
    fun showFloatingWindow(data: FloatingWindowData): Boolean {
        Log.d(logTag, "🎯 开始显示浮窗")
        Log.d(logTag, "📦 浮窗数据: duration=${data.duration}, isRecording=${data.isRecording}, isPaused=${data.isPaused}")
        
        // 检查权限
        val hasPermission = hasOverlayPermission()
        Log.d(logTag, "🔐 浮窗权限检查: $hasPermission")
        if (!hasPermission) {
            Log.e(logTag, "❌ 缺少浮窗权限，无法显示")
            return false
        }
        
        // 如果已显示，则更新内容
        if (isShowing) {
            Log.d(logTag, "♻️ 浮窗已显示，更新内容")
            updateFloatingWindow(data)
            return true
        }
        
        try {
            Log.d(logTag, "🏗️ 开始创建浮窗")
            
            // 获取WindowManager
            windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            Log.d(logTag, "✅ 获取WindowManager成功: ${windowManager != null}")
            
            // 创建浮窗视图
            createFloatingView(data)
            Log.d(logTag, "✅ 创建浮窗视图成功: ${floatingView != null}")
            
            // 设置布局参数
            setupLayoutParams()
            Log.d(logTag, "✅ 设置布局参数成功: ${layoutParams != null}")
            Log.d(logTag, "📍 浮窗位置: x=${layoutParams?.x}, y=${layoutParams?.y}")
            
            // 添加到WindowManager
            windowManager?.addView(floatingView, layoutParams)
            isShowing = true
            
            Log.d(logTag, "🎉 浮窗显示成功")
            return true
        } catch (e: Exception) {
            Log.e(logTag, "❌ 浮窗显示失败", e)
            e.printStackTrace()
            return false
        }
    }
    
    /**
     * 隐藏浮窗
     */
    fun hideFloatingWindow() {
        try {
            if (isShowing && floatingView != null) {
                windowManager?.removeView(floatingView)
                isShowing = false
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 更新浮窗内容
     */
    fun updateFloatingWindow(data: FloatingWindowData) {
        Log.d(logTag, "🔄 更新浮窗内容 - duration: ${data.duration}, isRecording: ${data.isRecording}, isPaused: ${data.isPaused}")
        Log.d(logTag, "📊 浮窗状态 - isShowing: $isShowing, statusText: ${statusText != null}")
        
        if (!isShowing) {
            Log.w(logTag, "⚠️ 浮窗未显示，跳过更新")
            return
        }
        
        val newText = formatDuration(data.duration, data.isRecording, data.isPaused)
        Log.d(logTag, "📝 更新文本: $newText")
        
        statusText?.text = newText
        updateWaveform(data.isRecording && !data.isPaused)
        
        Log.d(logTag, "✅ 浮窗内容更新完成")
    }
    
    /**
     * 创建浮窗视图（彻底重构版本）
     */
    private fun createFloatingView(data: FloatingWindowData) {
        floatingView = LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER_VERTICAL  // 关键：确保垂直居中
            
            // 不在这里设置layoutParams，让WindowManager来处理
            
            // 精确的内边距：基于Figma规格（8px 16px）
            setPadding(
                dpToPx(PADDING_LEFT_DP),   // 左16dp
                dpToPx(PADDING_VERTICAL_DP), // 上8dp
                dpToPx(PADDING_RIGHT_DP),    // 右8dp
                dpToPx(PADDING_VERTICAL_DP)  // 下8dp
            )
            
            // 背景和阴影
            background = createCapsuleBackground()
            elevation = dpToPx(ELEVATION_DP).toFloat()
            
            // 1. 左侧波形指示器容器（使用绝对定位）
            waveformContainer = createWaveformIndicatorFixed()
            addView(waveformContainer)
            
            // 2. 中间状态文本（flex=1，垂直居中）
            statusText = TextView(context).apply {
                textSize = 15f
                setTextColor(Color.parseColor("#2A2B33"))
                typeface = android.graphics.Typeface.DEFAULT  // 移除加粗，与RN胶囊保持一致
                text = formatDuration(data.duration, data.isRecording, data.isPaused)
                maxLines = 1
                ellipsize = android.text.TextUtils.TruncateAt.END
                gravity = Gravity.CENTER_VERTICAL  // 文本垂直居中
                setLineSpacing(0f, 1.6f)
                
                layoutParams = LinearLayout.LayoutParams(
                    0,  // 宽度0，使用weight
                    LinearLayout.LayoutParams.MATCH_PARENT,  // 高度填满父容器
                    1f  // weight=1，占据剩余空间
                ).apply {
                    setMargins(dpToPx(GAP_DP), 0, dpToPx(GAP_DP), 0)
                }
            }
            addView(statusText)
            
            // 3. 右侧停止按钮（使用切图，固定尺寸，居中）
            stopButton = ImageView(context).apply {
                scaleType = ImageView.ScaleType.CENTER_INSIDE  // 改为CENTER_INSIDE以保持图像比例
                // 使用与RN胶囊相同的停止按钮切图
                setImageResource(com.gzai168.zipco.R.drawable.stop_button)
                
                // 使用与RN胶囊相同的32dp尺寸，添加左边距调整位置
                layoutParams = LinearLayout.LayoutParams(
                    dpToPx(32),  // 与RN胶囊保持一致的32dp
                    dpToPx(32)   // 与RN胶囊保持一致的32dp
                ).apply {
                    setMargins(dpToPx(-4), 0, 0, 0)  // 左边距-4dp，向左调整按钮位置
                }
                
                setOnClickListener {
                    Log.d(logTag, "原生胶囊停止按钮被点击")
                    
                    // 优先使用CapsuleCoordinator的停止功能
                    if (capsuleCoordinator != null) {
                        capsuleCoordinator!!.onNativeCapsuleStop()
                    } else {
                        // 兼容旧版本：发送事件到RN
                        sendEventToRN("onFloatingWindowStop", null)
                    }
                }
            }
            addView(stopButton)
        }
        
        // 设置交互（包含拖拽和点击检测）
        setupDragListener()
    }
    
    /**
     * 创建胶囊背景（左侧圆角，右侧直角）
     */
    private fun createCapsuleBackground(): GradientDrawable {
        return GradientDrawable().apply {
            setColor(Color.WHITE)
            // 设置圆角：左上、左下为24dp，右上、右下为0
            cornerRadii = floatArrayOf(
                dpToPx(CORNER_RADIUS_DP).toFloat(), dpToPx(CORNER_RADIUS_DP).toFloat(), // 左上
                0f, 0f, // 右上
                0f, 0f, // 右下
                dpToPx(CORNER_RADIUS_DP).toFloat(), dpToPx(CORNER_RADIUS_DP).toFloat()  // 左下
            )
        }
    }
    
    
    /**
     * 创建精确定位的波形指示器（使用绝对定位）
     */
    private fun createWaveformIndicatorFixed(): FrameLayout {
        return FrameLayout(context).apply {
            // 固定容器尺寸：24x24dp（来自Figma）
            layoutParams = LinearLayout.LayoutParams(
                dpToPx(24),
                dpToPx(24)
            )
            
            // 基于Figma的精确坐标和尺寸
            val waveformData = arrayOf(
                Triple(0f, 8.44f, 2f),      // x=0, height=8.44, width=2
                Triple(4.5f, 12.71f, 2f),   // x=4.5, height=12.71, width=2
                Triple(9f, 17f, 2f),        // x=9, height=17, width=2
                Triple(13.5f, 12.71f, 2f),  // x=13.5, height=12.71, width=2
                Triple(18f, 8.44f, 2f)      // x=18, height=8.44, width=2
            )
            
            waveformData.forEach { (x, height, width) ->
                val waveformBar = View(context).apply {
                    background = GradientDrawable().apply {
                        setColor(Color.parseColor("#417FFB"))
                        cornerRadius = 1f  // 微小圆角
                    }
                }
                
                // 使用绝对定位
                val params = FrameLayout.LayoutParams(
                    (width * context.resources.displayMetrics.density + 0.5f).toInt(),  // 宽度转px
                    (height * context.resources.displayMetrics.density + 0.5f).toInt()  // 高度转px
                ).apply {
                    setMargins((x * context.resources.displayMetrics.density + 0.5f).toInt(), 0, 0, 0)  // 设置左边距
                    gravity = Gravity.CENTER_VERTICAL  // 垂直居中
                }
                
                waveformBar.layoutParams = params
                addView(waveformBar)
            }
        }
    }
    
    /**
     * 创建波形指示器（备用方法，保持兼容性）
     */
    private fun createWaveformIndicator(): LinearLayout {
        return LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER_VERTICAL
            
            // Figma精确的波形高度（px单位）：8.44, 12.71, 17, 12.71, 8.44
            val heightsPx = floatArrayOf(8.44f, 12.71f, 17f, 12.71f, 8.44f)
            
            heightsPx.forEachIndexed { index, heightPx ->
                val waveformBar = View(context).apply {
                    // 创建矩形背景（Figma中显示为直角矩形）
                    background = GradientDrawable().apply {
                        setColor(Color.parseColor("#417FFB"))
                        cornerRadius = 1f // 微小圆角，接近直角
                    }
                    layoutParams = LinearLayout.LayoutParams(
                        dpToPx(WAVEFORM_BAR_WIDTH), // 宽度2px
                        (heightPx * context.resources.displayMetrics.density + 0.5f).toInt() // 精确高度转换
                    ).apply {
                        // 波形条间距：4.5px，但第一个条左边距为0
                        val leftMargin = if (index == 0) 0 else (WAVEFORM_BAR_SPACING * context.resources.displayMetrics.density / 2 + 0.5f).toInt()
                        val rightMargin = if (index == heightsPx.size - 1) 0 else (WAVEFORM_BAR_SPACING * context.resources.displayMetrics.density / 2 + 0.5f).toInt()
                        setMargins(leftMargin, 0, rightMargin, 0)
                    }
                }
                addView(waveformBar)
            }
        }
    }
    
    /**
     * 设置窗口参数（修改为支持位置同步）
     */
    private fun setupLayoutParams() {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }
        
        layoutParams = WindowManager.LayoutParams(
            dpToPx(FLOATING_WINDOW_WIDTH_DP),
            dpToPx(FLOATING_WINDOW_HEIGHT_DP),
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            
            // 使用positionManager的位置（如果可用）
            if (positionManager != null) {
                val currentPosition = positionManager!!.getCurrentPosition()
                x = currentPosition.x
                y = currentPosition.y
                Log.d(logTag, "使用同步位置: (${currentPosition.x}, ${currentPosition.y})")
            } else {
                // 默认位置：右下角20%
                val displayMetrics = context.resources.displayMetrics
                x = displayMetrics.widthPixels - dpToPx(FLOATING_WINDOW_WIDTH_DP)  
                y = (displayMetrics.heightPixels * 0.8f).toInt() - dpToPx(FLOATING_WINDOW_HEIGHT_DP)
                Log.d(logTag, "使用默认位置: ($x, $y)")
            }
        }
    }
    
    /**
     * 设置拖拽监听器（支持点击检测）
     */
    private fun setupDragListener() {
        floatingView?.setOnTouchListener { view, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialX = layoutParams?.x ?: 0
                    initialY = layoutParams?.y ?: 0
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    isDragging = false
                    downTime = System.currentTimeMillis()
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    val deltaX = event.rawX - initialTouchX
                    val deltaY = event.rawY - initialTouchY
                    
                    // 检测是否开始拖拽（移动距离超过阈值）
                    val moveDistance = Math.sqrt((deltaX * deltaX + deltaY * deltaY).toDouble())
                    if (moveDistance > 10 && !isDragging) {
                        isDragging = true
                        Log.d(logTag, "开始拖拽，移动距离: $moveDistance")
                    }
                    
                    if (isDragging) {
                        layoutParams?.apply {
                            x = initialX + deltaX.toInt()
                            y = initialY + deltaY.toInt()
                        }
                        
                        windowManager?.updateViewLayout(floatingView, layoutParams)
                    }
                    true
                }
                MotionEvent.ACTION_UP -> {
                    val upTime = System.currentTimeMillis()
                    val touchDuration = upTime - downTime
                    
                    if (isDragging) {
                        // 如果是拖拽，处理拖拽结束
                        Log.d(logTag, "拖拽结束")
                        handleDragEnd()
                    } else if (touchDuration < 500) {
                        // 如果是短时间点击，触发点击事件
                        Log.d(logTag, "检测到点击事件，触发唤醒APP")
                        handleCapsuleClick()
                    }
                    
                    isDragging = false
                    false
                }
                else -> false
            }
        }
    }
    
    /**
     * 处理胶囊点击事件
     */
    private fun handleCapsuleClick() {
        Log.d(logTag, "原生胶囊被点击")
        
        // 优先使用CapsuleCoordinator的唤醒功能
        if (capsuleCoordinator != null) {
            capsuleCoordinator!!.onNativeCapsuleClick()
        } else {
            // 兼容旧版本：发送事件到RN
            sendEventToRN("onFloatingWindowPress", null)
        }
    }
    
    /**
     * 处理拖拽结束（修改为支持位置同步）
     */
    private fun handleDragEnd() {
        val displayMetrics = context.resources.displayMetrics
        val currentX = layoutParams?.x ?: 0
        val currentY = layoutParams?.y ?: 0
        val capsuleWidthPx = dpToPx(FLOATING_WINDOW_WIDTH_DP)
        val waveformWidthPx = dpToPx(WAVEFORM_WIDTH_DP)
        
        // 检查是否需要收缩 - 提高灵敏度，与RN胶囊保持一致（根据新宽度调整）
        val shouldCollapse = currentX > displayMetrics.widthPixels - capsuleWidthPx + dpToPx(30)
        
        val finalX: Int
        val finalY: Int
        
        if (shouldCollapse) {
            // 收缩状态：只露出波形部分（44dp宽度）
            finalX = displayMetrics.widthPixels - waveformWidthPx
            finalY = currentY
        } else {
            // 边界限制
            val safeMargin = dpToPx(20)
            finalX = maxOf(safeMargin, minOf(displayMetrics.widthPixels - capsuleWidthPx, currentX))
            finalY = maxOf(safeMargin + dpToPx(50), minOf(displayMetrics.heightPixels - dpToPx(FLOATING_WINDOW_HEIGHT_DP) - safeMargin - dpToPx(100), currentY))
        }
        
        layoutParams?.apply {
            x = finalX
            y = finalY
        }
        
        // 更新视图位置
        windowManager?.updateViewLayout(floatingView, layoutParams)
        
        // 同步位置到CapsuleCoordinator（新增）
        if (capsuleCoordinator != null) {
            capsuleCoordinator!!.onNativeCapsulePositionUpdate(finalX, finalY, shouldCollapse)
            Log.d(logTag, "原生胶囊位置已同步: ($finalX, $finalY), collapsed: $shouldCollapse")
        }
    }
    
    /**
     * 更新波形动画（支持FrameLayout和LinearLayout两种结构）
     */
    private fun updateWaveform(isActive: Boolean) {
        waveformContainer?.let { container ->
            val alpha = if (isActive) 1f else 0.3f
            
            // 遍历所有子视图，更新透明度
            for (i in 0 until container.childCount) {
                val waveformBar = container.getChildAt(i)
                waveformBar.alpha = alpha
            }
        }
    }
    
    /**
     * 格式化录音时长
     */
    private fun formatDuration(seconds: Int, isRecording: Boolean, isPaused: Boolean): String {
        val mins = seconds / 60
        val secs = seconds % 60
        val timeStr = String.format("%02d:%02d", mins, secs)
        
        val statusStr = when {
            isPaused -> "已暂停"
            isRecording -> "录音中"
            else -> "准备录音"
        }
        
        return "$timeStr $statusStr"
    }
    
    /**
     * 向RN发送事件
     */
    private fun sendEventToRN(eventName: String, data: WritableMap?) {
        if (context is ReactContext) {
            context
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                .emit(eventName, data ?: Arguments.createMap())
        }
    }
}

/**
 * 浮窗数据模型
 */
data class FloatingWindowData(
    val duration: Int,
    val isRecording: Boolean,
    val isPaused: Boolean
)