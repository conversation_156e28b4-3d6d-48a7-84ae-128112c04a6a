package com.gzai168.zipco.floating

import android.content.Context
import android.content.SharedPreferences
import android.util.DisplayMetrics
import androidx.core.content.edit

/**
 * 胶囊位置管理器
 * 负责原生胶囊和RN胶囊之间的位置同步
 * 使用SharedPreferences实现位置数据的持久化存储
 */
class CapsulePositionManager private constructor(private val context: Context) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val displayMetrics: DisplayMetrics = context.resources.displayMetrics
    
    // 位置变化监听器
    private var positionListener: ((CapsulePosition) -> Unit)? = null
    
    companion object {
        private const val PREFS_NAME = "capsule_position_prefs"
        private const val KEY_POSITION_X = "capsule_x"
        private const val KEY_POSITION_Y = "capsule_y" 
        private const val KEY_IS_COLLAPSED = "capsule_collapsed"
        private const val KEY_LAST_UPDATE_TIME = "last_update_time"
        
        // 胶囊尺寸常量（与FloatingWindowManager保持一致）
        private const val CAPSULE_WIDTH_DP = 200
        private const val CAPSULE_HEIGHT_DP = 50
        private const val WAVEFORM_WIDTH_DP = 44
        
        @Volatile
        private var INSTANCE: CapsulePositionManager? = null
        
        fun getInstance(context: Context): CapsulePositionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CapsulePositionManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 胶囊位置数据模型
     */
    data class CapsulePosition(
        val x: Int,          // 屏幕像素坐标X
        val y: Int,          // 屏幕像素坐标Y
        val isCollapsed: Boolean,  // 是否处于收缩状态
        val timestamp: Long  // 更新时间戳
    )
    
    /**
     * 获取当前胶囊位置
     * 如果没有存储的位置，返回默认位置（右下角20%）
     */
    fun getCurrentPosition(): CapsulePosition {
        val defaultX = displayMetrics.widthPixels - dpToPx(CAPSULE_WIDTH_DP)
        val defaultY = (displayMetrics.heightPixels * 0.8f).toInt() - dpToPx(CAPSULE_HEIGHT_DP)
        
        return CapsulePosition(
            x = prefs.getInt(KEY_POSITION_X, defaultX),
            y = prefs.getInt(KEY_POSITION_Y, defaultY),
            isCollapsed = prefs.getBoolean(KEY_IS_COLLAPSED, false),
            timestamp = prefs.getLong(KEY_LAST_UPDATE_TIME, System.currentTimeMillis())
        )
    }
    
    /**
     * 更新胶囊位置
     * 同时触发位置变化监听器
     */
    fun updatePosition(x: Int, y: Int, isCollapsed: Boolean = false) {
        val position = CapsulePosition(
            x = x,
            y = y, 
            isCollapsed = isCollapsed,
            timestamp = System.currentTimeMillis()
        )
        
        savePosition(position)
        positionListener?.invoke(position)
    }
    
    /**
     * 保存位置到SharedPreferences
     */
    private fun savePosition(position: CapsulePosition) {
        prefs.edit {
            putInt(KEY_POSITION_X, position.x)
            putInt(KEY_POSITION_Y, position.y)
            putBoolean(KEY_IS_COLLAPSED, position.isCollapsed)
            putLong(KEY_LAST_UPDATE_TIME, position.timestamp)
        }
    }
    
    /**
     * 设置位置变化监听器
     * 用于通知其他组件位置发生变化
     */
    fun setPositionListener(listener: (CapsulePosition) -> Unit) {
        this.positionListener = listener
    }
    
    /**
     * 移除位置变化监听器
     */
    fun removePositionListener() {
        this.positionListener = null
    }
    
    /**
     * 将RN胶囊的相对坐标转换为原生胶囊的像素坐标
     * RN使用的坐标系可能与原生Android不同
     */
    fun convertFromRNPosition(rnX: Float, rnY: Float): Pair<Int, Int> {
        // RN的坐标系通常以DP为单位，需要转换为像素
        val pixelX = (rnX * displayMetrics.density + 0.5f).toInt()
        val pixelY = (rnY * displayMetrics.density + 0.5f).toInt()
        
        return Pair(pixelX, pixelY)
    }
    
    /**
     * 将原生胶囊的像素坐标转换为RN胶囊的相对坐标
     */
    fun convertToRNPosition(pixelX: Int, pixelY: Int): Pair<Float, Float> {
        val rnX = pixelX / displayMetrics.density
        val rnY = pixelY / displayMetrics.density
        
        return Pair(rnX, rnY)
    }
    
    /**
     * 获取边界限制后的安全位置
     * 确保胶囊不会超出屏幕边界
     */
    fun getSafePosition(x: Int, y: Int, isCollapsed: Boolean): CapsulePosition {
        val safeMargin = dpToPx(20)
        val topMargin = dpToPx(50) // 状态栏和导航栏预留空间
        val bottomMargin = dpToPx(100) // 底部导航栏预留空间
        
        val capsuleWidth = if (isCollapsed) dpToPx(WAVEFORM_WIDTH_DP) else dpToPx(CAPSULE_WIDTH_DP)
        val capsuleHeight = dpToPx(CAPSULE_HEIGHT_DP)
        
        val safeX = if (isCollapsed) {
            // 收缩状态：固定在右侧边缘
            displayMetrics.widthPixels - dpToPx(WAVEFORM_WIDTH_DP)
        } else {
            // 正常状态：应用边界限制
            maxOf(safeMargin, minOf(displayMetrics.widthPixels - capsuleWidth, x))
        }
        
        val safeY = maxOf(
            safeMargin + topMargin,
            minOf(displayMetrics.heightPixels - capsuleHeight - safeMargin - bottomMargin, y)
        )
        
        return CapsulePosition(
            x = safeX,
            y = safeY,
            isCollapsed = isCollapsed,
            timestamp = System.currentTimeMillis()
        )
    }
    
    /**
     * 检查是否需要收缩胶囊
     * 基于当前位置判断是否触发收缩状态
     */
    fun shouldCollapse(x: Int): Boolean {
        val capsuleWidth = dpToPx(CAPSULE_WIDTH_DP)
        val threshold = displayMetrics.widthPixels - capsuleWidth + dpToPx(30) // 提高灵敏度
        return x > threshold
    }
    
    /**
     * 重置为默认位置
     */
    fun resetToDefault() {
        val defaultX = displayMetrics.widthPixels - dpToPx(CAPSULE_WIDTH_DP)
        val defaultY = (displayMetrics.heightPixels * 0.8f).toInt() - dpToPx(CAPSULE_HEIGHT_DP)
        
        updatePosition(defaultX, defaultY, false)
    }
    
    /**
     * 清除所有位置数据
     */
    fun clearPosition() {
        prefs.edit {
            clear()
        }
    }
    
    /**
     * DP转像素辅助函数 
     */
    private fun dpToPx(dp: Int): Int {
        return (dp * displayMetrics.density + 0.5f).toInt()
    }
    
    /**
     * 获取调试信息
     */
    fun getDebugInfo(): String {
        val position = getCurrentPosition()
        return """
            CapsulePositionManager Debug Info:
            - Position: (${position.x}, ${position.y})
            - Collapsed: ${position.isCollapsed}
            - Last Update: ${position.timestamp}
            - Screen Size: ${displayMetrics.widthPixels} x ${displayMetrics.heightPixels}
            - Density: ${displayMetrics.density}
        """.trimIndent()
    }
}