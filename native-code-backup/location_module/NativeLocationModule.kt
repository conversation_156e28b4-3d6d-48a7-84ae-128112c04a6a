package com.gzai168.zipco.location

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Criteria
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Bundle
import android.os.Looper
import androidx.core.app.ActivityCompat
import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

class NativeLocationModule(reactContext: ReactApplicationContext) : 
    ReactContextBaseJavaModule(reactContext), LocationListener {

    private val locationManager: LocationManager = 
        reactContext.getSystemService(Context.LOCATION_SERVICE) as LocationManager
    
    private var locationCallback: Promise? = null
    private val executor = Executors.newSingleThreadScheduledExecutor()
    
    companion object {
        const val MODULE_NAME = "NativeLocationModule"
        private const val LOCATION_UPDATE_MIN_TIME = 1000L // 1秒
        private const val LOCATION_UPDATE_MIN_DISTANCE = 10f // 10米
        private const val LOCATION_TIMEOUT = 15000L // 15秒超时
    }

    override fun getName(): String = MODULE_NAME

    /**
     * 获取当前位置
     */
    @ReactMethod
    fun getCurrentLocation(options: ReadableMap, promise: Promise) {
        try {
            // 检查权限
            if (!checkLocationPermissions()) {
                promise.reject("PERMISSION_DENIED", "定位权限未授予")
                return
            }

            // 检查定位服务是否开启
            if (!isLocationEnabled()) {
                promise.reject("LOCATION_DISABLED", "定位服务未开启")
                return
            }

            // 保存回调
            locationCallback = promise

            // 获取最佳定位提供者
            val criteria = Criteria().apply {
                accuracy = when (options.getInt("accuracy")) {
                    1 -> Criteria.ACCURACY_LOW
                    2 -> Criteria.ACCURACY_MEDIUM
                    3 -> Criteria.ACCURACY_HIGH
                    else -> Criteria.ACCURACY_COARSE
                }
                isCostAllowed = false
                powerRequirement = Criteria.POWER_LOW
            }

            val provider = locationManager.getBestProvider(criteria, true)
            if (provider == null) {
                // 如果没有最佳提供者，尝试使用可用的提供者
                val availableProvider = getAvailableProvider()
                if (availableProvider == null) {
                    promise.reject("NO_PROVIDER", "没有可用的定位提供者")
                    return
                }
                requestLocationWithProvider(availableProvider, options)
            } else {
                requestLocationWithProvider(provider, options)
            }

            // 设置超时
            val timeout = options.getInt("timeout").toLong()
            executor.schedule({
                if (locationCallback != null) {
                    locationManager.removeUpdates(this)
                    locationCallback?.reject("TIMEOUT", "定位超时")
                    locationCallback = null
                }
            }, timeout, TimeUnit.MILLISECONDS)

        } catch (e: Exception) {
            promise.reject("ERROR", "获取位置失败: ${e.message}", e)
        }
    }

    /**
     * 获取最后已知位置
     */
    @ReactMethod
    fun getLastKnownLocation(promise: Promise) {
        try {
            if (!checkLocationPermissions()) {
                promise.reject("PERMISSION_DENIED", "定位权限未授予")
                return
            }

            var lastLocation: Location? = null
            val providers = listOf(
                LocationManager.GPS_PROVIDER,
                LocationManager.NETWORK_PROVIDER,
                LocationManager.PASSIVE_PROVIDER
            )

            // 从所有提供者中找到最新的位置
            for (provider in providers) {
                try {
                    if (locationManager.isProviderEnabled(provider)) {
                        val location = locationManager.getLastKnownLocation(provider)
                        if (location != null) {
                            if (lastLocation == null || location.time > lastLocation.time) {
                                lastLocation = location
                            }
                        }
                    }
                } catch (e: SecurityException) {
                    // 忽略单个提供者的权限错误
                }
            }

            if (lastLocation != null) {
                promise.resolve(createLocationMap(lastLocation))
            } else {
                promise.reject("NO_LOCATION", "没有最后已知位置")
            }
        } catch (e: Exception) {
            promise.reject("ERROR", "获取最后已知位置失败: ${e.message}", e)
        }
    }

    /**
     * 检查定位服务是否可用
     */
    @ReactMethod
    fun hasServicesEnabled(promise: Promise) {
        promise.resolve(isLocationEnabled())
    }

    /**
     * 检查定位权限
     */
    @ReactMethod
    fun hasLocationPermissions(promise: Promise) {
        promise.resolve(checkLocationPermissions())
    }

    // LocationListener 回调
    override fun onLocationChanged(location: Location) {
        locationCallback?.let {
            locationManager.removeUpdates(this)
            it.resolve(createLocationMap(location))
            locationCallback = null
        }
    }

    override fun onStatusChanged(provider: String?, status: Int, extras: Bundle?) {}
    override fun onProviderEnabled(provider: String) {}
    override fun onProviderDisabled(provider: String) {}

    // 私有辅助方法
    private fun checkLocationPermissions(): Boolean {
        return ActivityCompat.checkSelfPermission(
            reactApplicationContext,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED ||
        ActivityCompat.checkSelfPermission(
            reactApplicationContext,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun isLocationEnabled(): Boolean {
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) ||
               locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
    }

    private fun getAvailableProvider(): String? {
        val providers = listOf(
            LocationManager.NETWORK_PROVIDER,
            LocationManager.GPS_PROVIDER,
            LocationManager.PASSIVE_PROVIDER
        )
        
        for (provider in providers) {
            if (locationManager.isProviderEnabled(provider)) {
                return provider
            }
        }
        return null
    }

    private fun requestLocationWithProvider(provider: String, options: ReadableMap) {
        try {
            // 先尝试获取最后已知位置
            val lastLocation = locationManager.getLastKnownLocation(provider)
            val maxAge = options.getInt("maximumAge").toLong()
            
            if (lastLocation != null && 
                (System.currentTimeMillis() - lastLocation.time) < maxAge) {
                // 如果最后已知位置在允许的时间范围内，直接返回
                locationCallback?.resolve(createLocationMap(lastLocation))
                locationCallback = null
                return
            }

            // 请求新的位置更新
            locationManager.requestLocationUpdates(
                provider,
                LOCATION_UPDATE_MIN_TIME,
                LOCATION_UPDATE_MIN_DISTANCE,
                this,
                Looper.getMainLooper()
            )
        } catch (e: SecurityException) {
            locationCallback?.reject("PERMISSION_DENIED", "定位权限被拒绝")
            locationCallback = null
        }
    }

    private fun createLocationMap(location: Location): WritableMap {
        val coords = Arguments.createMap().apply {
            putDouble("latitude", location.latitude)
            putDouble("longitude", location.longitude)
            putDouble("altitude", location.altitude)
            putDouble("accuracy", location.accuracy.toDouble())
            putDouble("heading", location.bearing.toDouble())
            putDouble("speed", location.speed.toDouble())
        }

        return Arguments.createMap().apply {
            putMap("coords", coords)
            putDouble("timestamp", location.time.toDouble())
        }
    }
}
