package com.gzai168.zipco.audio

import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.util.Log
import java.util.concurrent.atomic.AtomicBoolean

class AudioStreamManager {
    private var audioRecord: AudioRecord? = null
    private val isRecording = AtomicBoolean(false)
    private var recordingThread: Thread? = null
    private val TAG = "AudioStreamManager"
    
    fun startRecording(
        sampleRate: Int = 16000,
        interval: Int = 40, // 📋 讯飞文档要求：每40ms发送1280字节
        onAudioData: (ByteArray) -> Unit,
        onError: (String) -> Unit
    ) {
        try {
            Log.d(TAG, "开始初始化音频录制: sampleRate=$sampleRate, interval=$interval")
            
            // 停止之前的录音（如果有）
            stopRecording()
            
            // 🔧 计算合适的缓冲区大小 - 确保足够大，避免读取返回0
            val minBufferSize = AudioRecord.getMinBufferSize(
                sampleRate,
                AudioFormat.CHANNEL_IN_MONO,
                AudioFormat.ENCODING_PCM_16BIT
            )
            
            if (minBufferSize == AudioRecord.ERROR || minBufferSize == AudioRecord.ERROR_BAD_VALUE) {
                throw Exception("无法获取有效的缓冲区大小")
            }
            
            // 使用4倍最小缓冲区大小，确保充足的音频数据缓冲
            val bufferSize = minBufferSize * 4
            
            Log.d(TAG, "最小缓冲区: $minBufferSize bytes, 实际使用: $bufferSize bytes")
            
            // 创建AudioRecord实例 - 使用充足的缓冲区避免数据中断
            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,        // 麦克风输入
                sampleRate,                           // 16kHz采样率
                AudioFormat.CHANNEL_IN_MONO,          // 单声道
                AudioFormat.ENCODING_PCM_16BIT,       // 16位PCM编码
                bufferSize                            // 使用4倍缓冲区，确保连续数据
            )
            
            // 检查AudioRecord状态
            if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                throw Exception("AudioRecord初始化失败")
            }
            
            // 开始录音
            audioRecord?.startRecording()
            
            if (audioRecord?.recordingState != AudioRecord.RECORDSTATE_RECORDING) {
                throw Exception("AudioRecord启动录音失败")
            }
            
            isRecording.set(true)
            Log.d(TAG, "AudioRecord成功启动")
            
            // 启动录音线程
            recordingThread = Thread({
                recordingLoop(bufferSize, interval, onAudioData, onError)
            }, "AudioRecordingThread")
            
            recordingThread?.start()
            Log.d(TAG, "录音线程已启动")
            
        } catch (e: Exception) {
            Log.e(TAG, "启动录音失败", e)
            cleanupResources()
            onError("启动录音失败: ${e.message}")
        }
    }
    
    private fun recordingLoop(
        bufferSize: Int,
        interval: Int,
        onAudioData: (ByteArray) -> Unit,
        onError: (String) -> Unit
    ) {
        try {
            // 🔧 使用与AudioRecord一致的缓冲区大小
            val buffer = ByteArray(bufferSize)
            var audioDataBuffer = mutableListOf<Byte>()
            
            Log.d(TAG, "开始录音循环，缓冲区: ${bufferSize}字节，间隔: ${interval}ms")
            
            while (isRecording.get() && !Thread.currentThread().isInterrupted) {
                try {
                    val bytesRead = audioRecord?.read(buffer, 0, bufferSize) ?: 0
                    
                    if (bytesRead > 0) {
                        // 累积真实的PCM音频数据
                        for (i in 0 until bytesRead) {
                            audioDataBuffer.add(buffer[i])
                        }
                        Log.d(TAG, "✅ 读取到真实音频数据: $bytesRead bytes")
                    } else if (bytesRead == 0) {
                        // 🚨 不再填充人工静音！AudioRecord应该持续有数据
                        Log.w(TAG, "⚠️ AudioRecord读取返回0，等待真实音频数据...")
                        // 短暂等待后继续，不发送人工数据
                        Thread.sleep(5) // 短暂等待
                        continue
                    } else {
                        Log.e(TAG, "❌ AudioRecord读取错误: $bytesRead")
                        break
                    }
                    
                    // 📋 按讯飞文档：只有真实音频数据才发送1280字节包
                    while (audioDataBuffer.size >= 1280) {
                        val pcmBytes = audioDataBuffer.take(1280).toByteArray()
                        
                        // 从缓冲区移除已发送的数据
                        repeat(1280) {
                            if (audioDataBuffer.isNotEmpty()) {
                                audioDataBuffer.removeAt(0)
                            }
                        }
                        
                        Log.d(TAG, "📤 发送真实PCM音频: ${pcmBytes.size} bytes")
                        
                        // 只发送真实的音频数据
                        onAudioData(pcmBytes)
                    }
                    
                    // 按40ms间隔执行
                    Thread.sleep(interval.toLong())
                    
                } catch (e: Exception) {
                    if (isRecording.get()) {
                        Log.e(TAG, "录音循环中发生错误", e)
                        onError("录音过程中发生错误: ${e.message}")
                        break
                    }
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "录音循环异常", e)
            onError("录音循环异常: ${e.message}")
        } finally {
            Log.d(TAG, "录音循环结束")
        }
    }
    
    fun stopRecording() {
        try {
            Log.d(TAG, "停止录音")
            isRecording.set(false)
            
            // 中断并等待录音线程结束
            recordingThread?.interrupt()
            recordingThread?.join(1000) // 最多等待1秒
            recordingThread = null
            
            cleanupResources()
            Log.d(TAG, "录音已停止")
            
        } catch (e: Exception) {
            Log.e(TAG, "停止录音时发生错误", e)
        }
    }
    
    fun isRecording(): Boolean = isRecording.get()
    
    fun release() {
        Log.d(TAG, "释放音频资源")
        stopRecording()
    }
    
    private fun cleanupResources() {
        try {
            audioRecord?.let { record ->
                if (record.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                    record.stop()
                }
                if (record.state == AudioRecord.STATE_INITIALIZED) {
                    record.release()
                }
            }
            audioRecord = null
            Log.d(TAG, "音频资源已清理")
            
        } catch (e: Exception) {
            Log.e(TAG, "清理音频资源时发生错误", e)
        }
    }
} 