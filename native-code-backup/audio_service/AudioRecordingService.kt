package com.gzai168.zipco.audio

import android.app.Application
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Binder
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.core.app.NotificationCompat
import com.gzai168.zipco.MainActivity
import com.gzai168.zipco.R
import com.gzai168.zipco.floating.CapsuleCoordinator
import com.gzai168.zipco.floating.FloatingWindowData

/**
 * 前台录音服务
 * 确保应用在后台时仍能持续录音
 */
class AudioRecordingService : Service() {
    
    private val tag = "AudioRecordingService"
    private val binder = AudioRecordingBinder()
    
    // 服务状态
    private var isRecording = false
    private var recordingDuration = 0
    private var isPaused = false
    
    // 智能双重更新机制
    private val handler = Handler(Looper.getMainLooper())
    private var updateRunnable: Runnable? = null
    private var lastRNUpdateTime = 0L
    private var isNativeTimerActive = false
    private var capsuleCoordinator: CapsuleCoordinator? = null
    
    // 更新检测参数
    private val RN_UPDATE_TIMEOUT = 2000L // RN更新超时时间：2秒（缩短检测时间）
    private val UPDATE_INTERVAL = 1000L   // 原生定时器间隔：1秒
    
    companion object {
        const val CHANNEL_ID = "AudioRecordingChannel"
        const val NOTIFICATION_ID = 1001
        
        // 服务动作
        const val ACTION_START_RECORDING = "com.gzai168.zipco.START_RECORDING"
        const val ACTION_STOP_RECORDING = "com.gzai168.zipco.STOP_RECORDING"
        const val ACTION_UPDATE_RECORDING = "com.gzai168.zipco.UPDATE_RECORDING"
        
        // Intent额外数据
        const val EXTRA_DURATION = "duration"
        const val EXTRA_IS_RECORDING = "is_recording"
        const val EXTRA_IS_PAUSED = "is_paused"
    }
    
    inner class AudioRecordingBinder : Binder() {
        fun getService(): AudioRecordingService = this@AudioRecordingService
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(tag, "🎤 AudioRecordingService创建")
        
        // 获取CapsuleCoordinator实例
        try {
            val application = applicationContext as Application
            capsuleCoordinator = CapsuleCoordinator.getInstance(application)
            Log.d(tag, "✅ CapsuleCoordinator引用获取成功")
        } catch (e: Exception) {
            Log.e(tag, "❌ 获取CapsuleCoordinator失败", e)
        }
        
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(tag, "onStartCommand: action=${intent?.action}")
        
        when (intent?.action) {
            ACTION_START_RECORDING -> {
                val duration = intent.getIntExtra(EXTRA_DURATION, 0)
                val isRecording = intent.getBooleanExtra(EXTRA_IS_RECORDING, true)
                val isPaused = intent.getBooleanExtra(EXTRA_IS_PAUSED, false)
                startRecording(duration, isRecording, isPaused)
            }
            ACTION_STOP_RECORDING -> {
                stopRecording()
            }
            ACTION_UPDATE_RECORDING -> {
                val duration = intent.getIntExtra(EXTRA_DURATION, 0)
                val isRecording = intent.getBooleanExtra(EXTRA_IS_RECORDING, true)
                val isPaused = intent.getBooleanExtra(EXTRA_IS_PAUSED, false)
                updateRecording(duration, isRecording, isPaused)
            }
        }
        
        // 服务重启时自动恢复
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder {
        Log.d(tag, "服务绑定")
        return binder
    }
    
    override fun onDestroy() {
        Log.d(tag, "💀 AudioRecordingService销毁")
        
        // 清理所有定时器资源
        stopNativeTimer()
        handler.removeCallbacks(backupCheckRunnable)
        handler.removeCallbacksAndMessages(null)
        
        // 停止录音
        stopRecording()
        
        Log.d(tag, "✅ AudioRecordingService资源清理完成")
        super.onDestroy()
    }
    
    /**
     * 开始录音
     */
    private fun startRecording(duration: Int, isRecording: Boolean, isPaused: Boolean) {
        Log.d(tag, "🎤 开始前台录音服务 - duration: $duration, isRecording: $isRecording, isPaused: $isPaused")
        
        this.isRecording = isRecording
        this.recordingDuration = duration
        this.isPaused = isPaused
        
        // 初始化智能双重更新机制
        lastRNUpdateTime = System.currentTimeMillis()
        scheduleBackupCheck() // 开始检测RN更新
        
        // 启动前台服务
        val notification = createRecordingNotification(duration, isRecording, isPaused)
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(
                NOTIFICATION_ID, 
                notification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE
            )
        } else {
            startForeground(NOTIFICATION_ID, notification)
        }
        
        Log.d(tag, "前台录音服务已启动")
    }
    
    /**
     * 更新录音状态（支持双重更新机制）
     */
    private fun updateRecording(duration: Int, isRecording: Boolean, isPaused: Boolean) {
        Log.d(tag, "📞 收到录音状态更新 - duration: ${duration}s, isRecording: $isRecording, isPaused: $isPaused")
        
        // 记录RN更新时间
        onReceiveRNUpdate(duration, isRecording, isPaused)
        
        // 更新状态
        this.isRecording = isRecording
        this.recordingDuration = duration
        this.isPaused = isPaused
        
        // 统一更新UI
        updateUI()
        
        Log.d(tag, "✅ 录音状态更新完成")
    }
    
    /**
     * 处理RN更新（智能双重更新机制的核心）
     */
    private fun onReceiveRNUpdate(duration: Int, isRecording: Boolean, isPaused: Boolean) {
        Log.d(tag, "🔄 RN更新检测 - duration: ${duration}s, 上次更新: ${System.currentTimeMillis() - lastRNUpdateTime}ms前")
        
        // 更新最后RN更新时间
        lastRNUpdateTime = System.currentTimeMillis()
        
        // 如果原生定时器在运行，停止它（RN更新恢复正常）
        if (isNativeTimerActive) {
            Log.d(tag, "✅ RN更新恢复，停止原生定时器备用")
            stopNativeTimer()
        }
        
        // 启动备用检测定时器（3秒后检测是否需要启用原生备用）
        scheduleBackupCheck()
    }
    
    /**
     * 启动备用检测定时器
     */
    private fun scheduleBackupCheck() {
        // 清除之前的检测
        handler.removeCallbacks(backupCheckRunnable)
        
        // 3秒后检测RN更新是否停止
        handler.postDelayed(backupCheckRunnable, RN_UPDATE_TIMEOUT)
    }
    
    // 备用检测任务
    private val backupCheckRunnable = Runnable {
        val timeSinceLastUpdate = System.currentTimeMillis() - lastRNUpdateTime
        if (timeSinceLastUpdate >= RN_UPDATE_TIMEOUT && isRecording && !isPaused && !isNativeTimerActive) {
            Log.w(tag, "⚠️ 检测到RN更新停止${timeSinceLastUpdate}ms，启用原生定时器备用")
            startNativeTimer()
        } else {
            Log.d(tag, "🔍 备用检测 - RN更新正常，距上次: ${timeSinceLastUpdate}ms")
        }
    }
    
    /**
     * 启动原生定时器
     */
    private fun startNativeTimer() {
        if (isNativeTimerActive) {
            Log.d(tag, "⚠️ 原生定时器已在运行")
            return
        }
        
        Log.d(tag, "🚀 启动原生定时器备用机制 - 当前时长: ${recordingDuration}s")
        isNativeTimerActive = true
        
        updateRunnable = object : Runnable {
            private var lastUpdateTime = System.currentTimeMillis()
            
            override fun run() {
                if (isNativeTimerActive && isRecording && !isPaused) {
                    val currentTime = System.currentTimeMillis()
                    val actualInterval = currentTime - lastUpdateTime
                    lastUpdateTime = currentTime
                    
                    // 增加计时
                    recordingDuration++
                    Log.d(tag, "⏱️ 原生定时器更新 - duration: ${recordingDuration}s, 实际间隔: ${actualInterval}ms")
                    
                    // 更新UI
                    updateUI()
                    
                    // 同时更新胶囊（通过CapsuleCoordinator）
                    updateCapsuleFromService()
                    
                    // 精确延迟1秒后继续
                    handler.postDelayed(this, UPDATE_INTERVAL)
                } else {
                    Log.d(tag, "⏹️ 原生定时器停止条件触发 - active: $isNativeTimerActive, recording: $isRecording, paused: $isPaused")
                    isNativeTimerActive = false
                }
            }
        }
        
        // 延迟1秒后开始第一次更新，确保精确计时
        handler.postDelayed(updateRunnable!!, UPDATE_INTERVAL)
    }
    
    /**
     * 停止原生定时器
     */
    private fun stopNativeTimer() {
        if (isNativeTimerActive) {
            Log.d(tag, "⏹️ 停止原生定时器")
            isNativeTimerActive = false
            updateRunnable?.let { handler.removeCallbacks(it) }
            updateRunnable = null
        }
    }
    
    /**
     * 统一UI更新方法
     */
    private fun updateUI() {
        // 更新通知栏
        updateNotification()
    }
    
    /**
     * 更新通知栏
     */
    private fun updateNotification() {
        val notification = createRecordingNotification(recordingDuration, isRecording, isPaused)
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
    
    /**
     * 通过服务直接更新桌面胶囊（绕过可能的循环调用）
     */
    private fun updateCapsuleFromService() {
        try {
            Log.d(tag, "🔄 原生定时器更新桌面胶囊 - duration: ${recordingDuration}s")
            
            // 先检查CapsuleCoordinator是否可用
            if (capsuleCoordinator == null) {
                Log.w(tag, "⚠️ CapsuleCoordinator为null，跳过桌面胶囊更新")
                return
            }
            
            // 检查是否在后台且原生胶囊正在显示  
            if (!capsuleCoordinator!!.isAppInForeground() && capsuleCoordinator!!.isNativeCapsuleShowing()) {
                Log.d(tag, "📱 APP在后台且原生胶囊显示中，直接更新FloatingWindowManager")
                
                val floatingWindowManager = capsuleCoordinator!!.getFloatingWindowManager()
                if (floatingWindowManager != null) {
                    val data = FloatingWindowData(recordingDuration, isRecording, isPaused)
                    floatingWindowManager.updateFloatingWindow(data)
                    Log.d(tag, "✅ 桌面胶囊直接更新成功")
                } else {
                    Log.w(tag, "⚠️ FloatingWindowManager为null，无法更新桌面胶囊")
                }
            } else {
                Log.d(tag, "📱 APP在前台或原生胶囊未显示，跳过直接更新")
            }
            
        } catch (e: Exception) {
            Log.e(tag, "❌ 桌面胶囊更新失败", e)
        }
    }
    
    /**
     * 停止录音
     */
    private fun stopRecording() {
        Log.d(tag, "🛑 停止前台录音服务")
        
        // 清理定时器资源
        stopNativeTimer()
        handler.removeCallbacks(backupCheckRunnable)
        
        isRecording = false
        recordingDuration = 0
        isPaused = false
        lastRNUpdateTime = 0L
        
        // 停止前台服务
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
        
        Log.d(tag, "✅ 前台录音服务已完全停止")
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "录音服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "后台录音功能通知"
                setSound(null, null) // 禁用声音
                enableVibration(false) // 禁用震动
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            
            Log.d(tag, "通知渠道已创建")
        }
    }
    
    /**
     * 创建录音通知
     */
    private fun createRecordingNotification(duration: Int, isRecording: Boolean, isPaused: Boolean): Notification {
        // 创建点击通知时的意图
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            } else {
                PendingIntent.FLAG_UPDATE_CURRENT
            }
        )
        
        // 格式化录音时长
        val mins = duration / 60
        val secs = duration % 60
        val timeStr = String.format("%02d:%02d", mins, secs)
        
        // 根据状态设置文本
        val statusText = when {
            isPaused -> "录音已暂停"
            isRecording -> "正在录音"
            else -> "准备录音"
        }
        
        val title = "语音助手 - $statusText"
        val content = "录音时长: $timeStr - 点击返回应用"
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_notification) // 需要添加通知图标
            .setContentIntent(pendingIntent)
            .setOngoing(true) // 不可滑动删除
            .setSilent(true) // 静默通知
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE)
            .build()
    }
    
    /**
     * 获取当前录音状态
     */
    fun getCurrentRecordingState(): RecordingState {
        return RecordingState(isRecording, recordingDuration)
    }
    
    /**
     * 录音状态数据类
     */
    data class RecordingState(
        val isRecording: Boolean,
        val duration: Int
    )
}