import React, {
  createContext,
  useContext,
  useState,
  useRef,
  useCallback,
  ReactNode,
} from 'react';

interface RecordingContextType {
  // 胶囊状态
  isRecordingCapsuleVisible: boolean;
  setIsRecordingCapsuleVisible: (visible: boolean) => void;

  // 录音状态
  isRecording: boolean;
  setIsRecording: (recording: boolean) => void;

  isPaused: boolean;
  setIsPaused: (paused: boolean) => void;

  recordingDuration: number;
  setRecordingDuration: (duration: number) => void;

  // 胶囊操作回调 - 使用函数形式避免直接暴露ref
  callCapsulePress: () => void;
  setOnCapsulePress: (callback: (() => void) | undefined) => void;

  callCapsuleStop: () => void;
  setOnCapsuleStop: (callback: (() => void) | undefined) => void;
}

const RecordingContext = createContext<RecordingContextType | undefined>(
  undefined
);

interface RecordingProviderProps {
  children: ReactNode;
}

export const RecordingProvider: React.FC<RecordingProviderProps> = ({
  children,
}) => {
  const [isRecordingCapsuleVisible, setIsRecordingCapsuleVisible] =
    useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);

  // 使用useRef存储回调函数，避免无限循环
  const onCapsulePressRef = useRef<(() => void) | undefined>();
  const onCapsuleStopRef = useRef<(() => void) | undefined>();

  const setOnCapsulePress = useCallback(
    (callback: (() => void) | undefined) => {
      onCapsulePressRef.current = callback;
    },
    []
  );

  const setOnCapsuleStop = useCallback((callback: (() => void) | undefined) => {
    onCapsuleStopRef.current = callback;
  }, []);

  // 稳定的回调函数，避免Context value不断变化
  const callCapsulePress = useCallback(() => {
    if (onCapsulePressRef.current) {
      onCapsulePressRef.current();
    }
  }, []);

  const callCapsuleStop = useCallback(() => {
    if (onCapsuleStopRef.current) {
      onCapsuleStopRef.current();
    }
  }, []);

  // 使用useMemo优化Context value，避免不必要的重新渲染
  const value = React.useMemo<RecordingContextType>(
    () => ({
      isRecordingCapsuleVisible,
      setIsRecordingCapsuleVisible,
      isRecording,
      setIsRecording,
      isPaused,
      setIsPaused,
      recordingDuration,
      setRecordingDuration,
      callCapsulePress,
      setOnCapsulePress,
      callCapsuleStop,
      setOnCapsuleStop,
    }),
    [
      isRecordingCapsuleVisible,
      isRecording,
      isPaused,
      recordingDuration,
      callCapsulePress,
      setOnCapsulePress,
      callCapsuleStop,
      setOnCapsuleStop,
    ]
  );

  return (
    <RecordingContext.Provider value={value}>
      {children}
    </RecordingContext.Provider>
  );
};

export const useRecordingContext = () => {
  const context = useContext(RecordingContext);
  if (context === undefined) {
    throw new Error(
      'useRecordingContext must be used within a RecordingProvider'
    );
  }
  return context;
};
