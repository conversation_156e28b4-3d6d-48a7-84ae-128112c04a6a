/**
 * 统一的Markdown样式配置
 *
 * 这个文件定义了应用中所有Markdown渲染的基础样式，确保视觉一致性。
 *
 * 设计原则：
 * 1. 统一的字体、颜色和间距规范
 * 2. 清晰的标题层级（H1-H6）
 * 3. 明显的粗体和斜体效果
 * 4. 适当的列表和段落间距
 * 5. 支持特殊场景的样式覆盖
 *
 * 使用方式：
 * ```typescript
 * import { baseMarkdownStyles, createMarkdownStyles } from '@/styles/markdownStyles';
 *
 * // 使用基础样式
 * <Markdown style={baseMarkdownStyles}>{content}</Markdown>
 *
 * // 使用自定义变体
 * const customStyles = createMarkdownStyles({ variant: 'compact' });
 * <Markdown style={customStyles}>{content}</Markdown>
 *
 * // 覆盖特定样式
 * const overrideStyles = {
 *   ...baseMarkdownStyles,
 *   heading1: { ...baseMarkdownStyles.heading1, fontSize: 24 }
 * };
 * ```
 */

import { colors, typography } from './index';

/**
 * 基础Markdown样式配置
 * 这是所有Markdown渲染的默认样式，遵循应用的设计系统
 */
export const baseMarkdownStyles = {
  // 文档主体
  body: {
    fontSize: 14,
    color: colors.text.primary,
    lineHeight: 20,
    fontFamily: typography.fontFamily,
  },

  // 标题层级 - 采用渐进式字重和字号设计
  heading1: {
    fontSize: 18,
    fontWeight: typography.fontWeight.bold, // 700 - 最重要的标题
    color: colors.text.primary,
    marginBottom: 8,
    marginTop: 12,
    fontFamily: typography.fontFamily,
  },
  heading2: {
    fontSize: 16,
    fontWeight: typography.fontWeight.bold, // 700 - 次级标题
    color: colors.text.primary,
    marginBottom: 6,
    marginTop: 10,
    fontFamily: typography.fontFamily,
  },
  heading3: {
    fontSize: 15,
    fontWeight: typography.fontWeight.semibold, // 600 - 三级标题
    color: colors.text.primary,
    marginBottom: 4,
    marginTop: 8,
    fontFamily: typography.fontFamily,
  },
  heading4: {
    fontSize: 14,
    fontWeight: typography.fontWeight.semibold, // 600
    color: colors.text.primary,
    marginBottom: 3,
    marginTop: 6,
    fontFamily: typography.fontFamily,
  },
  heading5: {
    fontSize: 13,
    fontWeight: typography.fontWeight.semibold, // 600
    color: colors.text.primary,
    marginBottom: 2,
    marginTop: 4,
    fontFamily: typography.fontFamily,
  },
  heading6: {
    fontSize: 12,
    fontWeight: typography.fontWeight.semibold, // 600
    color: colors.text.primary,
    marginBottom: 2,
    marginTop: 4,
    fontFamily: typography.fontFamily,
  },

  // 段落和文本格式
  paragraph: {
    fontSize: 14,
    color: colors.text.primary,
    lineHeight: 20,
    marginBottom: 6,
    fontFamily: typography.fontFamily,
  },
  strong: {
    fontWeight: typography.fontWeight.bold, // 700 - 明显的粗体效果
    color: colors.text.primary,
  },
  em: {
    fontStyle: 'italic' as const,
    color: colors.text.primary,
  },
  s: {
    textDecorationLine: 'line-through' as const,
    color: colors.text.secondary, // 删除线使用次要颜色
  },

  // 列表样式
  list_item: {
    fontSize: 14,
    color: colors.text.primary,
    lineHeight: 20,
    marginBottom: 2,
    fontFamily: typography.fontFamily,
  },
  bullet_list: {
    marginBottom: 6,
  },
  ordered_list: {
    marginBottom: 6,
  },

  // 代码样式
  code_inline: {
    fontSize: 13,
    backgroundColor: colors.background.secondary,
    color: colors.text.primary,
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    fontFamily: 'Menlo',
  },

  // 水平分割线
  hr: {
    backgroundColor: colors.border,
    height: 1,
    marginVertical: 12,
  },

  // 表格样式
  table: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 6,
    marginVertical: 6,
  },
  th: {
    backgroundColor: colors.background.secondary,
    padding: 8,
    fontWeight: typography.fontWeight.semibold,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  td: {
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
};

/**
 * 样式变体类型定义
 */
export type MarkdownStyleVariant = 'default' | 'compact' | 'large' | 'chat';

/**
 * 样式覆盖选项
 */
export interface MarkdownStyleOptions {
  variant?: MarkdownStyleVariant;
  textColor?: string;
  fontSize?: number;
  lineHeight?: number;
}

/**
 * 创建自定义Markdown样式
 * 基于基础样式，根据不同场景需求生成定制化样式
 *
 * @param options 样式选项
 * @returns 定制化的Markdown样式对象
 */
export const createMarkdownStyles = (options: MarkdownStyleOptions = {}) => {
  const {
    variant = 'default',
    textColor = colors.text.primary,
    fontSize = 14,
    lineHeight = 20,
  } = options;

  // 基础样式副本
  const customStyles = { ...baseMarkdownStyles };

  // 根据变体调整样式
  switch (variant) {
    case 'compact':
      // 紧凑样式 - 减少间距，适用于摘要等场景
      return {
        ...customStyles,
        body: { ...customStyles.body, lineHeight: 18 },
        paragraph: {
          ...customStyles.paragraph,
          lineHeight: 18,
          marginBottom: 4,
        },
        list_item: {
          ...customStyles.list_item,
          lineHeight: 18,
          marginBottom: 1,
        },
        heading1: { ...customStyles.heading1, marginBottom: 6, marginTop: 8 },
        heading2: { ...customStyles.heading2, marginBottom: 4, marginTop: 6 },
        heading3: { ...customStyles.heading3, marginBottom: 3, marginTop: 5 },
      };

    case 'large':
      // 大字体样式 - 增加字号，适用于阅读场景
      return {
        ...customStyles,
        body: {
          ...customStyles.body,
          fontSize: fontSize + 2,
          lineHeight: lineHeight + 4,
        },
        paragraph: {
          ...customStyles.paragraph,
          fontSize: fontSize + 2,
          lineHeight: lineHeight + 4,
        },
        heading1: { ...customStyles.heading1, fontSize: 22 },
        heading2: { ...customStyles.heading2, fontSize: 20 },
        heading3: { ...customStyles.heading3, fontSize: 18 },
      };

    case 'chat':
      // 聊天样式 - 适用于对话场景
      return {
        ...customStyles,
        body: { ...customStyles.body, color: textColor },
        paragraph: { ...customStyles.paragraph, color: textColor },
        strong: { ...customStyles.strong, color: textColor },
        em: { ...customStyles.em, color: textColor },
        list_item: { ...customStyles.list_item, color: textColor },
      };

    default:
      return customStyles;
  }
};

/**
 * 预定义的样式变体
 * 为常见使用场景提供快捷方式
 */
export const markdownStyleVariants = {
  // 默认样式
  default: baseMarkdownStyles,

  // 紧凑样式 - 适用于摘要、预览等
  compact: createMarkdownStyles({ variant: 'compact' }),

  // 大字体样式 - 适用于文档阅读
  large: createMarkdownStyles({ variant: 'large' }),

  // 聊天样式 - 适用于对话场景
  chat: createMarkdownStyles({ variant: 'chat' }),
};
