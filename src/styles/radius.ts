/**
 * 设计系统 - 圆角规范
 * 遵循渐进式圆角设计，营造友好现代感
 */
export const radius = {
  // 基础圆角
  none: 0,
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,

  // 特定组件圆角
  button: 12,
  card: 16,
  modal: 20,
  pill: 999, // 胶囊形状

  // 录音相关组件
  recordingButton: 60, // 录音按钮（圆形）
  recordingCard: 16,
  waveform: 12,

  // AI功能相关
  aiCard: 16,
  chatBubble: 18,

  // 状态指示器
  statusPill: 12,
  badge: 8,
} as const;

export type RadiusKey = keyof typeof radius;
