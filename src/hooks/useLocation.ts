import { useState, useEffect } from 'react';
import { Platform } from 'react-native';
import * as Location from 'expo-location';
import Toast from 'react-native-toast-message';
import NativeLocationService from '../services/location/NativeLocationService';

export interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
  timestamp: number;
}

export interface LocationState {
  currentLocation: LocationData | null;
  hasLocationPermission: boolean;
  isLoadingLocation: boolean;
  locationError: string | null;
}

export const useLocation = () => {
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(
    null
  );
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // 请求定位权限
  const requestLocationPermission = async (): Promise<boolean> => {
    try {
      // 先检查当前权限状态
      const { status: currentStatus } =
        await Location.getForegroundPermissionsAsync();

      if (currentStatus === 'granted') {
        setHasLocationPermission(true);
        setLocationError(null);
        return true;
      }

      // 请求权限
      const { status } = await Location.requestForegroundPermissionsAsync();
      const granted = status === 'granted';
      setHasLocationPermission(granted);

      if (!granted) {
        setLocationError('定位权限被拒绝');
        console.warn('Location permission denied, status:', status);
        Toast.show({
          type: 'error',
          text1: '定位权限被拒绝',
          text2: '请在设置中允许应用访问位置信息',
        });
      } else {
        setLocationError(null);
        console.log('Location permission granted');
      }

      return granted;
    } catch (error) {
      console.error('Failed to request location permission:', error);
      setLocationError('请求定位权限失败');
      Toast.show({
        type: 'error',
        text1: '权限请求失败',
        text2: '无法请求定位权限',
      });
      return false;
    }
  };

  // 获取当前位置
  const getCurrentLocation = async (): Promise<LocationData | null> => {
    try {
      setIsLoadingLocation(true);
      setLocationError(null);

      // 检查定位服务是否开启
      const servicesEnabled = await checkLocationServicesEnabled();
      if (!servicesEnabled) {
        return null;
      }

      // 检查权限
      if (!hasLocationPermission) {
        const granted = await requestLocationPermission();
        if (!granted) {
          return null;
        }
      }

      // 获取位置 - 使用更宽松的配置和重试
      let location: Location.LocationObject;
      try {
        console.log('getCurrentLocation: 开始获取位置...');

        // 检查位置服务
        const servicesEnabled = await Location.hasServicesEnabledAsync();
        if (!servicesEnabled) {
          throw new Error('位置服务未开启');
        }

        location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Lowest, // 使用最低精度
          mayShowUserSettingsDialog: true,
        });
      } catch (currentLocationError) {
        console.warn(
          'getCurrentPositionAsync failed, trying getLastKnownPositionAsync:',
          currentLocationError
        );
        // 如果获取当前位置失败，尝试获取最后已知位置
        const lastKnownLocation = await Location.getLastKnownPositionAsync();

        if (!lastKnownLocation) {
          throw new Error('无法获取当前位置或最后已知位置');
        }

        location = lastKnownLocation;
      }

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        timestamp: location.timestamp,
      };

      // 尝试获取地址信息
      try {
        const addresses = await Location.reverseGeocodeAsync({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });

        if (addresses.length > 0) {
          const address = addresses[0];
          // 构建地址字符串
          const addressParts = [
            address.city,
            address.district,
            address.street,
            address.name,
          ].filter(Boolean);

          locationData.address = addressParts.join('');
        }
      } catch (geocodeError) {
        console.warn('Failed to get address:', geocodeError);
        // 地址获取失败不影响位置数据
      }

      setCurrentLocation(locationData);
      return locationData;
    } catch (error) {
      console.error('Failed to get current location:', error);

      let errorMessage = '无法获取当前位置信息';
      if (error instanceof Error) {
        if (error.message.includes('location services')) {
          errorMessage = '请在设备设置中开启定位服务';
        } else if (error.message.includes('permission')) {
          errorMessage = '定位权限被拒绝，请在设置中允许访问位置';
        } else if (error.message.includes('timeout')) {
          errorMessage = '定位超时，请稍后重试';
        }
      }

      setLocationError(errorMessage);
      Toast.show({
        type: 'error',
        text1: '定位失败',
        text2: errorMessage,
      });
      return null;
    } finally {
      setIsLoadingLocation(false);
    }
  };

  // 检查定位服务是否可用
  const checkLocationServicesEnabled = async (): Promise<boolean> => {
    try {
      const enabled = await Location.hasServicesEnabledAsync();
      if (!enabled) {
        Toast.show({
          type: 'error',
          text1: '定位服务未开启',
          text2: '请在设备设置中开启定位服务',
        });
      }
      return enabled;
    } catch (error) {
      console.error('Failed to check location services:', error);
      return false;
    }
  };

  // 格式化地址显示
  const formatLocationForDisplay = (
    location: LocationData | null
  ): string | null => {
    if (!location) return null;

    if (location.address) {
      // 如果地址太长，截取前20个字符
      return location.address.length > 20
        ? `${location.address.substring(0, 20)}...`
        : location.address;
    }

    // 如果没有地址，显示经纬度
    return `${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;
  };

  // 初始化位置信息（应用启动时调用一次）
  const initializeLocation = async () => {
    if (isInitialized) return; // 避免重复初始化

    try {
      setIsInitialized(true); // 立即设置为已初始化，避免重复调用
      setIsLoadingLocation(true);
      console.log('开始初始化位置信息...');

      // 检查权限
      const granted = await requestLocationPermission();
      if (!granted) {
        console.log('位置权限被拒绝，使用模拟位置');
        const mockLocation: LocationData = {
          latitude: 23.1291, // 广州坐标
          longitude: 113.2644,
          address: '广州市巨大产业园',
          timestamp: Date.now(),
        };
        setCurrentLocation(mockLocation);
        return;
      }

      // 尝试获取真实位置（增加重试机制）
      try {
        console.log('开始获取真实位置...');

        // 检查位置服务是否开启
        const servicesEnabled = await Location.hasServicesEnabledAsync();
        console.log('位置服务状态:', servicesEnabled);

        if (!servicesEnabled) {
          throw new Error('位置服务未开启');
        }

        // 添加更详细的调试信息和多种获取方式
        console.log('📍 开始位置获取流程...');

        // 1. 检查设备定位功能状态
        const hasServicesEnabled = await Location.hasServicesEnabledAsync();
        console.log('设备定位服务状态:', hasServicesEnabled);

        // 2. 获取权限详细状态
        const permissionStatus = await Location.getForegroundPermissionsAsync();
        console.log('权限详细状态:', permissionStatus);

        if (permissionStatus.status !== 'granted') {
          throw new Error(`权限状态异常: ${permissionStatus.status}`);
        }

        let location: Location.LocationObject | null = null;

        // 检查是否是 Android 设备且原生定位模块可用
        if (Platform.OS === 'android' && NativeLocationService.isAvailable()) {
          console.log('🔧 使用原生定位模块...');

          try {
            // 策略1: 尝试获取最后已知位置
            console.log('策略1: 尝试原生最后已知位置...');
            const lastNativeLocation =
              await NativeLocationService.getLastKnownLocation();

            if (lastNativeLocation) {
              console.log(
                '✅ 原生最后已知位置获取成功:',
                lastNativeLocation.coords
              );
              location = {
                coords: {
                  latitude: lastNativeLocation.coords.latitude,
                  longitude: lastNativeLocation.coords.longitude,
                  altitude: lastNativeLocation.coords.altitude,
                  accuracy: lastNativeLocation.coords.accuracy,
                  heading: lastNativeLocation.coords.heading,
                  speed: lastNativeLocation.coords.speed,
                  altitudeAccuracy: null,
                },
                timestamp: lastNativeLocation.timestamp,
              };
            }

            // 策略2: 获取当前位置
            if (!location) {
              console.log('策略2: 尝试原生当前位置...');
              const nativeConfigs = [
                { accuracy: 1, timeout: 8000, maximumAge: 300000 }, // 低精度
                { accuracy: 2, timeout: 12000, maximumAge: 60000 }, // 中精度
                { accuracy: 3, timeout: 15000, maximumAge: 30000 }, // 高精度
              ];

              for (const config of nativeConfigs) {
                try {
                  console.log('原生定位配置:', config);
                  const nativeLocation =
                    await NativeLocationService.getCurrentLocation(config);
                  console.log('✅ 原生定位成功:', nativeLocation.coords);

                  location = {
                    coords: {
                      latitude: nativeLocation.coords.latitude,
                      longitude: nativeLocation.coords.longitude,
                      altitude: nativeLocation.coords.altitude,
                      accuracy: nativeLocation.coords.accuracy,
                      heading: nativeLocation.coords.heading,
                      speed: nativeLocation.coords.speed,
                      altitudeAccuracy: null,
                    },
                    timestamp: nativeLocation.timestamp,
                  };
                  break;
                } catch (nativeError: any) {
                  console.warn('原生定位失败:', nativeError);
                }
              }
            }
          } catch (nativeError: any) {
            console.error('原生定位模块错误:', nativeError);
          }
        }

        // 如果原生定位失败或不可用，回退到 Expo Location
        if (!location) {
          console.log('📍 使用 Expo Location API...');

          // 策略1: 尝试获取最后已知位置（最快）
          try {
            console.log('策略1: 尝试获取最后已知位置...');
            const lastLocation = await Location.getLastKnownPositionAsync({
              maxAge: 300000, // 5分钟内的位置都可以接受
              requiredAccuracy: 1000, // 1公里精度即可
            });

            if (lastLocation) {
              console.log('✅ 最后已知位置获取成功:', lastLocation.coords);
              location = lastLocation;
            } else {
              console.log('❌ 没有最后已知位置');
            }
          } catch (lastLocationError) {
            console.warn('获取最后已知位置失败:', lastLocationError);
          }

          // 策略2: 如果没有缓存位置，尝试当前位置（多种配置）
          if (!location) {
            const locationConfigs = [
              {
                name: '最低精度配置',
                options: {
                  accuracy: Location.Accuracy.Lowest,
                  mayShowUserSettingsDialog: false,
                },
              },
              {
                name: '平衡配置',
                options: {
                  accuracy: Location.Accuracy.Low,
                  mayShowUserSettingsDialog: false,
                },
              },
              {
                name: '高精度配置',
                options: {
                  accuracy: Location.Accuracy.Balanced,
                  mayShowUserSettingsDialog: true,
                },
              },
            ];

            for (const config of locationConfigs) {
              try {
                console.log(`策略2: 尝试${config.name}...`);
                console.log('配置参数:', config.options);

                location = await Location.getCurrentPositionAsync(
                  config.options
                );
                console.log(`✅ ${config.name}获取成功:`, location.coords);
                break;
              } catch (configError: any) {
                console.warn(`${config.name}失败:`, {
                  message: configError.message,
                  code: configError.code,
                  stack: configError.stack?.split('\n')[0], // 只记录第一行堆栈
                });
              }
            }
          }

          // 策略3: 最后的尝试 - 强制获取当前位置
          if (!location) {
            try {
              console.log('策略3: 最后尝试 - 强制获取当前位置...');
              location = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.Lowest,
                mayShowUserSettingsDialog: true,
              });
              console.log('✅ 强制获取位置成功:', location.coords);
            } catch (forceError: any) {
              console.error('强制获取位置也失败了:', {
                message: forceError.message,
                code: forceError.code,
                name: forceError.name,
              });
            }
          }
        }

        if (!location) {
          // 详细的错误诊断
          const diagnosticInfo = {
            platform: Platform.OS,
            servicesEnabled: hasServicesEnabled,
            permissionStatus: permissionStatus.status,
            timestamp: new Date().toISOString(),
          };

          console.error('❌ 所有位置获取策略都失败了');
          console.error('诊断信息:', diagnosticInfo);

          throw new Error(
            `位置获取失败 - 平台:${Platform.OS} 服务:${hasServicesEnabled} 权限:${permissionStatus.status}`
          );
        }

        const locationData: LocationData = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          timestamp: location.timestamp,
        };

        // 尝试获取地址信息
        try {
          const reverseGeocode = await Location.reverseGeocodeAsync({
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          });

          if (reverseGeocode.length > 0) {
            const address = reverseGeocode[0];
            locationData.address = [
              address.country,
              address.region,
              address.city,
              address.district,
            ]
              .filter(Boolean)
              .join('');
          }
        } catch (geocodeError) {
          console.warn('地址解析失败:', geocodeError);
        }

        setCurrentLocation(locationData);
        console.log('真实位置初始化成功:', locationData);
      } catch (locationError: any) {
        // 获取真实位置失败，使用模拟位置
        console.error('真实位置获取失败，详细错误:', {
          error: locationError.message,
          name: locationError.name,
          timestamp: new Date().toISOString(),
        });
        console.log('使用模拟位置数据');
        const mockLocation: LocationData = {
          latitude: 23.1291, // 广州坐标
          longitude: 113.2644,
          address: '广州市巨大产业园',
          timestamp: Date.now(),
        };
        setCurrentLocation(mockLocation);
        console.log('模拟位置设置成功:', mockLocation);
      }
    } catch (error) {
      console.warn('位置初始化失败:', error);
      // 即使失败也设置模拟位置
      const mockLocation: LocationData = {
        latitude: 23.1291,
        longitude: 113.2644,
        address: '广州市巨大产业园',
        timestamp: Date.now(),
      };
      setCurrentLocation(mockLocation);
    } finally {
      setIsLoadingLocation(false);
    }
  };

  // 获取缓存的位置信息（用于录音保存）
  const getCachedLocation = (): LocationData | null => {
    return currentLocation;
  };

  // 初始化时检查权限状态
  useEffect(() => {
    const checkInitialPermission = async () => {
      try {
        const { status } = await Location.getForegroundPermissionsAsync();
        setHasLocationPermission(status === 'granted');
      } catch (error) {
        console.error('Failed to check initial location permission:', error);
      }
    };

    checkInitialPermission();
  }, []);

  return {
    // 状态
    currentLocation,
    hasLocationPermission,
    isLoadingLocation,
    locationError,
    isInitialized,

    // 方法
    requestLocationPermission,
    getCurrentLocation,
    getCachedLocation,
    initializeLocation,
    checkLocationServicesEnabled,
    formatLocationForDisplay,
  };
};
