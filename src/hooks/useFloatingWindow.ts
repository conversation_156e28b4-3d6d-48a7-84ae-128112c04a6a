import { useEffect, useCallback, useRef } from 'react';
import { NativeModules, DeviceEventEmitter } from 'react-native';
import { useRecordingContext } from '../contexts/RecordingContext';

const { FloatingWindowModule } = NativeModules;

/**
 * 浮窗管理Hook（更新版）
 * 使用新的胶囊协调系统
 */
export const useFloatingWindow = () => {
  const {
    setIsRecordingCapsuleVisible,
    isRecording,
    isPaused,
    recordingDuration,
    callCapsuleStop,
  } = useRecordingContext();

  const isRecordingActive = useRef(false);
  const hasPermission = useRef(false);

  /**
   * 检查和请求权限（使用新API）
   */
  const checkAndRequestPermission = useCallback(async (): Promise<boolean> => {
    if (!FloatingWindowModule) {
      console.warn('当前平台不支持浮窗功能');
      return false;
    }

    try {
      // 检查是否已有权限
      const hasOverlayPermission =
        await FloatingWindowModule.hasOverlayPermission();
      if (hasOverlayPermission) {
        hasPermission.current = true;
        return true;
      }

      // 请求权限
      const granted = await FloatingWindowModule.requestOverlayPermission();
      hasPermission.current = granted;

      if (!granted) {
        console.warn('用户未授予浮窗权限');
      }

      return granted;
    } catch (error) {
      console.error('检查/请求浮窗权限失败:', error);
      return false;
    }
  }, []);

  /**
   * 开始录音（使用新的胶囊协调系统）
   */
  const startRecording = useCallback(async (): Promise<boolean> => {
    if (!hasPermission.current) {
      const granted = await checkAndRequestPermission();
      if (!granted) {
        return false;
      }
    }

    if (!FloatingWindowModule) {
      return false;
    }

    try {
      const data = {
        duration: recordingDuration,
        isRecording: true,
        isPaused: false,
      };

      await FloatingWindowModule.startRecording(data);
      isRecordingActive.current = true;
      console.log('开始录音，胶囊协调系统已激活');
      return true;
    } catch (error) {
      console.error('开始录音失败:', error);
      return false;
    }
  }, [recordingDuration, checkAndRequestPermission]);

  /**
   * 停止录音
   */
  const stopRecording = useCallback(async (): Promise<boolean> => {
    if (!FloatingWindowModule) {
      return false;
    }

    try {
      await FloatingWindowModule.stopRecording();
      isRecordingActive.current = false;
      console.log('停止录音，胶囊协调系统已停用');
      return true;
    } catch (error) {
      console.error('停止录音失败:', error);
      return false;
    }
  }, []);

  /**
   * 更新录音状态
   */
  const updateRecording = useCallback(async (): Promise<void> => {
    if (!isRecordingActive.current || !FloatingWindowModule) {
      return;
    }

    try {
      const data = {
        duration: recordingDuration,
        isRecording,
        isPaused,
      };

      await FloatingWindowModule.updateRecording(data);
    } catch (error) {
      console.error('更新录音状态失败:', error);
    }
  }, [recordingDuration, isRecording, isPaused]);

  /**
   * 设置新的胶囊协调系统事件监听器
   */
  useEffect(() => {
    if (!FloatingWindowModule) {
      return;
    }

    // 监听原生胶囊停止事件
    const stopListener = DeviceEventEmitter.addListener(
      'onNativeCapsuleStop',
      () => {
        console.log('接收到原生胶囊停止事件');
        callCapsuleStop();
      }
    );

    // 监听胶囊显示/隐藏事件（可选，用于调试）
    const showListener = DeviceEventEmitter.addListener(
      'onShowRNCapsule',
      (data) => {
        console.log('显示RN胶囊事件:', data);
        setIsRecordingCapsuleVisible(true);
      }
    );

    const hideListener = DeviceEventEmitter.addListener(
      'onHideRNCapsule',
      (data) => {
        console.log('隐藏RN胶囊事件:', data);
        setIsRecordingCapsuleVisible(false);
      }
    );

    return () => {
      stopListener.remove();
      showListener.remove();
      hideListener.remove();
    };
  }, [callCapsuleStop, setIsRecordingCapsuleVisible]);

  /**
   * 监听录音状态变化，更新胶囊内容
   */
  useEffect(() => {
    if (isRecordingActive.current) {
      updateRecording();
    }
  }, [recordingDuration, isRecording, isPaused, updateRecording]);

  /**
   * 初始化权限检查
   */
  useEffect(() => {
    checkAndRequestPermission();
  }, [checkAndRequestPermission]);

  return {
    isSupported: !!FloatingWindowModule,
    isRecordingActive: isRecordingActive.current,
    startRecording,
    stopRecording,
    updateRecording,
    checkAndRequestPermission,
  };
};
