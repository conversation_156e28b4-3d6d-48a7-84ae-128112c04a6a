import { useState, useEffect, useRef, useCallback } from 'react';
import { Audio } from 'expo-av';
import { Platform } from 'react-native';
import Toast from 'react-native-toast-message';

interface PlaybackStatus {
  isLoaded: boolean;
  isPlaying: boolean;
  isBuffering: boolean;
  durationMillis: number;
  positionMillis: number;
  rate: number;
}

export const useAudioPlayer = () => {
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [playbackStatus, setPlaybackStatus] = useState<PlaybackStatus>({
    isLoaded: false,
    isPlaying: false,
    isBuffering: false,
    durationMillis: 0,
    positionMillis: 0,
    rate: 1.0,
  });
  const [playbackSpeed, setPlaybackSpeed] = useState(1.0);
  const previousBufferingRef = useRef<boolean>(false);
  const statusUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // 清理音频资源
    return () => {
      if (sound) {
        unload();
      }
    };
  }, [sound, unload]);

  // Android 专用：修复 expo-av SDK 53 上的回调问题
  useEffect(() => {
    if (Platform.OS === 'android' && sound && playbackStatus.isLoaded) {
      if (playbackStatus.isPlaying) {
        // 开始播放时，启动定期状态更新
        // eslint-disable-next-line no-console
        console.log('🔧 启动 Android 状态更新补丁');
        statusUpdateIntervalRef.current = setInterval(async () => {
          try {
            await sound.getStatusAsync();
          } catch (error) {
            // eslint-disable-next-line no-console
            console.error('❌ Android 状态更新失败:', error);
          }
        }, 500);
      } else {
        // 停止播放时，清理定期更新
        if (statusUpdateIntervalRef.current) {
          // eslint-disable-next-line no-console
          console.log('🔧 清理 Android 状态更新补丁');
          clearInterval(statusUpdateIntervalRef.current);
          statusUpdateIntervalRef.current = null;
        }
      }
    }

    return () => {
      if (statusUpdateIntervalRef.current) {
        clearInterval(statusUpdateIntervalRef.current);
        statusUpdateIntervalRef.current = null;
      }
    };
  }, [sound, playbackStatus.isPlaying, playbackStatus.isLoaded]);

  const loadAudio = async (uri: string) => {
    try {
      // 如果已有音频加载，先卸载
      if (sound) {
        await unload();
      }

      // 强制设置播放模式，完全清除录音模式
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false, // 禁用录音
        playsInSilentModeIOS: true, // 静音模式下播放
        staysActiveInBackground: false,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });

      // eslint-disable-next-line no-console
      console.log('🔧 已设置音频模式为播放模式');

      // 创建Sound实例（优化缓冲参数）
      const { sound: newSound, status } = await Audio.Sound.createAsync(
        { uri },
        {
          shouldPlay: false,
          isLooping: false,
          rate: playbackSpeed,
          volume: 1.0,
          progressUpdateIntervalMillis: 500, // 增加更新频率以便及时检测缓冲状态
        },
        onPlaybackStatusUpdate
      );

      setSound(newSound);

      // 如果加载成功，更新状态
      if (status.isLoaded) {
        setPlaybackStatus({
          isLoaded: true,
          isPlaying: false,
          isBuffering: false,
          durationMillis: status.durationMillis || 0,
          positionMillis: status.positionMillis || 0,
          rate: status.rate || 1.0,
        });
      }

      return newSound;
    } catch (error) {
      console.error('Failed to load audio', error);
      Toast.show({
        type: 'error',
        text1: '加载失败',
        text2: '无法加载音频文件',
      });
      return null;
    }
  };

  const onPlaybackStatusUpdate = async (status: Audio.AVPlaybackStatus) => {
    // 只在关键状态变化时输出日志
    if (status.isLoaded && (status.isBuffering || status.didJustFinish)) {
      // eslint-disable-next-line no-console
      console.log('🎵 播放状态更新:', {
        isLoaded: status.isLoaded,
        isPlaying: status.isPlaying,
        isBuffering: status.isBuffering,
        position: status.positionMillis,
        duration: status.durationMillis,
        didJustFinish: status.didJustFinish,
        shouldPlay: status.shouldPlay,
      });
    }

    if (status.isLoaded) {
      const wasBuffering = previousBufferingRef.current;
      const isBuffering = status.isBuffering || false;

      setPlaybackStatus({
        isLoaded: true,
        isPlaying: status.isPlaying || false,
        isBuffering: isBuffering,
        durationMillis: status.durationMillis || 0,
        positionMillis: status.positionMillis || 0,
        rate: status.rate || 1.0,
      });

      // 🔥 关键修复：检测缓冲完成后自动恢复播放（绕过 shouldPlay 问题）
      if (
        wasBuffering && // 之前在缓冲
        !isBuffering && // 现在缓冲完成
        !status.isPlaying && // 但没有在播放
        !status.didJustFinish && // 没有播放完成
        status.positionMillis > 0 && // 有播放进度
        sound // 音频对象存在
      ) {
        // eslint-disable-next-line no-console
        console.log(
          '🔄 缓冲完成，强制恢复播放... 位置:',
          status.positionMillis
        );
        try {
          // 解决方案：直接使用 playAsync()，不依赖 shouldPlay 属性
          // 这是 expo-av Android 上的推荐做法
          const playResult = await sound.playAsync();
          if (playResult.isLoaded) {
            // eslint-disable-next-line no-console
            console.log('✅ 强制恢复播放成功:', playResult.isPlaying);
          }
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error('❌ 强制恢复播放失败:', error);
          // 如果第一次失败，尝试重置状态后再播放
          try {
            await sound.setPositionAsync(status.positionMillis);
            await sound.playAsync();
            // eslint-disable-next-line no-console
            console.log('✅ 重试恢复播放成功');
          } catch (retryError) {
            // eslint-disable-next-line no-console
            console.error('❌ 重试恢复播放也失败:', retryError);
          }
        }
      }

      // 更新缓冲状态引用
      previousBufferingRef.current = isBuffering;

      // 检查播放是否意外停止
      if (
        status.didJustFinish &&
        status.positionMillis < (status.durationMillis || 0) - 1000
      ) {
        console.warn(
          '⚠️ 音频播放意外停止，位置:',
          status.positionMillis,
          '总长度:',
          status.durationMillis
        );
      }

      // 检查播放是否因为错误而停止
      if (
        !status.isPlaying &&
        status.positionMillis > 0 &&
        !status.didJustFinish &&
        !isBuffering
      ) {
        console.warn('⚠️ 音频播放意外暂停:', status);
      }
    } else if (status.error) {
      console.error('❌ 音频播放错误:', status.error);
    }
  };

  const playPause = async () => {
    if (!sound) {
      Toast.show({
        type: 'error',
        text1: '播放错误',
        text2: '音频未加载',
      });
      return;
    }

    try {
      const status = await sound.getStatusAsync();
      if (status.isLoaded) {
        // eslint-disable-next-line no-console
        console.log('🎛️ 播放/暂停操作，当前状态:', {
          isPlaying: status.isPlaying,
          isLoaded: status.isLoaded,
          shouldPlay: status.shouldPlay,
          position: status.positionMillis,
        });
      }

      if (status.isLoaded) {
        if (status.isPlaying) {
          // eslint-disable-next-line no-console
          console.log('⏸️ 暂停播放');
          await sound.pauseAsync();
        } else {
          // 重新强制设置音频模式确保播放顺畅
          await Audio.setAudioModeAsync({
            allowsRecordingIOS: false, // 禁用录音
            playsInSilentModeIOS: true, // 静音模式下播放
            staysActiveInBackground: false,
            shouldDuckAndroid: true,
            playThroughEarpieceAndroid: false,
          });

          // eslint-disable-next-line no-console
          console.log('🔧 重新设置音频播放模式');

          if (
            status.isLoaded &&
            status.positionMillis >= (status?.durationMillis || 0)
          ) {
            // eslint-disable-next-line no-console
            console.log('🔄 重置到开始位置');
            await seekTo(0);
          }

          // eslint-disable-next-line no-console
          console.log('▶️ 开始播放');
          await sound.playAsync();
        }
      }
    } catch (error) {
      console.error('Failed to play/pause', error);
      Toast.show({
        type: 'error',
        text1: '操作失败',
        text2: '无法播放/暂停音频',
      });
    }
  };

  const seekTo = async (position: number) => {
    if (!sound) return;

    try {
      await sound.setPositionAsync(position);
    } catch (error) {
      console.error('Failed to seek', error);
    }
  };

  const changeSpeed = async (speed: number) => {
    if (!sound) return;

    try {
      await sound.setRateAsync(speed, true);
      setPlaybackSpeed(speed);

      Toast.show({
        type: 'info',
        text1: '播放速度',
        text2: `${speed}x`,
      });
    } catch (error) {
      console.error('Failed to change speed', error);
      Toast.show({
        type: 'error',
        text1: '操作失败',
        text2: '无法改变播放速度',
      });
    }
  };

  const forward = async (seconds: number = 10) => {
    if (!sound) return;

    try {
      const status = await sound.getStatusAsync();
      if (status.isLoaded) {
        const newPosition = Math.min(
          (status.positionMillis || 0) + seconds * 1000,
          status.durationMillis || 0
        );
        await seekTo(newPosition);
      }
    } catch (error) {
      console.error('Failed to forward', error);
    }
  };

  const rewind = async (seconds: number = 10) => {
    if (!sound) return;

    try {
      const status = await sound.getStatusAsync();
      if (status.isLoaded) {
        const newPosition = Math.max(
          (status.positionMillis || 0) - seconds * 1000,
          0
        );
        await seekTo(newPosition);
      }
    } catch (error) {
      console.error('Failed to rewind', error);
    }
  };

  const stop = async () => {
    if (!sound) return;

    try {
      await sound.stopAsync();
      await sound.setPositionAsync(0);
    } catch (error) {
      console.error('Failed to stop', error);
    }
  };

  const unload = useCallback(async () => {
    if (!sound) return;

    try {
      // 清理 Android 状态更新定时器
      if (statusUpdateIntervalRef.current) {
        // eslint-disable-next-line no-console
        console.log('🔧 卸载时清理 Android 状态更新补丁');
        clearInterval(statusUpdateIntervalRef.current);
        statusUpdateIntervalRef.current = null;
      }

      await sound.unloadAsync();
      setSound(null);
      setPlaybackStatus({
        isLoaded: false,
        isPlaying: false,
        isBuffering: false,
        durationMillis: 0,
        positionMillis: 0,
        rate: 1.0,
      });
      setPlaybackSpeed(1.0);
      // 重置缓冲状态跟踪
      previousBufferingRef.current = false;
    } catch (error) {
      console.error('Failed to unload', error);
    }
  }, [sound]);

  return {
    sound,
    playbackStatus,
    playbackSpeed,
    loadAudio,
    playPause,
    seekTo,
    changeSpeed,
    forward,
    rewind,
    stop,
    unload,
  };
};
