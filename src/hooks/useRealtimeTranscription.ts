import { useCallback, useRef, useState } from 'react';
import { XunfeiRealtimeASR } from '../services/speech/XunfeiRealtimeASR';
import { XUNFEI_CONFIG } from '../config/transcription';

/**
 * 实时转写Hook - 简化版
 * 直接处理符合讯飞要求的PCM数据：16kHz、16bit、单声道
 */
export const useRealtimeTranscription = () => {
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [transcriptionText, setTranscriptionText] = useState('');
  const [partialText, setPartialText] = useState('');

  const asrRef = useRef<XunfeiRealtimeASR | null>(null);
  const isStartingRef = useRef(false);
  const isStoppingRef = useRef(false);
  const cleanupTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 开始实时转写 - 直接处理PCM数据
  const startTranscription = useCallback(async () => {
    if (isStartingRef.current) {
      console.log('转写服务正在启动中，忽略重复请求');
      return;
    }

    if (asrRef.current?.isConnected()) {
      console.log('转写服务已连接，无需重复启动');
      return;
    }

    isStartingRef.current = true;
    console.log('🚀 启动实时转写服务，处理PCM数据...');

    try {
      if (cleanupTimeoutRef.current) {
        clearTimeout(cleanupTimeoutRef.current);
        cleanupTimeoutRef.current = null;
      }

      if (asrRef.current) {
        try {
          asrRef.current.destroy();
        } catch (error) {
          console.log('清理旧ASR实例时出错:', error);
        }
        asrRef.current = null;
      }

      // 创建新的ASR实例
      asrRef.current = new XunfeiRealtimeASR({
        appId: XUNFEI_CONFIG.appId,
        apiKey: XUNFEI_CONFIG.apiKey,
        onStart: (_e) => {
          console.log('✅ 讯飞转写服务连接成功');
          setIsConnected(true);
          setIsTranscribing(true);
          isStartingRef.current = false;
        },
        onMessage: (message) => {
          try {
            if (message.text) {
              console.log('📝 收到转写结果:', {
                文本: message.text,
                文本长度: message.text.length,
                类型: message.type,
                说明: '已在服务层过滤中间结果，此处均为最终结果',
              });

              // 由于在XunfeiRealtimeASR服务层已过滤掉中间结果(type=1)
              // 这里收到的都是最终结果(type=0)，直接累加到转写文本中
              setTranscriptionText((prev) => prev + message.text);
              setPartialText(''); // 清空临时文本
            }
          } catch (error) {
            console.error('❌ 处理转写消息失败:', error);
          }
        },
        onError: (e) => {
          console.error('❌ 讯飞转写服务错误:', {
            错误对象: e,
            错误类型: typeof e,
            错误消息: e?.message || e?.desc || '未知错误',
            错误代码: e?.code || '无代码',
            完整错误: JSON.stringify(e, null, 2),
          });
          setIsConnected(false);
          isStartingRef.current = false;

          // 延迟清理，避免立即重连
          cleanupTimeoutRef.current = setTimeout(() => {
            if (asrRef.current) {
              try {
                asrRef.current.destroy();
                asrRef.current = null;
              } catch (error) {
                console.error('清理ASR实例时出错:', error);
              }
            }
          }, 1000);
        },
        onClose: (e) => {
          console.log('📪 讯飞转写服务连接关闭:', e.code, e.reason);
          setIsConnected(false);
          isStartingRef.current = false;

          if (e.code !== 1000) {
            console.log('异常关闭，清理资源');
            cleanupTimeoutRef.current = setTimeout(() => {
              if (asrRef.current) {
                try {
                  asrRef.current.destroy();
                  asrRef.current = null;
                } catch (error) {
                  console.error('清理ASR实例时出错:', error);
                }
              }
            }, 1000);
          }
        },
      });

      // 启动ASR服务
      await asrRef.current.start();
      console.log('🎤 转写服务启动完成，等待连接...');
    } catch (error) {
      console.error('❌ 启动转写服务失败:', error);
      setIsTranscribing(false);
      setIsConnected(false);
      isStartingRef.current = false;

      if (asrRef.current) {
        try {
          asrRef.current.destroy();
          asrRef.current = null;
        } catch (cleanupError) {
          console.error('清理ASR实例失败:', cleanupError);
        }
      }
    }
  }, []);

  // 停止实时转写
  const stopTranscription = useCallback(async () => {
    if (isStoppingRef.current) {
      console.log('正在停止转写服务，忽略重复请求');
      return;
    }

    if (!asrRef.current) {
      console.log('转写服务未启动，无需停止');
      return;
    }

    isStoppingRef.current = true;
    console.log('🛑 停止实时转写...');

    try {
      if (cleanupTimeoutRef.current) {
        clearTimeout(cleanupTimeoutRef.current);
        cleanupTimeoutRef.current = null;
      }

      if (asrRef.current) {
        try {
          asrRef.current.stop();
          asrRef.current.destroy();
          asrRef.current = null;
        } catch (error) {
          console.error('停止ASR服务时出错:', error);
          if (asrRef.current) {
            asrRef.current.destroy();
            asrRef.current = null;
          }
        }
      }

      setIsTranscribing(false);
      setIsConnected(false);
      isStartingRef.current = false;

      console.log('✅ 实时转写已停止');
    } finally {
      isStoppingRef.current = false;
    }
  }, []);

  // 发送PCM音频数据给讯飞转写服务
  const sendAudioData = useCallback((pcmData: Uint8Array) => {
    if (
      asrRef.current &&
      asrRef.current.isConnected() &&
      pcmData &&
      pcmData.length > 0
    ) {
      try {
        asrRef.current.send(pcmData);
        console.log('📤 发送PCM数据到讯飞:', {
          字节数: pcmData.length,
          连接状态: '已连接',
          数据格式: '16kHz、16bit、单声道PCM',
        });
      } catch (error) {
        console.error('❌ 发送PCM数据失败:', error);
      }
    } else {
      console.warn('⚠️ 无法发送PCM数据:', {
        ASR连接: asrRef.current?.isConnected() || false,
        音频数据: pcmData ? pcmData.length : 0,
      });
    }
  }, []);

  // 清除转写文本
  const clearTranscription = useCallback(() => {
    setTranscriptionText('');
    setPartialText('');
    console.log('🧹 已清除转写文本');
  }, []);

  // 重置转写状态
  const resetTranscription = useCallback(() => {
    setTranscriptionText('');
    setPartialText('');
    setIsTranscribing(false);
    setIsConnected(false);
    console.log('🔄 已重置转写状态');
  }, []);

  return {
    isTranscribing,
    isConnected,
    transcriptionText,
    partialText,
    startTranscription,
    stopTranscription,
    sendAudioData,
    clearTranscription,
    resetTranscription,
  };
};
