import { useState, useCallback } from 'react';
import { Dimensions, GestureResponderEvent } from 'react-native';

const { width: windowWidth } = Dimensions.get('window');

export function useSwipe(
  onSwipeLeft?: () => void,
  onSwipeRight?: () => void,
  rangeOffset = 4
) {
  const [firstTouch, setFirstTouch] = useState(0);

  // set user touch start position
  const onTouchStart = useCallback((e: GestureResponderEvent) => {
    setFirstTouch(e.nativeEvent.pageX);
  }, []);

  // when touch ends check for swipe directions
  const onTouchEnd = useCallback(
    (e: GestureResponderEvent) => {
      // get touch position and screen size
      const positionX = e.nativeEvent.pageX;
      const range = windowWidth / rangeOffset;

      // check if position is growing positively and has reached specified range
      if (positionX - firstTouch > range) {
        onSwipeRight?.();
      }
      // check if position is growing negatively and has reached specified range
      else if (firstTouch - positionX > range) {
        onSwipeLeft?.();
      }
    },
    [firstTouch, onSwipeLeft, onSwipeRight, rangeOffset]
  );

  return { onTouchStart, onTouchEnd };
}
