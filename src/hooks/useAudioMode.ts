import { useEffect, useRef } from 'react';
import { Audio } from 'expo-av';

/**
 * 管理音频模式的 Hook
 * 确保麦克风资源在不需要时被正确释放
 *
 * 已从 expo-audio 迁移到 expo-av，避免原生模块问题
 */
export const useAudioMode = () => {
  const isRecordingModeEnabled = useRef(false);

  // 启用录音模式
  const enableRecordingMode = async () => {
    if (isRecordingModeEnabled.current) return;

    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });
      isRecordingModeEnabled.current = true;
      console.log('🎤 expo-av 录音模式已启用');
    } catch (error) {
      console.error('❌ expo-av 启用录音模式失败:', error);
      throw error;
    }
  };

  // 禁用录音模式
  const disableRecordingMode = async () => {
    if (!isRecordingModeEnabled.current) return;

    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: true,
      });
      isRecordingModeEnabled.current = false;
      console.log('🔇 expo-av 录音模式已禁用');
    } catch (error) {
      console.error('❌ expo-av 禁用录音模式失败:', error);
      // 不抛出错误，因为这是清理操作
    }
  };

  // 组件卸载时确保禁用录音模式
  useEffect(() => {
    return () => {
      if (isRecordingModeEnabled.current) {
        Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
          playsInSilentModeIOS: true,
        }).catch((error: any) => {
          console.error('❌ expo-av 清理音频模式失败:', error);
        });
      }
    };
  }, []);

  return {
    enableRecordingMode,
    disableRecordingMode,
    isRecordingModeEnabled: () => isRecordingModeEnabled.current,
  };
};
