import { useCallback, useRef, useState, useEffect } from 'react';
import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { RealtimeAudioRecorder } from '../services/audio/RealtimeAudioRecorder';

// 兼容旧的API接口
export interface RealtimeAudioRecorderConfig {
  onAudioData?: (audioData: Float32Array) => void;
  onPCMData?: (pcmData: Uint8Array) => void; // 📋 新增：直接输出PCM数据，避免转换损失
  onError?: (error: Error) => void;
}

export interface RealtimeAudioConfig {
  sampleRate?: number;
  interval?: number;
  onAudioData?: (pcmData: Uint8Array) => void;
  onError?: (error: string) => void;
}

export interface RecordingData {
  uri?: string;
  duration?: number;
  size?: number;
}

/**
 * 实时音频录制Hook - 兼容版本
 * 支持旧的配置方式 (传入config) 和新的方式 (startRecording时传入)
 */
export const useRealtimeAudioRecorder = (
  legacyConfig?: RealtimeAudioRecorderConfig
) => {
  const [isRecording, setIsRecording] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isPaused, setIsPaused] = useState(false);

  const audioRecorderRef = useRef<RealtimeAudioRecorder | null>(null);
  const expoRecordingRef = useRef<Audio.Recording | null>(null);
  const configRef = useRef<RealtimeAudioRecorderConfig | null>(
    legacyConfig || null
  );
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 初始化音频录制器
  useEffect(() => {
    audioRecorderRef.current = new RealtimeAudioRecorder();

    return () => {
      if (audioRecorderRef.current) {
        audioRecorderRef.current.stopRecording();
        audioRecorderRef.current = null;
      }
    };
  }, []);

  // 计时器管理 - 修复暂停逻辑
  useEffect(() => {
    if (isRecording && !isPaused) {
      // 只有在录音且未暂停时才启动计时器
      durationIntervalRef.current = setInterval(() => {
        setDuration((prev) => prev + 1);
      }, 1000);
    } else {
      // 暂停或停止录音时清除计时器，但不重置时长
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
        durationIntervalRef.current = null;
      }
      // 只有完全停止录音时才重置时长
      if (!isRecording) {
        setDuration(0);
        setIsPaused(false); // 重置暂停状态
      }
    }
    return () => {
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
    };
  }, [isRecording, isPaused]);

  // 清理effect
  useEffect(() => {
    return () => {
      if (expoRecordingRef.current) {
        expoRecordingRef.current.stopAndUnloadAsync().catch(() => {});
        expoRecordingRef.current = null;
      }
    };
  }, []);

  const requestPermissions = useCallback(async (): Promise<boolean> => {
    try {
      console.log('📱 请求录音权限...');
      const granted = await audioRecorderRef.current?.requestPermissions();
      setHasPermission(granted || false);

      if (granted) {
        console.log('✅ 录音权限已获取');
      } else {
        console.log('❌ 录音权限被拒绝');
        configRef.current?.onError?.(new Error('录音权限被拒绝'));
      }

      return granted || false;
    } catch (error) {
      console.error('❌ 录音权限请求失败:', error);
      configRef.current?.onError?.(error as Error);
      setHasPermission(false);
      return false;
    }
  }, []);

  // 简化版：仅计算音频电平用于UI显示
  const calculateAudioLevel = useCallback((pcmData: Uint8Array): number => {
    if (pcmData.length === 0) return 0;

    let sum = 0;
    const samples = Math.min(pcmData.length / 2, 100); // 限制采样数量

    for (let i = 0; i < samples; i += 10) {
      const sample = (pcmData[i * 2 + 1] << 8) | pcmData[i * 2];
      const normalized =
        sample < 0x8000 ? sample / 0x7fff : (sample - 0x10000) / 0x8000;
      sum += Math.abs(normalized);
    }

    return sum / (samples / 10);
  }, []);

  // 开始录音 - 兼容新旧两种API
  const startRecording = useCallback(
    async (newConfig?: RealtimeAudioConfig): Promise<boolean> => {
      try {
        console.log('🎤 开始录音，使用兼容API...');

        if (!audioRecorderRef.current) {
          throw new Error('音频录制器未初始化');
        }

        // 检查权限
        if (!hasPermission) {
          const granted = await requestPermissions();
          if (!granted) return false;
        }

        // 停止之前的录音
        await stopRecording();

        // 启动expo-av录音用于文件保存
        try {
          await Audio.requestPermissionsAsync();
          await Audio.setAudioModeAsync({
            allowsRecordingIOS: true,
            playsInSilentModeIOS: true,
          });

          const { recording } = await Audio.Recording.createAsync(
            Audio.RecordingOptionsPresets.HIGH_QUALITY
          );
          expoRecordingRef.current = recording;
          console.log('✅ expo-av 录音器已启动（用于文件保存）');
        } catch (expoError) {
          console.warn(
            '⚠️ expo-av 录音器启动失败，将无法保存录音文件:',
            expoError
          );
        }

        // 使用新的API启动录音，但适配旧的回调
        const success = await audioRecorderRef.current.startRecording({
          sampleRate: newConfig?.sampleRate || 16000,
          interval: newConfig?.interval || 40,
          onAudioData: (pcmData: Uint8Array) => {
            try {
              // ✅ 直接发送原始PCM数据到讯飞（主要路径）
              if (configRef.current?.onPCMData) {
                configRef.current.onPCMData(pcmData);
              }

              // ✅ 如果有新的API回调，直接传递PCM数据
              if (newConfig?.onAudioData) {
                newConfig.onAudioData(pcmData);
              }

              // 🔧 仅用于UI显示的音频电平计算（不转换整个数组）
              if (configRef.current?.onAudioData) {
                const level = calculateAudioLevel(pcmData);
                setAudioLevel((prevLevel) => {
                  const diff = Math.abs(level - prevLevel);
                  return diff >= 0.01 ? level : prevLevel;
                });

                // 🚫 不再转换为Float32Array，只提供电平信息
                console.log('🎵 音频电平:', level.toFixed(3));
              }

              console.log('📤 PCM数据流:', {
                字节数: pcmData.length,
                格式: '16kHz、16bit、单声道',
                直接传输: true,
              });
            } catch (error) {
              console.error('❌ 处理音频数据失败:', error);
              const errorObj = error as Error;
              configRef.current?.onError?.(errorObj);
              newConfig?.onError?.(errorObj.message);
            }
          },
          onError: (error: string) => {
            console.error('❌ 录音错误:', error);
            const errorObj = new Error(error);
            configRef.current?.onError?.(errorObj);
            newConfig?.onError?.(error);
            setIsRecording(false);
          },
        });

        if (success) {
          setIsRecording(true);
          console.log('✅ 录音已启动 (兼容模式)');
          return true;
        } else {
          console.error('❌ 启动录音失败');
          return false;
        }
      } catch (error) {
        console.error('❌ 启动录音过程中发生错误:', error);
        const errorObj = error as Error;
        configRef.current?.onError?.(errorObj);
        setIsRecording(false);
        return false;
      }
    },
    [hasPermission, requestPermissions, calculateAudioLevel]
  );

  // 其他方法保持简单
  const stopRecording = useCallback(async (): Promise<RecordingData | null> => {
    try {
      console.log('🛑 停止录音...');
      let recordingData: RecordingData | null = null;

      if (audioRecorderRef.current) {
        await audioRecorderRef.current.stopRecording();
        console.log('✅ 实时音频流录音已停止');
      }

      if (expoRecordingRef.current) {
        try {
          await expoRecordingRef.current.stopAndUnloadAsync();
          const uri = expoRecordingRef.current.getURI();

          if (uri) {
            const status = await expoRecordingRef.current.getStatusAsync();

            // 获取文件大小
            let fileSize = 0;
            try {
              const fileInfo = await FileSystem.getInfoAsync(uri);
              if (fileInfo.exists && !fileInfo.isDirectory) {
                fileSize = (fileInfo as any).size || 0;
              }
            } catch (sizeError) {
              console.warn('⚠️ 获取文件大小失败:', sizeError);
            }

            recordingData = {
              uri,
              duration: Math.floor(status.durationMillis / 1000), // 转换毫秒为秒
              size: fileSize,
            };
            console.log('✅ 录音文件已保存:', uri, '大小:', fileSize, '字节');
          }
        } catch (expoError) {
          console.warn('⚠️ 保存录音文件失败:', expoError);
        }
        expoRecordingRef.current = null;
      }

      setIsRecording(false);
      setAudioLevel(0);

      return recordingData;
    } catch (error) {
      console.error('❌ 停止录音失败:', error);
      setIsRecording(false);
      return null;
    }
  }, []);

  // 暂停/恢复录音 - 实际实现
  const pauseRecording = useCallback(async (): Promise<boolean> => {
    try {
      console.log('⏸️ 暂停录音...');
      if (isRecording && !isPaused) {
        setIsPaused(true);
        // 暂停实时音频录制（如果底层支持）
        if (audioRecorderRef.current) {
          // 注意：当前的RealtimeAudioRecorder可能不支持暂停
          // 这里先设置状态，计时器会自动暂停
          console.log('✅ 录音已暂停（计时器已停止）');
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ 暂停录音失败:', error);
      return false;
    }
  }, [isRecording, isPaused]);

  const resumeRecording = useCallback(async (): Promise<boolean> => {
    try {
      console.log('▶️ 恢复录音...');
      if (isRecording && isPaused) {
        setIsPaused(false);
        // 恢复实时音频录制（如果底层支持）
        if (audioRecorderRef.current) {
          // 注意：当前的RealtimeAudioRecorder可能不支持恢复
          // 这里先设置状态，计时器会自动恢复
          console.log('✅ 录音已恢复（计时器已启动）');
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ 恢复录音失败:', error);
      return false;
    }
  }, [isRecording, isPaused]);

  const checkRecordingStatus = useCallback(async (): Promise<boolean> => {
    if (!audioRecorderRef.current) return false;

    try {
      const recording = await audioRecorderRef.current.isRecording();
      return recording;
    } catch (error) {
      console.error('❌ 检查录音状态失败:', error);
      return false;
    }
  }, []);

  return {
    isRecording,
    hasPermission,
    audioLevel,
    duration, // 兼容旧API
    isPaused, // 暴露暂停状态
    startRecording,
    stopRecording,
    pauseRecording, // 实际实现
    resumeRecording, // 实际实现
    requestPermissions,
    checkRecordingStatus,
  };
};
