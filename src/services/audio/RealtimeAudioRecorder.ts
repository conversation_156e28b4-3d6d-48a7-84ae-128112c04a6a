import { NativeModules, NativeEventEmitter, EmitterSubscription } from 'react-native';

const { RealtimeAudio } = NativeModules;

export interface AudioConfig {
  sampleRate: number;
  interval: number;
  onAudioData: (pcmData: Uint8Array) => void; // 📋 直接传递PCM字节数据，符合讯飞要求
  onError?: (error: string) => void;
}

/**
 * 实时音频录制器 - 简化版，直接提供符合讯飞要求的PCM数据
 * 音频格式：16kHz、16bit、单声道PCM
 * 数据格式：每40ms提供1280字节PCM数据
 */
export class RealtimeAudioRecorder {
  private audioEmitter?: NativeEventEmitter;
  private audioDataSubscription?: EmitterSubscription;
  private errorSubscription?: EmitterSubscription;
  private isInitialized = false;

  constructor() {
    this.initialize();
  }

  private initialize() {
    try {
      if (RealtimeAudio) {
        this.audioEmitter = new NativeEventEmitter(RealtimeAudio);
        this.isInitialized = true;
        console.log('✅ RealtimeAudioRecorder 初始化成功');
      } else {
        console.error('❌ RealtimeAudio 原生模块未找到');
        this.isInitialized = false;
      }
    } catch (error) {
      console.error('❌ RealtimeAudioRecorder 初始化失败:', error);
      this.isInitialized = false;
    }
  }

  /**
   * 请求录音权限
   */
  async requestPermissions(): Promise<boolean> {
    // 在Android上，权限在原生层处理
    return true;
  }

  /**
   * 开始录音 - 直接提供符合讯飞要求的PCM数据
   */
  async startRecording(config: AudioConfig): Promise<boolean> {
    try {
      if (!this.isInitialized || !this.audioEmitter) {
        throw new Error('RealtimeAudioRecorder 未正确初始化');
      }

      // 检查权限
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        throw new Error('没有录音权限');
      }

      // 清理之前的订阅
      this.cleanup();

      console.log('🎤 开始录音，直接提供PCM数据，符合讯飞文档要求:', {
        sampleRate: config.sampleRate,
        interval: config.interval,
      });

      // 监听PCM音频数据
      this.audioDataSubscription = this.audioEmitter.addListener(
        'onAudioData',
        (base64Data: string) => {
          try {
            // 📋 原生层提供的是符合讯飞要求的PCM数据，直接解码Base64即可
            const pcmBytes = this.base64ToPCMBytes(base64Data);
            console.log('📤 接收PCM音频数据:', {
              base64Length: base64Data.length,
              pcmBytes: pcmBytes.length,
              符合讯飞格式: '16kHz、16bit、单声道PCM',
            });
            config.onAudioData(pcmBytes);
          } catch (error) {
            console.error('❌ 处理PCM数据失败:', error);
            config.onError?.(`处理PCM数据失败: ${error}`);
          }
        }
      );

      // 监听错误事件
      this.errorSubscription = this.audioEmitter.addListener(
        'onError',
        (error: string) => {
          console.error('❌ 原生音频模块错误:', error);
          config.onError?.(error);
        }
      );

      // 启动录音
      const result = await RealtimeAudio.startRecording({
        sampleRate: config.sampleRate,
        interval: config.interval,
      });

      console.log('✅ 录音启动结果:', result);
      return result.success === true;
    } catch (error) {
      console.error('❌ 启动录音失败:', error);
      config.onError?.(`启动录音失败: ${error}`);
      this.cleanup();
      return false;
    }
  }

  /**
   * 停止录音
   */
  async stopRecording(): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        return false;
      }

      this.cleanup();

      if (RealtimeAudio) {
        const result = await RealtimeAudio.stopRecording();
        console.log('🛑 录音停止结果:', result);
        return result.success === true;
      }
      return false;
    } catch (error) {
      console.error('❌ 停止录音失败:', error);
      return false;
    }
  }

  /**
   * 检查是否正在录音
   */
  async isRecording(): Promise<boolean> {
    try {
      if (RealtimeAudio) {
        return await RealtimeAudio.isRecording();
      }
      return false;
    } catch (error) {
      console.error('❌ 检查录音状态失败:', error);
      return false;
    }
  }

  /**
   * 清理订阅和资源
   */
  private cleanup() {
    try {
      this.audioDataSubscription?.remove();
      this.errorSubscription?.remove();
      this.audioDataSubscription = undefined;
      this.errorSubscription = undefined;
      console.log('🧹 已清理音频事件订阅');
    } catch (error) {
      console.error('❌ 清理订阅失败:', error);
    }
  }

  /**
   * 将Base64数据解码为PCM字节数据
   * 原生层已经提供了符合讯飞要求的16kHz、16bit、单声道PCM数据
   */
  private base64ToPCMBytes(base64: string): Uint8Array {
    try {
      // 直接解码Base64，获取PCM字节数据
      const binaryString = atob(base64);
      const pcmBytes = new Uint8Array(binaryString.length);

      for (let i = 0; i < binaryString.length; i++) {
        pcmBytes[i] = binaryString.charCodeAt(i);
      }

      return pcmBytes;
    } catch (error) {
      console.error('❌ Base64解码失败:', error);
      throw new Error(`PCM数据解码失败: ${error}`);
    }
  }
}
