import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from 'axios';
import { ApiError, ApiResponse } from '../../types';
import Toast from 'react-native-toast-message';
import { useAuthStore } from '../../stores';
import { API_CONFIG } from '../../config/api';
import { resetAndNavigateTo } from '../../navigation/navigationService';

class ApiClient {
  private client: AxiosInstance;

  constructor(baseURL: string = API_CONFIG.BASE_URL) {
    this.client = axios.create({
      baseURL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        // 添加认证token
        const token = this.getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // 统一添加 APP-ID 头部（LCDP 平台要求）
        config.headers['APP-ID'] = API_CONFIG.APP_ID;

        if (__DEV__) {
          console.log(
            `🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`
          );
          console.log('Request headers:', config.headers);
          console.log('Request params:', config.params);
          console.log('Request data:', config.data);
        }
        return config;
      },
      (error) => {
        console.error('❌ Request Error:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        if (__DEV__) {
          console.log(
            `✅ API Response: ${response.status} ${response.config.url}`
          );
          console.log('Response headers:', response.headers);
          console.log('Response data:', JSON.stringify(response.data, null, 2));
        }
        return response;
      },
      (error: AxiosError) => {
        const apiError = this.handleError(error);
        console.error('❌ API Error:', apiError);

        // 显示错误提示
        Toast.show({
          type: 'error',
          text1: '请求失败',
          text2: apiError.message,
        });

        return Promise.reject(apiError);
      }
    );
  }

  private getAuthToken(): string | null {
    // 从auth store获取token
    const token = useAuthStore.getState().token;
    return token || 'default-token'; // 如果没有token，返回默认值用于测试
  }

  private handleError(error: AxiosError): ApiError {
    if (error.response) {
      // 服务器响应错误
      const status = error.response.status;
      const data = error.response.data as any;

      // 如果是401未授权错误，登出并跳转到登录页面
      if (status === 401) {
        // 使用setTimeout避免在promise链中直接调用
        setTimeout(() => {
          // 登出并重置用户状态
          const authStore = useAuthStore.getState();
          authStore.logout();

          // 使用导航服务重置导航堆栈并跳转到登录页面
          resetAndNavigateTo('Login');
        }, 0);
      }

      return {
        code: status.toString(),
        message: data?.message || this.getStatusMessage(status),
        details: data,
      };
    } else if (error.request) {
      // 网络错误
      return {
        code: 'NETWORK_ERROR',
        message: '网络连接失败，请检查网络设置',
      };
    } else {
      // 其他错误
      return {
        code: 'UNKNOWN_ERROR',
        message: error.message || '未知错误，请稍后重试',
      };
    }
  }

  private getStatusMessage(status: number): string {
    const statusMessages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权，请重新登录',
      403: '权限不足',
      404: '请求的资源不存在',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务暂时不可用',
    };

    return statusMessages[status] || '请求失败';
  }

  // 通用请求方法
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async delete<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.client.delete(url, config);
    return response.data;
  }

  // 文件上传
  async upload<T>(
    url: string,
    formData: FormData,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.client.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    });
    return response.data;
  }
}

// 创建默认实例
export const apiClient = new ApiClient();
export default ApiClient;
