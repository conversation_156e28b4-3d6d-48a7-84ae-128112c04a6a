import { apiClient } from './client';
import { WritingContent, ApiResponse } from '../../types';

export interface GenerateContentRequest {
  prompt: string;
  type: 'article' | 'optimization' | 'template';
  options?: {
    style?: string;
    length?: number;
    language?: 'zh' | 'en';
    tone?: 'formal' | 'casual' | 'professional';
  };
}

export interface GenerateContentResponse {
  content: string;
  wordCount: number;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface OptimizeContentRequest {
  content: string;
  optimizationType: 'grammar' | 'style' | 'clarity' | 'all';
  targetStyle?: string;
}

export interface ContinueConversationRequest {
  contentId: string;
  message: string;
}

export interface SummarizeContentRequest {
  contentId: string;
  summaryType?: 'brief' | 'detailed' | 'keypoints';
}

export interface WritingTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  prompt: string;
  examples?: string[];
}

class WritingService {
  // 内容管理
  async getContents(): Promise<WritingContent[]> {
    const response = await apiClient.get<WritingContent[]>('/writing/contents');
    return response.data;
  }

  async getContent(id: string): Promise<WritingContent> {
    const response = await apiClient.get<WritingContent>(`/writing/contents/${id}`);
    return response.data;
  }

  async saveContent(data: Partial<WritingContent>): Promise<WritingContent> {
    const response = await apiClient.post<WritingContent>('/writing/contents', data);
    return response.data;
  }

  async updateContent(id: string, data: Partial<WritingContent>): Promise<WritingContent> {
    const response = await apiClient.put<WritingContent>(`/writing/contents/${id}`, data);
    return response.data;
  }

  async deleteContent(id: string): Promise<void> {
    await apiClient.delete(`/writing/contents/${id}`);
  }

  // AI生成功能
  async generateContent(data: GenerateContentRequest): Promise<GenerateContentResponse> {
    const response = await apiClient.post<GenerateContentResponse>('/writing/generate', data);
    return response.data;
  }

  async optimizeContent(data: OptimizeContentRequest): Promise<GenerateContentResponse> {
    const response = await apiClient.post<GenerateContentResponse>('/writing/optimize', data);
    return response.data;
  }

  async continueConversation(data: ContinueConversationRequest): Promise<GenerateContentResponse> {
    const response = await apiClient.post<GenerateContentResponse>('/writing/continue', data);
    return response.data;
  }

  async summarizeContent(data: SummarizeContentRequest): Promise<{ summary: string }> {
    const response = await apiClient.post<{ summary: string }>('/writing/summarize', data);
    return response.data;
  }

  // 模板管理
  async getTemplates(): Promise<WritingTemplate[]> {
    const response = await apiClient.get<WritingTemplate[]>('/writing/templates');
    return response.data;
  }

  async getTemplate(id: string): Promise<WritingTemplate> {
    const response = await apiClient.get<WritingTemplate>(`/writing/templates/${id}`);
    return response.data;
  }

  async getTemplateCategories(): Promise<string[]> {
    const response = await apiClient.get<string[]>('/writing/templates/categories');
    return response.data;
  }

  // 导出功能
  async exportToPDF(contentId: string): Promise<Blob> {
    const response = await apiClient.get(`/writing/contents/${contentId}/export/pdf`, {
      responseType: 'blob',
    });
    return response.data as Blob;
  }

  async exportToWord(contentId: string): Promise<Blob> {
    const response = await apiClient.get(`/writing/contents/${contentId}/export/word`, {
      responseType: 'blob',
    });
    return response.data as Blob;
  }

  async exportToText(contentId: string): Promise<string> {
    const response = await apiClient.get<{ content: string }>(`/writing/contents/${contentId}/export/text`);
    return response.data.content;
  }

  // 搜索和过滤
  async searchContents(query: string): Promise<WritingContent[]> {
    const response = await apiClient.get<WritingContent[]>('/writing/contents/search', {
      params: { q: query },
    });
    return response.data;
  }

  async getContentsByType(type: WritingContent['type']): Promise<WritingContent[]> {
    const response = await apiClient.get<WritingContent[]>('/writing/contents', {
      params: { type },
    });
    return response.data;
  }

  // 使用统计
  async getUsageStatistics(): Promise<{
    totalContents: number;
    totalWords: number;
    tokensUsed: number;
    remainingQuota: number;
  }> {
    const response = await apiClient.get<{
      totalContents: number;
      totalWords: number;
      tokensUsed: number;
      remainingQuota: number;
    }>('/writing/statistics');
    return response.data;
  }

  // 热门话题
  async getTrendingTopics(): Promise<Array<{ topic: string; heat: number }>> {
    const response = await apiClient.get<Array<{ topic: string; heat: number }>>('/writing/trending');
    return response.data;
  }
}

export const writingService = new WritingService();
