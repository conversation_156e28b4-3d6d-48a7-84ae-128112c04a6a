import { apiClient } from './client';
import { Recording, ApiResponse } from '../../types';

export interface TranscriptionRequest {
  recordingId: string;
  language?: 'zh' | 'en';
}

export interface TranscriptionResponse {
  recordingId: string;
  transcription: string;
  confidence: number;
}

export interface AIAnalysisRequest {
  recordingId: string;
  analysisType: 'summary' | 'minutes' | 'keywords' | 'all';
}

export interface AIAnalysisResponse {
  recordingId: string;
  summary?: string;
  minutes?: string[];
  keywords?: string[];
  participants?: string[];
}

class RecordingService {
  // 获取录音列表
  async getRecordings(): Promise<Recording[]> {
    const response = await apiClient.get<Recording[]>('/recordings');
    return response.data;
  }

  // 获取单个录音详情
  async getRecording(id: string): Promise<Recording> {
    const response = await apiClient.get<Recording>(`/recordings/${id}`);
    return response.data;
  }

  // 创建录音记录
  async createRecording(data: Partial<Recording>): Promise<Recording> {
    const response = await apiClient.post<Recording>('/recordings', data);
    return response.data;
  }

  // 更新录音信息
  async updateRecording(id: string, data: Partial<Recording>): Promise<Recording> {
    const response = await apiClient.put<Recording>(`/recordings/${id}`, data);
    return response.data;
  }

  // 删除录音
  async deleteRecording(id: string): Promise<void> {
    await apiClient.delete(`/recordings/${id}`);
  }

  // 上传录音文件
  async uploadAudio(file: File | Blob, metadata?: Partial<Recording>): Promise<Recording> {
    const formData = new FormData();
    formData.append('audio', file);
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }
    const response = await apiClient.upload<Recording>('/recordings/upload', formData);
    return response.data;
  }

  // 语音转写
  async transcribe(data: TranscriptionRequest): Promise<TranscriptionResponse> {
    const response = await apiClient.post<TranscriptionResponse>('/recordings/transcribe', data);
    return response.data;
  }

  // AI分析
  async analyzeRecording(data: AIAnalysisRequest): Promise<AIAnalysisResponse> {
    const response = await apiClient.post<AIAnalysisResponse>('/recordings/analyze', data);
    return response.data;
  }

  // 获取录音的转写文本
  async getTranscription(recordingId: string): Promise<string> {
    const response = await apiClient.get<{ transcription: string }>(`/recordings/${recordingId}/transcription`);
    return response.data.transcription;
  }

  // 更新转写文本
  async updateTranscription(recordingId: string, transcription: string): Promise<void> {
    await apiClient.put(`/recordings/${recordingId}/transcription`, { transcription });
  }

  // 搜索录音
  async searchRecordings(query: string): Promise<Recording[]> {
    const response = await apiClient.get<Recording[]>('/recordings/search', {
      params: { q: query },
    });
    return response.data;
  }

  // 批量删除录音
  async deleteMultipleRecordings(ids: string[]): Promise<void> {
    await apiClient.post('/recordings/batch-delete', { ids });
  }
}

export const recordingService = new RecordingService();
