import { apiClient } from './client';
import { SESSION_CONFIG } from './const';
import { generateXSign } from '../../utils/signHelper';

// Session API 响应类型
export interface SessionObject {
  statusCd: string;
  creatorId: number;
  updatorId: number;
  createdTime: string;
  updatedTime: string;
  statusTime: string;
  remark: string;
  tenantCode: string;
  sessionId: number;
  botId: number;
  botTenantId: number;
  tenantId: number;
  extSystemId: number;
  sessionTitle: string;
  prologue: string;
  beginTime: string;
  endTime: string;
  fieldUpdateFlagMap: Record<string, any>;
}

export interface ErrorInfo {
  errorInfoId: number;
  errorCode: string;
  errorMsg: string;
  errorType: string;
  errorTypeName: string;
  errorLevel: string;
  errorKeyword: string;
  centerType: string;
  guidance: {
    errorGuidanceId: number;
    errorInfoId: number;
    title: string;
    content: string;
    guidanceUrl: string;
  };
}

export interface CreateSessionResponse {
  resultCode: string;
  resultMsg: string;
  resultObject: SessionObject;
  stack: string;
  errorInfos: ErrorInfo[];
  guidance: {
    errorGuidanceId: number;
    errorInfoId: number;
    title: string;
    content: string;
    guidanceUrl: string;
  };
}

// Create Session 请求参数
export interface CreateSessionParams {
  tenantId?: string;
  botId?: string;
  botTenantId?: string;
  extSystemId?: string;
  btnClick?: boolean;
}

/**
 * 创建会话
 * 使用统一的 apiClient，自动获得 Bearer Token 鉴权
 */
export const createSession = async (
  params: CreateSessionParams,
  options: { withSign?: boolean } = {}
): Promise<CreateSessionResponse> => {
  const requestParams = {
    btnClick: true,
    ...params,
  };

  // 构建特殊的请求头（保留 BOTE 系统必需的头部）
  const headers: Record<string, string> = {
    'Tenant-Id': SESSION_CONFIG.TENANT_ID,
  };

  // 可选：添加签名头
  if (options.withSign) {
    const xSign = generateXSign(requestParams);
    headers['X-SIGN'] = xSign;
    headers['XA-TYPE'] = '1.1';
  }

  const response = await apiClient.post<CreateSessionResponse>(
    `${SESSION_CONFIG.BASE_URL}/session/createSession`,
    null,
    {
      params: requestParams,
      headers,
    }
  );

  return response;
};

export const getDefaultSessionParams = (
  _recordingId: string
): CreateSessionParams => {
  return {
    tenantId: SESSION_CONFIG.TENANT_ID,
    botId: SESSION_CONFIG.BOT_ID,
    botTenantId: SESSION_CONFIG.BOT_TENANT_ID,
  };
};

