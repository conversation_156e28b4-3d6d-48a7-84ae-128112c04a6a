import ApiClient from './client';
import { getOrchestrationByCode, OrchestrationServiceCode } from './const';
import { API_CONFIG } from '../../config/api';
import { NoteData } from '@/types';
import { getUserId } from '@/utils/userIdUtils';

interface LCDPRequest {
  appId: string;
  serviceCode: string;
  version: string;
  serviceVersionId: string;
  parameters: Record<string, any>;
}

interface LCDPResponse {
  errorInfos?: any;
  guidance?: any;
  resultCode: string;
  resultMsg: string;
  resultObject: any;
}

interface userInfoParameters {
  userName: string;
  userCode: string;
  userId: string;
}

interface deleteRecordingRecordParameters {
  appFileId: string;
}

interface editRecordingRecordNameParameters {
  id: string;
  name: string;
}

interface recordingRecords {
  id?: string;
  recording_id?: string;
  beginTime: string;
  endTime: string;
  speaker: string;
  text: string;
  // creator_id: string;
  // create_time: string;
  // updator_id: string;
  // update_time: string;
}

interface addRecordingToKnowledgeGraphParameters {
  recording_records: recordingRecords[];
}

interface addNewRecordingParameters {
  app_file_id: string;
  file_id: string;
  name: string;
  duration?: number;
  source?: string;
  location?: string;
}

interface queryRecordingInfoParameters {
  app_file_id: string;
}

interface addAudioTranscriptionParameters {
  recording_records: recordingRecords[];
  recording_id: string;
}

interface updateTranscriptionProgressParameters {
  recording_id: string;
  transcriptionProgress: string;
}

interface transcribeAudioToTextParameters {
  maxSpeakers: string;
  minSpeakers: string;
  app_file_id: string;
  duration?: number;
}

interface updateSpeakerNameParameters {
  recording_records: recordingRecords[];
  appFileId: string;
}

interface addRecordMarkParameters {
  appFileId: string;
  recordMarks: NoteData[];
}

interface addSceneParameters {
  scene_name: string;
}

interface deleteSceneParameters {
  id: string;
}

interface addSpaceFileParameters {
  small_office_space: any;
}

interface changeSpaceSceneParameters {
  space_ids: Record<'id', string>[];
  scene_id: string;
}

interface deleteSpaceFileParameters {
  ids: Record<'id', string>[];
}

class LCDPService {
  private client: ApiClient;

  constructor(url?: string) {
    this.client = new ApiClient(
      url || 'https://www.hjlingxi.com/LCDP-DEV/lcdp-app/server'
    );
  }

  /**
   * 调用LCDP编排服务
   * @param serviceCode 服务代码
   * @param serviceVersionId 服务版本ID
   * @param parameters 请求参数
   * @param version 版本号，默认为"1.0"
   * @param appId 应用ID，默认从配置文件读取
   * @returns Promise<LCDPResponse>
   */
  async runOrchestration(
    serviceCode: OrchestrationServiceCode,
    parameters: Record<string, any> = {},
    appId: string = API_CONFIG.APP_ID,
    version: string = API_CONFIG.DEFAULT_VERSION
  ): Promise<LCDPResponse> {
    try {
      const { serviceVersionId } = getOrchestrationByCode(serviceCode);

      if (!serviceVersionId) {
        throw new Error(`调用服务失败${serviceCode}`);
      }

      const userId = await getUserId();
      const requestData: LCDPRequest = {
        appId,
        serviceCode,
        version,
        serviceVersionId,
        parameters: {
          ...parameters,
          userId, // 添加用户ID
        },
      };

      const response = await this.client.post<LCDPResponse>(
        '/app/orchestration/run',
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
        }
      );

      return response;
    } catch (error: any) {
      console.error('LCDP orchestration error:', error);
      return {
        resultCode: '1',
        resultMsg: error.message || '服务调用失败',
        resultObject: null,
      };
    }
  }

  // 检查用户信息
  async checkUserInfo(parameters: userInfoParameters): Promise<LCDPResponse> {
    return this.runOrchestration('checkUserInfo', parameters);
  }

  // 删除录音记录
  async deleteRecordingRecord(
    parameters: deleteRecordingRecordParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('deleteRecordingRecord', parameters);
  }

  async batchDeleteRecordings(parameters: {
    appFileIds: deleteRecordingRecordParameters[];
  }): Promise<LCDPResponse> {
    return this.runOrchestration('batchDeleteRecordings', parameters);
  }

  // 修改录音记录名称
  async editRecordingRecordName(
    parameters: editRecordingRecordNameParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('editRecordingRecordName', parameters);
  }

  // 添加录音内容到知识库
  async addRecordingToKnowledgeGraph(
    parameters: addRecordingToKnowledgeGraphParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('addRecordingToKnowledgeGraph', parameters);
  }

  // 新增录音主记录
  async addNewRecording(
    parameters: addNewRecordingParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('addNewRecording', parameters);
  }

  // 查询录音转文字记录
  async queryRecordingRecord(
    parameters: queryRecordingInfoParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('queryRecordingRecord', parameters);
  }

  // 查询录音主记录信息
  async queryRecordingInfo(
    parameters: queryRecordingInfoParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('queryRecordingInfo', parameters);
  }

  // 新增录音转文字记录
  async addAudioTranscription(
    parameters: addAudioTranscriptionParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('addAudioTranscription', parameters);
  }

  // 更新转写进度
  async updateTranscriptionProgress(
    parameters: updateTranscriptionProgressParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('updateTranscriptionProgress', parameters);
  }

  // 录音转文字
  async transcribeAudioToText(
    parameters: transcribeAudioToTextParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('transcribeAudioToText', parameters);
  }

  // 录音转文字
  async updateSpeakerName(
    parameters: updateSpeakerNameParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('updateSpeakerName', parameters);
  }

  // 录音转文字
  async addRecordMark(
    parameters: addRecordMarkParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('addRecordMark', parameters);
  }

  // 新增场景
  async addScene(parameters: addSceneParameters): Promise<LCDPResponse> {
    return this.runOrchestration('addScene', parameters);
  }

  // 删除场景
  async deleteScene(parameters: deleteSceneParameters): Promise<LCDPResponse> {
    return this.runOrchestration('deleteScene', parameters);
  }

  // 新增分类下的文件
  async addSpaceFile(
    parameters: addSpaceFileParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('addSpaceFile', parameters);
  }

  // 修改分类
  async changeSpaceScene(
    parameters: changeSpaceSceneParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('changeSpaceScene', parameters);
  }

  // 删除小办空间文件
  async deleteSpaceFile(
    parameters: deleteSpaceFileParameters
  ): Promise<LCDPResponse> {
    return this.runOrchestration('deleteSpaceFile', parameters);
  }

  /**
   * 上传单个文件
   * @param file 文件对象或文件流
   * @param fileName 文件名
   * @param fileType 文件类型
   * @returns Promise<LCDPResponse>
   */
  async uploadSingleFile(
    file: File | Blob | ArrayBuffer | any,
    fileType?: string
  ): Promise<LCDPResponse> {
    try {
      const formData = new FormData();

      // 根据文件类型创建合适的FormData条目
      if (file instanceof File) {
        formData.append('file', file);
      } else if (file instanceof Blob) {
        formData.append('file', file);
      } else if (file instanceof ArrayBuffer) {
        const blob = new Blob([file], {
          type: fileType || 'application/octet-stream',
        });
        formData.append('file', blob);
      } else if (file && file.uri) {
        // React Native文件对象，直接添加到FormData
        formData.append('file', file);
      } else {
        throw new Error('不支持的文件类型');
      }

      const response = await this.client.post<LCDPResponse>(
        '/app/file/uploadSingleFile',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            // APP-ID 现在由 ApiClient 的请求拦截器统一添加
          },
          timeout: API_CONFIG.FILE_UPLOAD_TIMEOUT, // 使用专门的文件上传超时配置
        }
      );

      return response;
    } catch (error: any) {
      console.error('文件上传错误:', error);
      return {
        resultCode: '1',
        resultMsg: error.message || '文件上传失败',
        resultObject: null,
      };
    }
  }

  async boteLogged(): Promise<LCDPResponse> {
    try {
      const response = await this.client.get<LCDPResponse>(
        '/BOTE/api/bote/logged?systemCode=LingxiSaas_TEST',
        {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
        }
      );
      return response;
    } catch (error: any) {
      console.error('BOTE logged error:', error);
      return {
        resultCode: '1',
        resultMsg: error.message || 'BOTE logged调用失败',
        resultObject: null,
      };
    }
  }
}

export const lcdpService = new LCDPService();
export { LCDPService };
export type { LCDPRequest, LCDPResponse };
