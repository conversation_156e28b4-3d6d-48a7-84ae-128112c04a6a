import { apiClient } from './client';
import ApiClient from './client';
import { User } from '../../types';
import { LoginCredentials } from '../../stores';
import { rsaEncryptFun } from '../../utils/rsa';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { LCDPService } from './lcdpService';

export interface RegisterData {
  username: string;
  email?: string;
  phone: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
}

export interface UpdatePasswordData {
  oldPassword: string;
  newPassword: string;
}

// 创建专门用于登录的API客户端实例
const portalClient = new ApiClient('https://www.hjlingxi.com/LCDP-TEST/portal');
const lcdpService = new LCDPService(
  'https://www.hjlingxi.com/LCDP-TEST/lcdp-app/server'
);

// 生成签名
function generateSign(): string {
  const timestamp = Date.now();
  const randomStr = Math.random().toString(36).substring(2, 15);
  return `44be3317af72226431633c74ca4edd03ee4d38427faf42570b99681a11a4a088.${timestamp}.${randomStr}`;
}

class AuthService {
  // 登录
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // 准备登录数据
      const formData = new URLSearchParams();
      formData.append('username', credentials.email); // 使用手机号作为username

      // RSA加密密码 - rsaEncryptFun已经包含了URL编码
      const encryptedPassword = rsaEncryptFun(credentials.password.trim());
      console.log('Encrypted password length:', encryptedPassword.length);

      formData.append('password', encryptedPassword);
      formData.append('loginType', 'COMMON');

      console.log('Sending login request with username:', credentials.email);

      // 调用登录接口
      const response = await portalClient.post<any>(
        '/login',
        formData.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'X-SIGN': generateSign(),
            'XA-TYPE': '1.1',
            Accept: 'application/json',
            Origin: 'https://www.hjlingxi.com',
            Referer: 'https://www.hjlingxi.com/assistant/',
          },
        }
      );

      console.log('Login response:', JSON.stringify(response));

      // 检查登录是否成功
      if (response?.isSuccess) {
        // 调用logged接口获取用户信息
        const loggedResponse = await lcdpService.boteLogged();

        console.log('Logged response:', JSON.stringify(loggedResponse));

        if (loggedResponse.resultCode === '0') {
          const { loginInfo } = loggedResponse.resultObject;

          const { token } = loginInfo;

          console.log('Token:', token);
          console.log('LoginInfo:', loginInfo);

          // 存储token和用户信息 - 确保值不为null/undefined
          if (token) {
            await AsyncStorage.setItem('userToken', token);
          }

          if (loginInfo) {
            await AsyncStorage.setItem('userInfo', JSON.stringify(loginInfo));
          }

          // 返回格式化的响应 - 确保token不为undefined
          return {
            token: token || 'default-token', // 提供默认值避免undefined
            user: {
              id: loginInfo?.attributes?.userInfo?.userId || '1',
              username:
                loginInfo?.attributes?.userInfo?.userName ||
                loginInfo?.name ||
                '用户',
              email: credentials.email,
              avatar: loginInfo?.avatar || '',
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          };
        }
      }

      throw new Error('登录失败，请检查手机号和密码');
    } catch (error: any) {
      console.error('登录错误:', error);
      throw new Error(error.message || '登录失败，请稍后重试');
    }
  }

  // 注册
  async register(data: RegisterData): Promise<LoginResponse> {
    try {
      // 准备注册数据
      const registerPayload = {
        userName: data.username,
        phone: data.phone,
        pwd: rsaEncryptFun(data.password.trim()), // RSA加密密码
        loginName: data.phone, // 使用手机号作为登录名
      };

      console.log('Sending register request:', {
        userName: registerPayload.userName,
        phone: registerPayload.phone,
        loginName: registerPayload.loginName,
      });

      // 调用注册接口
      const response = await portalClient.post<any>(
        '/basiccenter/user/register',
        registerPayload,
        {
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'X-SIGN': generateSign(),
            'XA-TYPE': '1.1',
            Accept: 'application/json',
            Origin: 'https://www.hjlingxi.com',
            Referer: 'https://www.hjlingxi.com/assistant/',
          },
        }
      );

      console.log('Register response:', JSON.stringify(response));

      // 检查注册是否成功
      if (response?.resultCode === '0' || response?.resultCode === 0) {
        // 注册成功，返回一个模拟的登录响应
        const userInfo = response?.resultObject || {};
        return {
          token: 'register-success', // 注册成功后需要用户重新登录
          user: {
            id: userInfo.userId || '1',
            username: userInfo.userName || data.username,
            email: userInfo.phone || data.phone,
            avatar: userInfo.thumbnailUri || '',
            createdAt: new Date(userInfo.createdDate || Date.now()),
            updatedAt: new Date(userInfo.updateDate || Date.now()),
          },
        };
      } else {
        throw new Error(response?.resultMsg || response?.message || '注册失败');
      }
    } catch (error: any) {
      console.error('注册错误:', error);
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      throw new Error(error.message || '注册失败，请稍后重试');
    }
  }

  // 登出
  async logout(): Promise<void> {
    try {
      await portalClient.get('/logout');
      // 清除本地存储的token和用户信息
      await AsyncStorage.multiRemove([
        'userToken',
        'userInfo',
        'botSelectedTenantID',
      ]);
    } catch (error) {
      console.error('登出错误:', error);
    }
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get<User>('/auth/me');
    return response;
  }

  // 更新用户信息
  async updateProfile(data: Partial<User>): Promise<User> {
    const response = await apiClient.put<User>('/auth/profile', data);
    return response;
  }

  // 更新密码
  async updatePassword(data: UpdatePasswordData): Promise<void> {
    await apiClient.put('/auth/password', data);
  }

  // 刷新Token
  async refreshToken(): Promise<{ token: string }> {
    const response = await apiClient.post<{ token: string }>('/auth/refresh');
    return response;
  }

  // 上传头像
  async uploadAvatar(file: File | Blob): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append('avatar', file);
    const response = await apiClient.upload<{ url: string }>(
      '/auth/avatar',
      formData
    );
    return response.data;
  }
}

export const authService = new AuthService();
