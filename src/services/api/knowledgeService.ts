import { apiClient } from './client';
import { KnowledgeDocument, KnowledgeCategory, ApiResponse } from '../../types';

export interface UploadDocumentRequest {
  file: File | Blob;
  categoryId?: string;
  tags?: string[];
  description?: string;
}

export interface SearchDocumentsParams {
  query?: string;
  categoryId?: string;
  type?: string;
  tags?: string[];
  page?: number;
  limit?: number;
}

export interface AIKnowledgeRequest {
  documentIds: string[];
  question: string;
}

export interface AIKnowledgeResponse {
  answer: string;
  references: Array<{
    documentId: string;
    title: string;
    excerpt: string;
  }>;
}

class KnowledgeService {
  // 文档管理
  async getDocuments(params?: SearchDocumentsParams): Promise<KnowledgeDocument[]> {
    const response = await apiClient.get<KnowledgeDocument[]>('/knowledge/documents', { params });
    return response.data;
  }

  async getDocument(id: string): Promise<KnowledgeDocument> {
    const response = await apiClient.get<KnowledgeDocument>(`/knowledge/documents/${id}`);
    return response.data;
  }

  async uploadDocument(data: UploadDocumentRequest): Promise<KnowledgeDocument> {
    const formData = new FormData();
    formData.append('file', data.file);
    if (data.categoryId) formData.append('categoryId', data.categoryId);
    if (data.tags) formData.append('tags', JSON.stringify(data.tags));
    if (data.description) formData.append('description', data.description);
    
    const response = await apiClient.upload<KnowledgeDocument>('/knowledge/documents/upload', formData);
    return response.data;
  }

  async updateDocument(id: string, data: Partial<KnowledgeDocument>): Promise<KnowledgeDocument> {
    const response = await apiClient.put<KnowledgeDocument>(`/knowledge/documents/${id}`, data);
    return response.data;
  }

  async deleteDocument(id: string): Promise<void> {
    await apiClient.delete(`/knowledge/documents/${id}`);
  }

  async searchDocuments(query: string): Promise<KnowledgeDocument[]> {
    const response = await apiClient.get<KnowledgeDocument[]>('/knowledge/documents/search', {
      params: { q: query },
    });
    return response.data;
  }

  // 分类管理
  async getCategories(): Promise<KnowledgeCategory[]> {
    const response = await apiClient.get<KnowledgeCategory[]>('/knowledge/categories');
    return response.data;
  }

  async getCategory(id: string): Promise<KnowledgeCategory> {
    const response = await apiClient.get<KnowledgeCategory>(`/knowledge/categories/${id}`);
    return response.data;
  }

  async createCategory(data: Partial<KnowledgeCategory>): Promise<KnowledgeCategory> {
    const response = await apiClient.post<KnowledgeCategory>('/knowledge/categories', data);
    return response.data;
  }

  async updateCategory(id: string, data: Partial<KnowledgeCategory>): Promise<KnowledgeCategory> {
    const response = await apiClient.put<KnowledgeCategory>(`/knowledge/categories/${id}`, data);
    return response.data;
  }

  async deleteCategory(id: string, moveToId?: string): Promise<void> {
    await apiClient.delete(`/knowledge/categories/${id}`, {
      params: { moveToId },
    });
  }

  // AI功能
  async askAI(data: AIKnowledgeRequest): Promise<AIKnowledgeResponse> {
    const response = await apiClient.post<AIKnowledgeResponse>('/knowledge/ai/ask', data);
    return response.data;
  }

  async generateSummary(documentId: string): Promise<{ summary: string }> {
    const response = await apiClient.post<{ summary: string }>(`/knowledge/documents/${documentId}/summary`);
    return response.data;
  }

  async extractKeywords(documentId: string): Promise<{ keywords: string[] }> {
    const response = await apiClient.post<{ keywords: string[] }>(`/knowledge/documents/${documentId}/keywords`);
    return response.data;
  }

  async suggestCategory(documentId: string): Promise<{ categoryId: string; confidence: number }> {
    const response = await apiClient.post<{ categoryId: string; confidence: number }>(
      `/knowledge/documents/${documentId}/suggest-category`
    );
    return response.data;
  }

  // 批量操作
  async batchDeleteDocuments(ids: string[]): Promise<void> {
    await apiClient.post('/knowledge/documents/batch-delete', { ids });
  }

  async batchMoveDocuments(ids: string[], categoryId: string): Promise<void> {
    await apiClient.post('/knowledge/documents/batch-move', { ids, categoryId });
  }

  async batchTagDocuments(ids: string[], tags: string[]): Promise<void> {
    await apiClient.post('/knowledge/documents/batch-tag', { ids, tags });
  }

  // 统计信息
  async getStatistics(): Promise<{
    totalDocuments: number;
    totalSize: number;
    documentsByType: Record<string, number>;
    documentsByCategory: Record<string, number>;
  }> {
    const response = await apiClient.get<{
      totalDocuments: number;
      totalSize: number;
      documentsByType: Record<string, number>;
      documentsByCategory: Record<string, number>;
    }>('/knowledge/statistics');
    return response.data;
  }
}

export const knowledgeService = new KnowledgeService();
