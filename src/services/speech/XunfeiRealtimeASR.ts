import { createHmacSha1, createMd5Hash } from '../crypto/CryptoUtils';

export interface SpeechRecognitionMessage {
  text: string | null;
  list?: any[];
  type?: string | number; // 添加type字段，用于区分中间结果(1)和最终结果(0)
}

export interface XunfeiASRConfig {
  appId: string;
  apiKey: string;
  onStart?: (e: Event) => void;
  onMessage?: (message: SpeechRecognitionMessage) => void;
  onError?: (e: Event) => void;
  onClose?: (e: CloseEvent) => void;
}

// 讯飞实时语音转写服务类 - 简化版
// 直接处理符合讯飞要求的PCM数据：16kHz、16bit、单声道PCM
export class XunfeiRealtimeASR {
  private ws?: WebSocket;
  private state: 'idle' | 'connecting' | 'connected' | 'closing' | 'closed' =
    'idle';
  private buffer: Uint8Array = new Uint8Array(0); // 📋 直接存储PCM字节数据
  private handlerInterval?: NodeJS.Timeout;
  private config: XunfeiASRConfig;

  private connectionTimeout?: NodeJS.Timeout;
  private heartbeatInterval?: NodeJS.Timeout;
  private isDestroyed = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectDelay = 2000;
  private lastAudioSendTime = 0;
  private connectionStartTime = 0;

  // 绑定的事件处理函数
  private boundOnOpen = this.onOpen.bind(this);
  private boundOnMessage = this.onMessage.bind(this);
  private boundOnError = this.onError.bind(this);
  private boundOnClose = this.onClose.bind(this);
  private boundHandleVisibilityChange = this.handleVisibilityChange.bind(this);
  private boundHandleBeforeUnload = this.handleBeforeUnload.bind(this);

  constructor(config: XunfeiASRConfig) {
    this.config = config;

    // 监听页面可见性变化和页面卸载事件
    if (typeof document !== 'undefined') {
      document.addEventListener(
        'visibilitychange',
        this.boundHandleVisibilityChange
      );
      window.addEventListener('beforeunload', this.boundHandleBeforeUnload);
    }
  }

  // 发送音频数据 - 直接处理PCM字节数据
  send(pcmData: Uint8Array): void {
    if (!pcmData || pcmData.length === 0) {
      console.warn('⚠️ 尝试发送空的PCM数据');
      return;
    }

    // 📋 原生层已提供符合讯飞要求的PCM数据，直接添加到缓冲区
    const newBuffer = new Uint8Array(this.buffer.length + pcmData.length);
    newBuffer.set(this.buffer);
    newBuffer.set(pcmData, this.buffer.length);
    this.buffer = newBuffer;

    console.log('📦 PCM数据已添加到缓冲区:', {
      缓冲区当前大小: this.buffer.length,
      新增数据大小: pcmData.length,
      数据格式: '16kHz、16bit、单声道PCM',
      符合讯飞要求: true,
    });
  }

  // 开始连接
  async start(): Promise<void> {
    if (this.isDestroyed) {
      throw new Error('ASR实例已被销毁');
    }

    if (this.state === 'connecting' || this.state === 'connected') {
      console.log(
        'ASR已在连接或已连接状态，先强制清理再重新连接（避免连接数超限）'
      );
      await this.forceCleanup();
    }

    // 🔧 关键修复：确保完全清理之前的连接，避免10800连接数超限错误
    this.cleanup();
    this.buffer = new Uint8Array(0);
    this.state = 'connecting';
    this.reconnectAttempts = 0;

    console.log('🚀 开始连接讯飞ASR服务...');
    return this.connectWebSocket();
  }

  // 强制清理连接，用于避免连接数超限
  private async forceCleanup(): Promise<void> {
    console.log('🧹 强制清理连接，避免连接数超限...');

    if (this.ws) {
      try {
        if (
          this.ws.readyState === WebSocket.OPEN ||
          this.ws.readyState === WebSocket.CONNECTING
        ) {
          this.ws.close(1000, '强制关闭避免连接超限');
        }
      } catch (error) {
        console.log('强制关闭连接时忽略错误:', error);
      }
    }

    this.cleanup();

    // 等待一小段时间确保服务端释放连接
    await new Promise((resolve) => setTimeout(resolve, 1000));
    console.log('✅ 强制清理完成，等待1秒后重新连接');
  }

  // 停止连接
  stop(): void {
    if (this.isDestroyed) {
      return;
    }

    this.state = 'closing';
    this.cleanup();
  }

  // 生成握手参数
  private getHandShakeParams() {
    const appId = this.config.appId;
    const apiKey = this.config.apiKey;
    console.log('讯飞配置:', appId, apiKey);
    const ts = Math.floor(new Date().getTime() / 1000);

    // 按照讯飞官方示例的正确签名算法: Base64(HmacSHA1(MD5(appid + ts), api_key))
    const baseString = appId + ts;
    const md5Hash = createMd5Hash(baseString);
    const signature = createHmacSha1(md5Hash, apiKey);
    const encodedSignature = encodeURIComponent(signature);

    console.log('🔐 签名生成详情:', {
      appId,
      ts,
      baseString,
      md5Hash: `${md5Hash.substring(0, 8)}...${md5Hash.substring(-8)} (长度:${md5Hash.length})`,
      signature: `${signature.substring(0, 8)}...${signature.substring(-8)} (长度:${signature.length})`,
      完整性检查: {
        baseString长度: baseString.length,
        md5Hash长度: md5Hash.length,
        signature长度: signature.length,
        签名算法: 'Base64(HmacSHA1(MD5(appid + ts), api_key))',
      },
    });

    return `?appid=${appId}&ts=${ts}&signa=${encodedSignature}&engLangType=2&roleType=2`;
  }

  // 建立WebSocket连接
  private async connectWebSocket(): Promise<void> {
    if (this.isDestroyed) {
      throw new Error('尝试连接时发现实例已销毁');
    }

    try {
      let url = 'wss://rtasr.xfyun.cn/v1/ws';
      const urlParam = this.getHandShakeParams();
      url = `${url}${urlParam}`;
      console.log('🌐 正在连接讯飞转写服务:', {
        URL长度: url.length,
        协议: url.startsWith('wss') ? 'WSS (安全)' : 'WS (非安全)',
        参数概览: url.split('?')[1]?.substring(0, 80) + '...',
        完整URL: url,
        服务器: 'rtasr.xfyun.cn',
      });

      this.ws = new WebSocket(url);

      console.log('📱 WebSocket实例创建完成');
      console.log('🔗 WebSocket初始状态:', this.getWebSocketStateText());

      // 监控连接状态变化
      const connectionMonitor = setInterval(() => {
        if (!this.ws || this.isDestroyed) {
          clearInterval(connectionMonitor);
          return;
        }

        console.log('📊 连接状态监控:', {
          readyState: this.getWebSocketStateText(),
          ASR状态: this.state,
          连接耗时: `${Date.now() - this.connectionStartTime}ms`,
        });

        if (
          this.ws.readyState === WebSocket.OPEN ||
          this.ws.readyState === WebSocket.CLOSED
        ) {
          clearInterval(connectionMonitor);
        }
      }, 500);

      // 设置连接超时监控
      const connectionTimeout = setTimeout(() => {
        if (this.state === 'connecting') {
          console.error('❌ WebSocket连接超时 (15秒)');
          clearInterval(connectionMonitor);
          this.ws?.close();
          this.state = 'idle';
          this.config.onError?.({
            type: 'timeout',
            message: '连接超时',
          } as any);
        }
      }, 15000);

      this.ws.onopen = (event) => {
        clearTimeout(connectionTimeout);
        clearInterval(connectionMonitor);
        console.log('讯飞转写服务连接成功');
        this.onOpen(event);
      };

      this.ws.onerror = (event) => {
        clearTimeout(connectionTimeout);
        clearInterval(connectionMonitor);
        this.onError(event);
      };

      this.ws.onclose = (event) => {
        clearTimeout(connectionTimeout);
        clearInterval(connectionMonitor);
        this.onClose(event);
      };

      this.ws.onmessage = this.onMessage.bind(this);

      console.log('✅ WebSocket事件监听器绑定完成');
      this.connectionStartTime = Date.now();
    } catch (error) {
      console.error('❌ 创建WebSocket连接失败:', error);
      this.state = 'idle';
      throw error;
    }
  }

  // WebSocket事件处理函数
  private onOpen(e: Event): void {
    if (this.isDestroyed) return;

    console.log('讯飞转写服务连接成功');
    this.state = 'connected';
    this.reconnectAttempts = 0;

    // 清除连接超时
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = undefined;
    }

    // 启动心跳
    this.startHeartbeat();

    // 开始发送音频数据
    setTimeout(() => {
      this.startAudioTransmission();
    }, 500);

    // 调用用户回调
    try {
      this.config.onStart && this.config.onStart(e);
    } catch (error) {
      console.error('用户onStart回调执行失败:', error);
    }

    // 解决连接Promise
    const ws = this.ws as any;
    if (ws && ws._resolve) {
      ws._resolve();
      delete ws._resolve;
      delete ws._reject;
    }
  }

  // WebSocket消息处理
  private onMessage(event: MessageEvent): void {
    try {
      const message = JSON.parse(event.data);

      // 🔍 【完整数据打印】- 讯飞服务器返回的原始数据
      console.log('📨 ========== 讯飞服务器完整返回数据 ==========');
      console.log('📨 原始JSON字符串:', event.data);
      console.log('📨 解析后的完整对象:', message);
      console.log('📨 对象类型:', typeof message);
      console.log('📨 对象键列表:', Object.keys(message));

      // 详细分析每个字段
      if (message.action) console.log('📨 action字段:', message.action);
      if (message.code) console.log('📨 code字段:', message.code);
      if (message.desc) console.log('📨 desc字段:', message.desc);
      if (message.sid) console.log('📨 sid字段 (会话ID):', message.sid);
      if (message.data) {
        console.log('📨 data字段 (原始):', message.data);
        console.log('📨 data字段类型:', typeof message.data);
        console.log('📨 data字段长度:', message.data.length);
      }

      console.log('📨 ================================================');

      if (message.action === 'started') {
        console.log('✅ 握手成功，会话ID:', message.sid);
        this.state = 'connected';
        this.config.onStart?.(event);
        this.startAudioTransmission();
      } else if (message.action === 'result') {
        // 🔍 【转写数据详细分析】
        console.log('📝 ========== 转写结果详细分析 ==========');
        console.log('📝 message.data原始内容:', message.data);

        try {
          if (message.data) {
            const resultData = JSON.parse(message.data);

            // 完整打印解析后的数据结构
            console.log('📝 解析后的完整数据结构:');
            console.log(JSON.stringify(resultData, null, 2));

            // 分析数据层级结构
            console.log('📝 数据层级分析:');
            console.log('📝 - 根级别键:', Object.keys(resultData));

            if (resultData.cn) {
              console.log('📝 - cn (中文)字段:', resultData.cn);
              console.log('📝 - cn字段键:', Object.keys(resultData.cn));

              if (resultData.cn.st) {
                console.log('📝 - st (句子)字段:', resultData.cn.st);
                console.log('📝 - st字段键:', Object.keys(resultData.cn.st));
                console.log(
                  '📝 - st.type (类型):',
                  resultData.cn.st.type,
                  '(0=最终结果, 1=中间结果)'
                );
                console.log('📝 - st.bg (开始时间):', resultData.cn.st.bg);
                console.log('📝 - st.ed (结束时间):', resultData.cn.st.ed);
                console.log('📝 - st.rl (可信度):', resultData.cn.st.rl);

                if (resultData.cn.st.rt) {
                  console.log(
                    '📝 - rt (实时结果)数组长度:',
                    resultData.cn.st.rt.length
                  );
                  console.log('📝 - rt完整数组:', resultData.cn.st.rt);

                  // 分析每个rt元素
                  resultData.cn.st.rt.forEach(
                    (rtItem: any, rtIndex: number) => {
                      console.log(`📝 - rt[${rtIndex}]:`, rtItem);
                      if (rtItem.ws) {
                        console.log(
                          `📝 - rt[${rtIndex}].ws (词语)数组长度:`,
                          rtItem.ws.length
                        );
                        rtItem.ws.forEach((wsItem: any, wsIndex: number) => {
                          console.log(
                            `📝 - rt[${rtIndex}].ws[${wsIndex}]:`,
                            wsItem
                          );
                          if (wsItem.cw) {
                            console.log(
                              `📝 - rt[${rtIndex}].ws[${wsIndex}].cw (候选词)数组:`,
                              wsItem.cw
                            );
                            wsItem.cw.forEach(
                              (cwItem: any, cwIndex: number) => {
                                console.log(
                                  `📝 - rt[${rtIndex}].ws[${wsIndex}].cw[${cwIndex}]:`,
                                  {
                                    词语: cwItem.w,
                                    可信度: cwItem.rl,
                                    其他字段: Object.keys(cwItem).filter(
                                      (k) => k !== 'w' && k !== 'rl'
                                    ),
                                  }
                                );
                              }
                            );
                          }
                        });
                      }
                    }
                  );
                }
              }
            }

            // 检查是否包含说话人信息
            console.log('📝 ========== 说话人信息检查 ==========');
            if (resultData.speaker || resultData.spk || resultData.sid) {
              console.log('📝 ✅ 发现潜在说话人信息:');
              if (resultData.speaker)
                console.log('📝 - speaker字段:', resultData.speaker);
              if (resultData.spk) console.log('📝 - spk字段:', resultData.spk);
              if (resultData.sid) console.log('📝 - sid字段:', resultData.sid);
            } else {
              console.log('📝 ❌ 未发现明显的说话人标识字段');
            }

            console.log('📝 ================================================');

            let resultText = '';
            if (resultData.cn && resultData.cn.st && resultData.cn.st.rt) {
              const rtArray = resultData.cn.st.rt;
              rtArray.forEach((rtItem: any) => {
                if (rtItem.ws) {
                  rtItem.ws.forEach((wsItem: any) => {
                    if (wsItem.cw) {
                      wsItem.cw.forEach((cwItem: any) => {
                        if (cwItem.w) {
                          resultText += cwItem.w;
                        }
                      });
                    }
                  });
                }
              });
            }

            // 根据讯飞文档，检查type字段过滤中间结果
            const resultType = resultData.cn?.st?.type;
            const isIntermediateResult = resultType === '1' || resultType === 1;
            const isFinalResult = resultType === '0' || resultType === 0;

            console.log('📄 提取的文本结果:', {
              文本: resultText,
              文本长度: resultText.length,
              类型: resultType,
              是否中间结果: isIntermediateResult,
              是否最终结果: isFinalResult,
              过滤状态: isIntermediateResult ? '已过滤（中间结果）' : '保留',
            });

            // 过滤掉中间结果，只处理最终结果以保证准确性
            if (resultText.trim() && !isIntermediateResult) {
              const speechMessage: SpeechRecognitionMessage = {
                text: resultText,
                list: resultData.cn?.st?.rt || [],
                type: resultType, // 添加type字段便于后续处理
              };

              console.log('✅ 调用onMessage回调（最终结果）:', speechMessage);
              this.config.onMessage?.(speechMessage);
            } else if (isIntermediateResult) {
              console.log('⚠️ 过滤中间结果，等待最终结果:', resultText);
            } else {
              console.log('⚠️ 提取的文本为空，跳过回调');
            }
          } else {
            console.log('⚠️ 转写结果中没有数据字段');
          }
        } catch (parseError) {
          console.log('❌ 解析转写结果失败:', parseError);
          console.log('原始消息数据:', message.data);
        }
      } else if (message.action === 'error') {
        console.log('❌ 讯飞服务器错误:', message);
        this.config.onError?.(message);
      } else {
        console.log('🔔 其他消息类型:', message.action, message);
      }
    } catch (error) {
      console.log('❌ 处理WebSocket消息失败:', error);
      console.log('原始消息:', event.data);
    }
  }

  private onError(e: Event): void {
    console.error('讯飞转写服务连接错误:', e);

    if (!this.isDestroyed) {
      try {
        this.config.onError && this.config.onError(e);
      } catch (error) {
        console.error('用户onError回调执行失败:', error);
      }
    }

    // 拒绝连接Promise
    const ws = this.ws as any;
    if (ws && ws._reject) {
      ws._reject(new Error('WebSocket连接错误'));
      delete ws._resolve;
      delete ws._reject;
    }

    this.handleConnectionFailure();
  }

  private onClose(e: CloseEvent): void {
    console.log('关闭连接ws.onclose, code:', e.code, 'reason:', e.reason);

    const wasConnected = this.state === 'connected';
    this.state = 'closed';

    this.stopHeartbeat();
    this.stopAudioTransmission();

    if (!this.isDestroyed) {
      try {
        this.config.onClose && this.config.onClose(e);
      } catch (error) {
        console.error('用户onClose回调执行失败:', error);
      }
    }

    // 拒绝连接Promise
    const ws = this.ws as any;
    if (ws && ws._reject) {
      ws._reject(new Error(`WebSocket连接关闭: ${e.code} ${e.reason}`));
      delete ws._resolve;
      delete ws._reject;
    }

    // 如果是异常断开且允许重连，则尝试重连
    if (
      wasConnected &&
      !this.isDestroyed &&
      e.code !== 1000 &&
      this.shouldReconnect()
    ) {
      this.attemptReconnect();
    }
  }

  // WebSocket连接成功后开始发送数据
  private startAudioTransmission(): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN || this.isDestroyed) {
      return;
    }

    console.log('🎵 开始音频数据传输');
    this.lastAudioSendTime = Date.now();

    // 开始定时发送音频数据
    this.handlerInterval = setInterval(() => {
      if (
        this.isDestroyed ||
        !this.ws ||
        this.ws.readyState !== WebSocket.OPEN
      ) {
        this.stopAudioTransmission();
        return;
      }

      if (this.buffer.length === 0) {
        if (this.state === 'closing') {
          try {
            this.ws.send('{"end": true}');
            console.log('📤 发送结束标识');
          } catch (error) {
            console.log('发送结束标识失败:', error);
          }
          this.stopAudioTransmission();
        } else {
          // ✅ 不再发送人工静音数据！麦克风应该持续有真实音频
          console.log('⏸️ 缓冲区为空，等待真实音频数据...');
        }
        return;
      }

      // 📋 严格按讯飞文档：发送1280字节PCM数据包
      if (this.buffer.length >= 1280) {
        const audioData = this.buffer.slice(0, 1280);
        // 从缓冲区移除已发送的数据
        this.buffer = this.buffer.slice(1280);

        try {
          this.ws.send(audioData);
          this.lastAudioSendTime = Date.now();
          console.log(
            '📤 发送PCM音频数据:',
            audioData.length,
            '字节, 缓冲区剩余:',
            this.buffer.length,
            '字节'
          );
        } catch (error) {
          console.log('发送音频数据失败:', error);
          this.stopAudioTransmission();
        }
      }
    }, 40);
  }

  // 获取WebSocket状态文本
  private getWebSocketStateText(): string {
    if (!this.ws) return 'null';
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'CONNECTING(0)';
      case WebSocket.OPEN:
        return 'OPEN(1)';
      case WebSocket.CLOSING:
        return 'CLOSING(2)';
      case WebSocket.CLOSED:
        return 'CLOSED(3)';
      default:
        return `UNKNOWN(${this.ws.readyState})`;
    }
  }

  private stopAudioTransmission(): void {
    if (this.handlerInterval) {
      clearInterval(this.handlerInterval);
      this.handlerInterval = undefined;
    }
  }

  // 心跳机制
  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        try {
          this.ws.send(JSON.stringify({ type: 'heartbeat' }));
        } catch (error) {
          console.log('发送心跳失败:', error);
          this.handleConnectionFailure();
        }
      } else {
        this.stopHeartbeat();
      }
    }, 30000); // 每30秒发送一次心跳
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = undefined;
    }
  }

  // 处理连接失败
  private handleConnectionFailure(): void {
    this.state = 'closed';
    this.cleanup();

    if (!this.isDestroyed && this.shouldReconnect()) {
      this.attemptReconnect();
    }
  }

  // 判断是否应该重连
  private shouldReconnect(): boolean {
    return this.reconnectAttempts < this.maxReconnectAttempts;
  }

  // 尝试重连
  private attemptReconnect(): void {
    if (this.isDestroyed || !this.shouldReconnect()) {
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * this.reconnectAttempts;

    console.log(`尝试第${this.reconnectAttempts}次重连，延迟${delay}ms...`);

    setTimeout(() => {
      if (!this.isDestroyed && this.shouldReconnect()) {
        this.state = 'idle';
        this.start().catch((error) => {
          console.error('重连失败:', error);
          this.handleConnectionFailure();
        });
      }
    }, delay);
  }

  // 清理WebSocket连接
  private cleanupWebSocket(): void {
    if (this.ws) {
      try {
        this.ws.removeEventListener('open', this.boundOnOpen);
        this.ws.removeEventListener('message', this.boundOnMessage);
        this.ws.removeEventListener('error', this.boundOnError);
        this.ws.removeEventListener('close', this.boundOnClose);

        if (
          this.ws.readyState === WebSocket.CONNECTING ||
          this.ws.readyState === WebSocket.OPEN
        ) {
          this.ws.close(1000, '正常关闭');
        }
      } catch (error) {
        console.error('清理WebSocket连接时出错:', error);
      }
      this.ws = undefined;
    }

    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = undefined;
    }
  }

  // 全面清理资源
  private cleanup(): void {
    this.stopAudioTransmission();
    this.stopHeartbeat();
    this.cleanupWebSocket();
    this.buffer = new Uint8Array(0);
  }

  // 处理页面可见性变化
  private handleVisibilityChange(): void {
    if (typeof document !== 'undefined') {
      if (document.hidden && this.state === 'connected') {
        console.log('页面隐藏，暂停连接');
        this.stop();
      }
    }
  }

  // 处理页面卸载
  private handleBeforeUnload(): void {
    console.log('页面即将卸载，清理资源');
    this.destroy();
  }

  // 销毁实例
  public destroy(): void {
    if (this.isDestroyed) {
      return;
    }

    console.log('销毁XunfeiRealtimeASR实例');
    this.isDestroyed = true;
    this.state = 'closed';

    this.cleanup();

    // 移除全局事件监听器
    if (typeof document !== 'undefined') {
      document.removeEventListener(
        'visibilitychange',
        this.boundHandleVisibilityChange
      );
      window.removeEventListener('beforeunload', this.boundHandleBeforeUnload);
    }
  }

  // 获取当前状态
  public getState(): string {
    return this.state;
  }

  // 获取连接状态
  public isConnected(): boolean {
    return this.state === 'connected' && this.ws?.readyState === WebSocket.OPEN;
  }
}
