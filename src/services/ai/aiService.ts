import axios, { AxiosInstance, AxiosError } from 'axios';
import { AIServiceConfig, AIResponse, AIChatMessage } from '@/types/ai';
import Toast from 'react-native-toast-message';
import { generateMessageId } from '@/utils/uuid';

class AIService {
  private client: AxiosInstance;
  private config: AIServiceConfig;
  private isConfigured: boolean = false;

  // TODO: 没用的东西
  constructor() {
    // 默认配置
    this.config = {
      apiKey: '',
      baseUrl: 'https://api.openai.com/v1',
      model: 'gpt-3.5-turbo',
      timeout: 30000,
      retryAttempts: 3,
    };

    this.client = axios.create({
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  /**
   * 配置AI服务
   */
  configure(config: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (this.config.apiKey) {
      this.client.defaults.headers.common['Authorization'] = `Bearer ${this.config.apiKey}`;
      this.client.defaults.baseURL = this.config.baseUrl;
      this.isConfigured = true;
    }
  }

  /**
   * 检查是否已配置
   */
  isReady(): boolean {
    return this.isConfigured && !!this.config.apiKey;
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        // 添加请求日志
        console.log(`[AI Service] Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[AI Service] Request error:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        console.log(`[AI Service] Response: ${response.status} ${response.statusText}`);
        return response;
      },
      (error: AxiosError) => {
        console.error('[AI Service] Response error:', error);
        return Promise.reject(this.handleError(error));
      }
    );
  }

  /**
   * 错误处理
   */
  private handleError(error: AxiosError): Error {
    if (error.response) {
      // 服务器响应错误
      const status = error.response.status;
      const data = error.response.data as any;
      
      switch (status) {
        case 401:
          return new Error('API密钥无效或已过期');
        case 429:
          return new Error('请求频率过高，请稍后重试');
        case 500:
          return new Error('AI服务暂时不可用');
        case 503:
          return new Error('AI服务维护中');
        default:
          return new Error(data?.error?.message || `请求失败 (${status})`);
      }
    } else if (error.request) {
      // 网络错误
      return new Error('网络连接失败，请检查网络设置');
    } else {
      // 其他错误
      return new Error(error.message || '未知错误');
    }
  }

  /**
   * 重试机制
   */
  private async withRetry<T>(
    operation: () => Promise<T>,
    attempts: number = this.config.retryAttempts
  ): Promise<T> {
    for (let i = 0; i < attempts; i++) {
      try {
        return await operation();
      } catch (error) {
        console.warn(`[AI Service] Attempt ${i + 1} failed:`, error);
        
        if (i === attempts - 1) {
          throw error;
        }
        
        // 指数退避
        const delay = Math.pow(2, i) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw new Error('所有重试次数已用完');
  }

  /**
   * 聊天完成
   */
  async chatCompletion(messages: AIChatMessage[], options?: Record<string, any>): Promise<AIResponse> {
    if (!this.isReady()) {
      throw new Error('AI服务未配置，请先设置API密钥');
    }

    try {
      const response = await this.withRetry(async () => {
        return await this.client.post('/chat/completions', {
          model: this.config.model,
          messages: messages.map(msg => ({
            role: msg.role,
            content: msg.content,
          })),
          max_tokens: options?.maxTokens || 1000,
          temperature: options?.temperature || 0.7,
          ...options,
        });
      });

      const choice = response.data.choices[0];
      const usage = response.data.usage;

      return {
        success: true,
        data: choice.message.content,
        usage: {
          tokens: usage.total_tokens,
          cost: this.calculateCost(usage.total_tokens),
        },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '聊天服务出错';
      
      Toast.show({
        type: 'error',
        text1: 'AI服务错误',
        text2: errorMessage,
      });

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 文档总结
   */
  async summarizeDocument(content: string, options?: Record<string, any>): Promise<AIResponse> {
    const messages: AIChatMessage[] = [
      {
        id: Date.now().toString(),
        role: 'system',
        content: '你是一个专业的文档总结助手。请为用户提供准确、简洁的文档摘要。',
        timestamp: new Date(),
      },
      {
        id: (Date.now() + 1).toString(),
        role: 'user',
        content: `请总结以下文档内容：\n\n${content}`,
        timestamp: new Date(),
      },
    ];

    return this.chatCompletion(messages, {
      maxTokens: options?.maxTokens || 500,
      temperature: 0.3,
    });
  }

  /**
   * 内容翻译
   */
  async translateContent(content: string, targetLanguage: string = '中文'): Promise<AIResponse> {
    const messages: AIChatMessage[] = [
      {
        id: Date.now().toString(),
        role: 'system',
        content: `你是一个专业的翻译助手。请将用户提供的内容翻译成${targetLanguage}，保持原意不变。`,
        timestamp: new Date(),
      },
      {
        id: (Date.now() + 1).toString(),
        role: 'user',
        content: content,
        timestamp: new Date(),
      },
    ];

    return this.chatCompletion(messages, {
      maxTokens: 1000,
      temperature: 0.2,
    });
  }

  /**
   * 写作辅助
   */
  async assistWriting(prompt: string, context?: string): Promise<AIResponse> {
    const messages: AIChatMessage[] = [
      {
        id: Date.now().toString(),
        role: 'system',
        content: '你是一个专业的写作助手。请根据用户的要求，提供高质量的写作建议或内容生成。',
        timestamp: new Date(),
      },
    ];

    if (context) {
      messages.push({
        id: (Date.now() + 1).toString(),
        role: 'user',
        content: `背景信息：${context}`,
        timestamp: new Date(),
      });
    }

    messages.push({
      id: (Date.now() + 2).toString(),
      role: 'user',
      content: prompt,
      timestamp: new Date(),
    });

    return this.chatCompletion(messages, {
      maxTokens: 1500,
      temperature: 0.8,
    });
  }

  /**
   * 文档问答
   */
  async askDocument(question: string, documentContent: string): Promise<AIResponse> {
    const messages: AIChatMessage[] = [
      {
        id: Date.now().toString(),
        role: 'system',
        content: '你是一个文档问答助手。请基于提供的文档内容回答用户的问题，如果文档中没有相关信息，请明确说明。',
        timestamp: new Date(),
      },
      {
        id: (Date.now() + 1).toString(),
        role: 'user',
        content: `文档内容：\n${documentContent}\n\n问题：${question}`,
        timestamp: new Date(),
      },
    ];

    return this.chatCompletion(messages, {
      maxTokens: 800,
      temperature: 0.1,
    });
  }

  /**
   * 内容分析
   */
  async analyzeContent(content: string, analysisType: 'sentiment' | 'keywords' | 'structure' = 'structure'): Promise<AIResponse> {
    let systemPrompt = '';
    
    switch (analysisType) {
      case 'sentiment':
        systemPrompt = '你是一个情感分析专家。请分析文本的情感倾向（正面、负面、中性）并给出理由。';
        break;
      case 'keywords':
        systemPrompt = '你是一个关键词提取专家。请提取文本中的关键词和主题，按重要性排序。';
        break;
      case 'structure':
        systemPrompt = '你是一个文档结构分析专家。请分析文档的结构、逻辑层次和改进建议。';
        break;
    }

    const messages: AIChatMessage[] = [
      {
        id: generateMessageId(),
        role: 'system',
        content: systemPrompt,
        timestamp: new Date(),
      },
      {
        id: generateMessageId(),
        role: 'user',
        content: content,
        timestamp: new Date(),
      },
    ];

    return this.chatCompletion(messages, {
      maxTokens: 600,
      temperature: 0.3,
    });
  }

  /**
   * 计算成本（模拟）
   */
  private calculateCost(tokens: number): number {
    // 假设每1000个token成本为0.002美元
    return (tokens / 1000) * 0.002;
  }

  /**
   * 获取使用统计
   */
  async getUsageStats(): Promise<{
    totalTokens: number;
    totalCost: number;
    requestCount: number;
  }> {
    // 这里应该从本地存储或后端API获取使用统计
    return {
      totalTokens: 0,
      totalCost: 0,
      requestCount: 0,
    };
  }

  /**
   * 清理资源
   */
  destroy(): void {
    // 清理定时器、取消请求等
    this.client.defaults.timeout = 1000; // 快速超时
  }
}

// 创建单例实例
export const aiService = new AIService();

// 默认导出
export default aiService;