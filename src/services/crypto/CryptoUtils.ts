/**
 * React Native 兼容的加密工具类
 * 使用原生 JavaScript 实现 MD5 和 HMAC-SHA1
 */

/**
 * MD5 哈希实现
 */
class MD5 {
  private static rotateLeft(value: number, amount: number): number {
    return (value << amount) | (value >>> (32 - amount));
  }

  private static addUnsigned(x: number, y: number): number {
    const x4 = x & 0x40000000;
    const y4 = y & 0x40000000;
    const x8 = x & 0x80000000;
    const y8 = y & 0x80000000;
    const result = (x & 0x3fffffff) + (y & 0x3fffffff);

    if (x4 & y4) {
      return result ^ 0x80000000 ^ x8 ^ y8;
    }
    if (x4 | y4) {
      if (result & 0x40000000) {
        return result ^ 0xc0000000 ^ x8 ^ y8;
      } else {
        return result ^ 0x40000000 ^ x8 ^ y8;
      }
    } else {
      return result ^ x8 ^ y8;
    }
  }

  private static f(x: number, y: number, z: number): number {
    return (x & y) | (~x & z);
  }

  private static g(x: number, y: number, z: number): number {
    return (x & z) | (y & ~z);
  }

  private static h(x: number, y: number, z: number): number {
    return x ^ y ^ z;
  }

  private static i(x: number, y: number, z: number): number {
    return y ^ (x | ~z);
  }

  private static ff(
    a: number,
    b: number,
    c: number,
    d: number,
    x: number,
    s: number,
    ac: number
  ): number {
    a = this.addUnsigned(
      a,
      this.addUnsigned(this.addUnsigned(this.f(b, c, d), x), ac)
    );
    return this.addUnsigned(this.rotateLeft(a, s), b);
  }

  private static gg(
    a: number,
    b: number,
    c: number,
    d: number,
    x: number,
    s: number,
    ac: number
  ): number {
    a = this.addUnsigned(
      a,
      this.addUnsigned(this.addUnsigned(this.g(b, c, d), x), ac)
    );
    return this.addUnsigned(this.rotateLeft(a, s), b);
  }

  private static hh(
    a: number,
    b: number,
    c: number,
    d: number,
    x: number,
    s: number,
    ac: number
  ): number {
    a = this.addUnsigned(
      a,
      this.addUnsigned(this.addUnsigned(this.h(b, c, d), x), ac)
    );
    return this.addUnsigned(this.rotateLeft(a, s), b);
  }

  private static ii(
    a: number,
    b: number,
    c: number,
    d: number,
    x: number,
    s: number,
    ac: number
  ): number {
    a = this.addUnsigned(
      a,
      this.addUnsigned(this.addUnsigned(this.i(b, c, d), x), ac)
    );
    return this.addUnsigned(this.rotateLeft(a, s), b);
  }

  private static convertToWordArray(str: string): number[] {
    const wordArray: number[] = [];

    for (let j = 0; j < str.length * 8; j += 8) {
      wordArray[j >> 5] |= (str.charCodeAt(j / 8) & 0xff) << j % 32;
    }

    return wordArray;
  }

  private static wordToHex(value: number): string {
    let result = '';
    for (let i = 0; i <= 3; i++) {
      const byte = (value >>> (i * 8)) & 0xff;
      result += ('0' + byte.toString(16)).slice(-2);
    }
    return result;
  }

  static hash(str: string): string {
    const x = this.convertToWordArray(str);
    const len = str.length * 8;

    x[len >> 5] |= 0x80 << len % 32;
    x[(((len + 64) >>> 9) << 4) + 14] = len;

    let a = 0x67452301;
    let b = 0xefcdab89;
    let c = 0x98badcfe;
    let d = 0x10325476;

    for (let i = 0; i < x.length; i += 16) {
      const olda = a;
      const oldb = b;
      const oldc = c;
      const oldd = d;

      a = this.ff(a, b, c, d, x[i], 7, 0xd76aa478);
      d = this.ff(d, a, b, c, x[i + 1], 12, 0xe8c7b756);
      c = this.ff(c, d, a, b, x[i + 2], 17, 0x242070db);
      b = this.ff(b, c, d, a, x[i + 3], 22, 0xc1bdceee);
      a = this.ff(a, b, c, d, x[i + 4], 7, 0xf57c0faf);
      d = this.ff(d, a, b, c, x[i + 5], 12, 0x4787c62a);
      c = this.ff(c, d, a, b, x[i + 6], 17, 0xa8304613);
      b = this.ff(b, c, d, a, x[i + 7], 22, 0xfd469501);
      a = this.ff(a, b, c, d, x[i + 8], 7, 0x698098d8);
      d = this.ff(d, a, b, c, x[i + 9], 12, 0x8b44f7af);
      c = this.ff(c, d, a, b, x[i + 10], 17, 0xffff5bb1);
      b = this.ff(b, c, d, a, x[i + 11], 22, 0x895cd7be);
      a = this.ff(a, b, c, d, x[i + 12], 7, 0x6b901122);
      d = this.ff(d, a, b, c, x[i + 13], 12, 0xfd987193);
      c = this.ff(c, d, a, b, x[i + 14], 17, 0xa679438e);
      b = this.ff(b, c, d, a, x[i + 15], 22, 0x49b40821);

      a = this.gg(a, b, c, d, x[i + 1], 5, 0xf61e2562);
      d = this.gg(d, a, b, c, x[i + 6], 9, 0xc040b340);
      c = this.gg(c, d, a, b, x[i + 11], 14, 0x265e5a51);
      b = this.gg(b, c, d, a, x[i], 20, 0xe9b6c7aa);
      a = this.gg(a, b, c, d, x[i + 5], 5, 0xd62f105d);
      d = this.gg(d, a, b, c, x[i + 10], 9, 0x2441453);
      c = this.gg(c, d, a, b, x[i + 15], 14, 0xd8a1e681);
      b = this.gg(b, c, d, a, x[i + 4], 20, 0xe7d3fbc8);
      a = this.gg(a, b, c, d, x[i + 9], 5, 0x21e1cde6);
      d = this.gg(d, a, b, c, x[i + 14], 9, 0xc33707d6);
      c = this.gg(c, d, a, b, x[i + 3], 14, 0xf4d50d87);
      b = this.gg(b, c, d, a, x[i + 8], 20, 0x455a14ed);
      a = this.gg(a, b, c, d, x[i + 13], 5, 0xa9e3e905);
      d = this.gg(d, a, b, c, x[i + 2], 9, 0xfcefa3f8);
      c = this.gg(c, d, a, b, x[i + 7], 14, 0x676f02d9);
      b = this.gg(b, c, d, a, x[i + 12], 20, 0x8d2a4c8a);

      a = this.hh(a, b, c, d, x[i + 5], 4, 0xfffa3942);
      d = this.hh(d, a, b, c, x[i + 8], 11, 0x8771f681);
      c = this.hh(c, d, a, b, x[i + 11], 16, 0x6d9d6122);
      b = this.hh(b, c, d, a, x[i + 14], 23, 0xfde5380c);
      a = this.hh(a, b, c, d, x[i + 1], 4, 0xa4beea44);
      d = this.hh(d, a, b, c, x[i + 4], 11, 0x4bdecfa9);
      c = this.hh(c, d, a, b, x[i + 7], 16, 0xf6bb4b60);
      b = this.hh(b, c, d, a, x[i + 10], 23, 0xbebfbc70);
      a = this.hh(a, b, c, d, x[i + 13], 4, 0x289b7ec6);
      d = this.hh(d, a, b, c, x[i], 11, 0xeaa127fa);
      c = this.hh(c, d, a, b, x[i + 3], 16, 0xd4ef3085);
      b = this.hh(b, c, d, a, x[i + 6], 23, 0x4881d05);
      a = this.hh(a, b, c, d, x[i + 9], 4, 0xd9d4d039);
      d = this.hh(d, a, b, c, x[i + 12], 11, 0xe6db99e5);
      c = this.hh(c, d, a, b, x[i + 15], 16, 0x1fa27cf8);
      b = this.hh(b, c, d, a, x[i + 2], 23, 0xc4ac5665);

      a = this.ii(a, b, c, d, x[i], 6, 0xf4292244);
      d = this.ii(d, a, b, c, x[i + 7], 10, 0x432aff97);
      c = this.ii(c, d, a, b, x[i + 14], 15, 0xab9423a7);
      b = this.ii(b, c, d, a, x[i + 5], 21, 0xfc93a039);
      a = this.ii(a, b, c, d, x[i + 12], 6, 0x655b59c3);
      d = this.ii(d, a, b, c, x[i + 3], 10, 0x8f0ccc92);
      c = this.ii(c, d, a, b, x[i + 10], 15, 0xffeff47d);
      b = this.ii(b, c, d, a, x[i + 1], 21, 0x85845dd1);
      a = this.ii(a, b, c, d, x[i + 8], 6, 0x6fa87e4f);
      d = this.ii(d, a, b, c, x[i + 15], 10, 0xfe2ce6e0);
      c = this.ii(c, d, a, b, x[i + 6], 15, 0xa3014314);
      b = this.ii(b, c, d, a, x[i + 13], 21, 0x4e0811a1);
      a = this.ii(a, b, c, d, x[i + 4], 6, 0xf7537e82);
      d = this.ii(d, a, b, c, x[i + 11], 10, 0xbd3af235);
      c = this.ii(c, d, a, b, x[i + 2], 15, 0x2ad7d2bb);
      b = this.ii(b, c, d, a, x[i + 9], 21, 0xeb86d391);

      a = this.addUnsigned(a, olda);
      b = this.addUnsigned(b, oldb);
      c = this.addUnsigned(c, oldc);
      d = this.addUnsigned(d, oldd);
    }

    return (
      this.wordToHex(a) +
      this.wordToHex(b) +
      this.wordToHex(c) +
      this.wordToHex(d)
    ).toLowerCase();
  }
}

/**
 * SHA1 哈希实现
 */
class SHA1 {
  private static rotateLeft(n: number, s: number): number {
    return (n << s) | (n >>> (32 - s));
  }

  private static toHexStr(n: number): string {
    let s = '';
    for (let i = 7; i >= 0; i--) {
      const v = (n >>> (i * 4)) & 0x0f;
      s += v.toString(16);
    }
    return s;
  }

  static hash(msg: string): string {
    const msgLength = msg.length;
    const wordArray: number[] = [];

    for (let i = 0; i < msgLength - 3; i += 4) {
      const j =
        (msg.charCodeAt(i) << 24) |
        (msg.charCodeAt(i + 1) << 16) |
        (msg.charCodeAt(i + 2) << 8) |
        msg.charCodeAt(i + 3);
      wordArray.push(j);
    }

    switch (msgLength % 4) {
      case 0:
        wordArray.push(0x080000000);
        break;
      case 1:
        wordArray.push((msg.charCodeAt(msgLength - 1) << 24) | 0x0800000);
        break;
      case 2:
        wordArray.push(
          (msg.charCodeAt(msgLength - 2) << 24) |
            (msg.charCodeAt(msgLength - 1) << 16) |
            0x08000
        );
        break;
      case 3:
        wordArray.push(
          (msg.charCodeAt(msgLength - 3) << 24) |
            (msg.charCodeAt(msgLength - 2) << 16) |
            (msg.charCodeAt(msgLength - 1) << 8) |
            0x80
        );
        break;
    }

    while (wordArray.length % 16 !== 14) {
      wordArray.push(0);
    }

    wordArray.push(msgLength >>> 29);
    wordArray.push((msgLength << 3) & 0x0ffffffff);

    const w = new Array(80);
    let H0 = 0x67452301;
    let H1 = 0xefcdab89;
    let H2 = 0x98badcfe;
    let H3 = 0x10325476;
    let H4 = 0xc3d2e1f0;

    for (let i = 0; i < wordArray.length; i += 16) {
      for (let j = 0; j < 16; j++) {
        w[j] = wordArray[i + j];
      }

      for (let j = 16; j <= 79; j++) {
        w[j] = this.rotateLeft(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);
      }

      let A = H0;
      let B = H1;
      let C = H2;
      let D = H3;
      let E = H4;

      for (let j = 0; j <= 19; j++) {
        const temp =
          (this.rotateLeft(A, 5) +
            ((B & C) | (~B & D)) +
            E +
            w[j] +
            0x5a827999) &
          0x0ffffffff;
        E = D;
        D = C;
        C = this.rotateLeft(B, 30);
        B = A;
        A = temp;
      }

      for (let j = 20; j <= 39; j++) {
        const temp =
          (this.rotateLeft(A, 5) + (B ^ C ^ D) + E + w[j] + 0x6ed9eba1) &
          0x0ffffffff;
        E = D;
        D = C;
        C = this.rotateLeft(B, 30);
        B = A;
        A = temp;
      }

      for (let j = 40; j <= 59; j++) {
        const temp =
          (this.rotateLeft(A, 5) +
            ((B & C) | (B & D) | (C & D)) +
            E +
            w[j] +
            0x8f1bbcdc) &
          0x0ffffffff;
        E = D;
        D = C;
        C = this.rotateLeft(B, 30);
        B = A;
        A = temp;
      }

      for (let j = 60; j <= 79; j++) {
        const temp =
          (this.rotateLeft(A, 5) + (B ^ C ^ D) + E + w[j] + 0xca62c1d6) &
          0x0ffffffff;
        E = D;
        D = C;
        C = this.rotateLeft(B, 30);
        B = A;
        A = temp;
      }

      H0 = (H0 + A) & 0x0ffffffff;
      H1 = (H1 + B) & 0x0ffffffff;
      H2 = (H2 + C) & 0x0ffffffff;
      H3 = (H3 + D) & 0x0ffffffff;
      H4 = (H4 + E) & 0x0ffffffff;
    }

    return (
      this.toHexStr(H0) +
      this.toHexStr(H1) +
      this.toHexStr(H2) +
      this.toHexStr(H3) +
      this.toHexStr(H4)
    );
  }
}

/**
 * 创建 MD5 哈希
 */
export function createMd5Hash(input: string): string {
  return MD5.hash(input);
}

/**
 * 创建 HMAC-SHA1 签名
 */
export function createHmacSha1(message: string, key: string): string {
  const blockSize = 64;

  // 如果密钥长度大于块大小，先进行哈希并转换为字节
  if (key.length > blockSize) {
    const hashedKey = SHA1.hash(key);
    // 将十六进制字符串转换为字节字符串
    key =
      hashedKey
        .match(/.{2}/g)
        ?.map((byte) => String.fromCharCode(parseInt(byte, 16)))
        .join('') || '';
  }

  // 填充密钥到块大小
  while (key.length < blockSize) {
    key += '\0';
  }

  // 创建内部和外部填充
  let ipad = '';
  let opad = '';

  for (let i = 0; i < blockSize; i++) {
    ipad += String.fromCharCode(key.charCodeAt(i) ^ 0x36);
    opad += String.fromCharCode(key.charCodeAt(i) ^ 0x5c);
  }

  // 计算 HMAC
  const innerHash = SHA1.hash(ipad + message);
  // 注意：innerHash是十六进制字符串，需要转换为字节再进行外层哈希
  const innerHashBytes =
    innerHash
      .match(/.{2}/g)
      ?.map((byte) => String.fromCharCode(parseInt(byte, 16)))
      .join('') || '';
  const outerHash = SHA1.hash(opad + innerHashBytes);

  // 将十六进制结果转换为字节数组，然后转换为 Base64
  const bytes =
    outerHash
      .match(/.{2}/g)
      ?.map((byte) => String.fromCharCode(parseInt(byte, 16)))
      .join('') || '';
  return btoa(bytes);
}
