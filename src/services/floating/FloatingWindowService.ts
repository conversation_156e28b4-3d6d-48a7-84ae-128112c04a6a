import { NativeModules, NativeEventEmitter, Platform } from 'react-native';

/**
 * 浮窗数据接口
 */
export interface FloatingWindowData {
  duration: number;
  isRecording: boolean;
  isPaused: boolean;
}

/**
 * 浮窗事件类型
 */
export type FloatingWindowEventType =
  | 'onFloatingWindowPress'
  | 'onFloatingWindowStop';

/**
 * 浮窗服务类
 * 管理系统级浮窗显示和交互
 */
class FloatingWindowService {
  private eventEmitter: NativeEventEmitter | null = null;
  private listeners: Map<string, (data?: any) => void> = new Map();

  constructor() {
    if (Platform.OS === 'android' && NativeModules.FloatingWindowModule) {
      this.eventEmitter = new NativeEventEmitter(
        NativeModules.FloatingWindowModule
      );
    }
  }

  /**
   * 检查是否支持浮窗功能
   */
  isSupported(): boolean {
    return Platform.OS === 'android' && !!NativeModules.FloatingWindowModule;
  }

  /**
   * 检查是否有浮窗权限
   */
  async hasOverlayPermission(): Promise<boolean> {
    if (!this.isSupported()) {
      return false;
    }

    try {
      return await NativeModules.FloatingWindowModule.hasOverlayPermission();
    } catch (error) {
      console.error('检查浮窗权限失败:', error);
      return false;
    }
  }

  /**
   * 请求浮窗权限
   */
  async requestOverlayPermission(): Promise<boolean> {
    if (!this.isSupported()) {
      return false;
    }

    try {
      return await NativeModules.FloatingWindowModule.requestOverlayPermission();
    } catch (error) {
      console.error('请求浮窗权限失败:', error);
      return false;
    }
  }

  /**
   * 显示浮窗
   */
  async showFloatingWindow(data: FloatingWindowData): Promise<boolean> {
    if (!this.isSupported()) {
      console.warn('当前平台不支持浮窗功能');
      return false;
    }

    try {
      return await NativeModules.FloatingWindowModule.showFloatingWindow(data);
    } catch (error) {
      console.error('显示浮窗失败:', error);
      return false;
    }
  }

  /**
   * 隐藏浮窗
   */
  async hideFloatingWindow(): Promise<boolean> {
    if (!this.isSupported()) {
      return false;
    }

    try {
      return await NativeModules.FloatingWindowModule.hideFloatingWindow();
    } catch (error) {
      console.error('隐藏浮窗失败:', error);
      return false;
    }
  }

  /**
   * 更新浮窗内容
   */
  async updateFloatingWindow(data: FloatingWindowData): Promise<boolean> {
    if (!this.isSupported()) {
      return false;
    }

    try {
      return await NativeModules.FloatingWindowModule.updateFloatingWindow(
        data
      );
    } catch (error) {
      console.error('更新浮窗失败:', error);
      return false;
    }
  }

  /**
   * 添加事件监听器
   */
  addEventListener(
    eventType: FloatingWindowEventType,
    listener: (data?: any) => void
  ): void {
    if (!this.eventEmitter) {
      return;
    }

    const subscription = this.eventEmitter.addListener(eventType, listener);
    this.listeners.set(eventType, listener);

    // 返回取消订阅函数
    return () => {
      subscription.remove();
      this.listeners.delete(eventType);
    };
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(eventType: FloatingWindowEventType): void {
    if (!this.eventEmitter) {
      return;
    }

    this.eventEmitter.removeAllListeners(eventType);
    this.listeners.delete(eventType);
  }

  /**
   * 移除所有事件监听器
   */
  removeAllListeners(): void {
    if (!this.eventEmitter) {
      return;
    }

    this.listeners.forEach((_, eventType) => {
      this.eventEmitter!.removeAllListeners(eventType);
    });
    this.listeners.clear();
  }
}

// 导出单例实例
export const floatingWindowService = new FloatingWindowService();

// 导出类型
export default FloatingWindowService;
