/**
 * 原生定位服务
 * 用于在没有 Google Play Services 的设备上获取定位
 */
import { NativeModules, Platform } from 'react-native';

export interface NativeLocationOptions {
  accuracy?: number; // 1: 低精度, 2: 中精度, 3: 高精度
  timeout?: number; // 超时时间（毫秒）
  maximumAge?: number; // 最大缓存时间（毫秒）
}

export interface NativeLocationCoords {
  latitude: number;
  longitude: number;
  altitude: number;
  accuracy: number;
  heading: number;
  speed: number;
}

export interface NativeLocationResult {
  coords: NativeLocationCoords;
  timestamp: number;
}

class NativeLocationService {
  private nativeModule: any;

  constructor() {
    this.nativeModule = NativeModules.NativeLocationModule;
  }

  /**
   * 检查原生定位模块是否可用
   */
  isAvailable(): boolean {
    return Platform.OS === 'android' && this.nativeModule != null;
  }

  /**
   * 获取当前位置
   */
  async getCurrentLocation(
    options: NativeLocationOptions = {}
  ): Promise<NativeLocationResult> {
    if (!this.isAvailable()) {
      throw new Error('Native location module is not available');
    }

    const defaultOptions = {
      accuracy: 1, // 默认低精度
      timeout: 15000, // 15秒超时
      maximumAge: 30000, // 30秒缓存
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
      const location = await this.nativeModule.getCurrentLocation(finalOptions);
      return location;
    } catch (error) {
      console.error('Native location error:', error);
      throw error;
    }
  }

  /**
   * 获取最后已知位置
   */
  async getLastKnownLocation(): Promise<NativeLocationResult | null> {
    if (!this.isAvailable()) {
      throw new Error('Native location module is not available');
    }

    try {
      const location = await this.nativeModule.getLastKnownLocation();
      return location;
    } catch (error) {
      console.error('Get last known location error:', error);
      return null;
    }
  }

  /**
   * 检查定位服务是否开启
   */
  async hasServicesEnabled(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      return await this.nativeModule.hasServicesEnabled();
    } catch (error) {
      console.error('Check services enabled error:', error);
      return false;
    }
  }

  /**
   * 检查定位权限
   */
  async hasLocationPermissions(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      return await this.nativeModule.hasLocationPermissions();
    } catch (error) {
      console.error('Check permissions error:', error);
      return false;
    }
  }
}

export default new NativeLocationService();
