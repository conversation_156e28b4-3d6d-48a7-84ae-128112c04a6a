import { createRef } from 'react';
import {
  NavigationContainerRef,
  CommonActions,
  StackActions,
} from '@react-navigation/native';

// 创建一个可以在应用程序的任何地方使用的导航引用
export const navigationRef = createRef<NavigationContainerRef<any>>();

/**
 * 导航到指定的路由
 * @param name 路由名称
 * @param params 路由参数
 */
export function navigate(name: string, params?: object) {
  if (navigationRef.current) {
    navigationRef.current.dispatch(StackActions.push(name, params));
  } else {
    // 导航可能还没有准备好，可以在这里添加一些后备逻辑
    console.warn('导航尚未准备好，无法导航到:', name);
  }
}

/**
 * 重置导航堆栈并导航到特定路由
 * @param name 路由名称
 * @param params 路由参数
 */
export function resetAndNavigateTo(name: string, params?: object) {
  if (navigationRef.current) {
    navigationRef.current.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name, params }],
      })
    );
  } else {
    console.warn('导航尚未准备好，无法重置并导航到:', name);
  }
}

/**
 * 返回上一屏幕
 */
export function goBack() {
  if (navigationRef.current) {
    navigationRef.current.goBack();
  } else {
    console.warn('导航尚未准备好，无法返回');
  }
}
