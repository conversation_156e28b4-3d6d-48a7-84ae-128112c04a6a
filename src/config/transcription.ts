// 讯飞实时语音转写配置
// 请在讯飞开放平台申请应用并获取以下配置信息
// https://www.xfyun.cn/

export const XUNFEI_CONFIG = {
  // 讯飞应用ID - 请替换为您的真实AppId（通常是8位字母数字组合）
  appId: 'a1feaee4', // 移除下划线占位符

  // 讯飞API密钥（实时转写使用apiKey）
  apiKey: 'c25f6d5492b8091e689db549c6d086da',
};

// 检查配置是否有效 - 增加更严格的验证
export const isTranscriptionConfigValid = () => {
  // 基本验证
  const basicValid =
    XUNFEI_CONFIG.appId !== 'your_app_id_here' &&
    XUNFEI_CONFIG.apiKey !== 'your_api_key_here' &&
    XUNFEI_CONFIG.appId.length > 0 &&
    XUNFEI_CONFIG.apiKey.length > 0;

  // 严格验证 - AppId格式检查
  const appIdValid = /^[a-zA-Z0-9]{8}$/.test(XUNFEI_CONFIG.appId); // 8位字母数字
  const noPlaceholders =
    !XUNFEI_CONFIG.appId.includes('_') && !XUNFEI_CONFIG.apiKey.includes('_');

  const isValid = basicValid && appIdValid && noPlaceholders;

  if (!isValid) {
    console.warn('⚠️ 讯飞配置验证失败:', {
      basicValid,
      appIdValid,
      noPlaceholders,
      appIdFormat: XUNFEI_CONFIG.appId,
      appIdLength: XUNFEI_CONFIG.appId.length,
    });
  }

  return isValid;
};
