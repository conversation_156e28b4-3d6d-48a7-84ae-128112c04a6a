// 讯飞实时语音转写配置示例
// 复制此文件为 transcription.ts 并填入您的实际配置

export const XUNFEI_CONFIG = {
  // 讯飞应用ID - 从讯飞开放平台控制台获取
  // 示例: '5f8a1b2c'
  appId: 'your_app_id_here',

  // 讯飞API密钥 - 从讯飞开放平台控制台获取
  // 示例: 'd9f4aa7ea6d94faca62cd88a28fd5234'
  apiKey: 'your_api_key_here',
};

// 检查配置是否有效
export const isTranscriptionConfigValid = () => {
  return (
    XUNFEI_CONFIG.appId !== 'your_app_id_here' &&
    XUNFEI_CONFIG.apiKey !== 'your_api_key_here' &&
    XUNFEI_CONFIG.appId.length > 0 &&
    XUNFEI_CONFIG.apiKey.length > 0
  );
};

// 配置获取步骤：
// 1. 访问 https://www.xfyun.cn/
// 2. 注册并登录账号
// 3. 进入控制台 -> 我的应用
// 4. 创建新应用或选择现有应用
// 5. 添加"实时语音转写"服务
// 6. 在应用详情页面获取 appId 和 apiKey
