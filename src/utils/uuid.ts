import { v4 as uuidv4 } from 'uuid';

/**
 * 统一的UUID生成工具
 * 使用专业的uuid库替换项目中所有的ID生成方法
 */

/**
 * 生成标准的UUID v4
 * 用于需要全局唯一标识符的场景
 */
export const generateUUID = (): string => {
  return uuidv4();
};

/**
 * 生成录音专用ID
 * 格式: rec_[uuid]，便于识别和调试
 */
export const generateRecordingId = (): string => {
  return `rec_${uuidv4()}`;
};

/**
 * 生成聊天会话ID
 * 格式: chat_[uuid]，便于识别和调试
 */
export const generateChatSessionId = (): string => {
  return `chat_${uuidv4()}`;
};

/**
 * 生成聊天消息ID
 * 格式: msg_[uuid]，便于识别和调试
 */
export const generateMessageId = (): string => {
  return `msg_${uuidv4()}`;
};

/**
 * 生成客户端请求ID
 * 用于API请求的客户端标识
 */
export const generateClientId = (): string => {
  return uuidv4();
};

/**
 * 生成通用唯一ID
 * 兼容原有的generateId方法，但使用更安全的UUID
 * @deprecated 建议使用更具体的ID生成方法
 */
export const generateId = (): string => {
  return uuidv4();
};
