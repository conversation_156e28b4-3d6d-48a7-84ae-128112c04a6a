import { updateKnowledgeStorageName } from '@/stores/knowledgeStore';
import { updateRecordingStorageName } from '@/stores/recordingStore';
import { updateAIStorageName } from '@/stores/aiStore';
import { getUserId } from './userIdUtils';
import { checkStorageConsistency, printStorageReport } from './storageConsistency';

/**
 * 初始化用户存储
 * 当用户登录或应用启动时调用此函数，以确保所有存储使用正确的用户ID
 */
export const initUserStorage = async (): Promise<void> => {
  try {
    // 获取当前用户ID
    const userId = await getUserId();

    // 确保用户ID被记录在日志中，方便调试
    console.log('🔄 初始化用户存储: 用户ID =', userId);

    // 更新所有用户隔离的存储名称
    await updateKnowledgeStorageName();
    await updateRecordingStorageName();
    await updateAIStorageName();

    // 清理缓存或执行其他需要的初始化操作
    console.log('✅ 用户存储初始化成功');
  } catch (error) {
    console.error('❌ 初始化用户存储失败:', error);
    throw error; // 重新抛出错误，让调用者知道初始化失败
  }
};

/**
 * 增强的用户存储初始化（包含一致性检查）
 * 推荐在关键场景下使用此函数
 */
export const initUserStorageWithValidation = async (): Promise<void> => {
  try {
    console.log('🚀 开始增强的用户存储初始化...');

    // 1. 执行标准初始化
    await initUserStorage();

    // 2. 执行一致性检查
    console.log('🔍 执行存储一致性检查...');
    const consistencyReport = await checkStorageConsistency();

    if (!consistencyReport.isConsistent) {
      console.warn('⚠️ 存储一致性检查发现问题');
      await printStorageReport();

      // 不抛出错误，只是警告，因为大多数情况下应用仍然可以正常工作
      console.warn('⚠️ 存储初始化完成，但存在一致性问题，请检查日志');
    } else {
      console.log('✅ 存储初始化并验证成功');
    }

  } catch (error) {
    console.error('❌ 增强的用户存储初始化失败:', error);

    // 尝试打印诊断信息
    try {
      await printStorageReport();
    } catch (reportError) {
      console.error('无法生成存储报告:', reportError);
    }

    throw error;
  }
};

/**
 * 轻量级的存储健康检查
 * 适用于应用运行时的定期检查
 */
export const quickStorageHealthCheck = async (): Promise<boolean> => {
  try {
    const consistencyReport = await checkStorageConsistency();

    if (!consistencyReport.isConsistent) {
      console.warn('⚠️ 存储健康检查发现问题，建议重新初始化');
      return false;
    }

    console.log('✅ 存储健康检查通过');
    return true;
  } catch (error) {
    console.error('❌ 存储健康检查失败:', error);
    return false;
  }
};
