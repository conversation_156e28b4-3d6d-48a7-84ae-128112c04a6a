import AsyncStorage from '@react-native-async-storage/async-storage';
import { getUserId, generateStorageName } from './userIdUtils';

/**
 * 存储一致性验证和修复工具
 * 确保所有用户相关的存储都使用正确的用户ID
 */

export interface StorageConsistencyReport {
  userId: number;
  isConsistent: boolean;
  storageStates: {
    [key: string]: {
      expectedKey: string;
      actualKey: string;
      exists: boolean;
      consistent: boolean;
    };
  };
  errors: string[];
  timestamp: Date;
}

/**
 * 需要进行用户隔离的存储列表
 */
const USER_ISOLATED_STORAGES = [
  'knowledge-storage',
  'recording-storage', 
  'ai-storage'
] as const;

/**
 * 全局共享的存储列表（不需要用户隔离）
 */
const GLOBAL_STORAGES = [
  'app-storage',
  'auth-storage'
] as const;

/**
 * 检查存储一致性
 */
export const checkStorageConsistency = async (): Promise<StorageConsistencyReport> => {
  const report: StorageConsistencyReport = {
    userId: await getUserId(),
    isConsistent: true,
    storageStates: {},
    errors: [],
    timestamp: new Date()
  };

  try {
    console.log(`🔍 开始存储一致性检查，用户ID: ${report.userId}`);

    // 检查用户隔离的存储
    for (const storageName of USER_ISOLATED_STORAGES) {
      try {
        const expectedKey = await generateStorageName(storageName);
        const data = await AsyncStorage.getItem(expectedKey);
        const exists = data !== null;
        
        report.storageStates[storageName] = {
          expectedKey,
          actualKey: expectedKey,
          exists,
          consistent: true // 如果能正确生成key，就认为是一致的
        };

        console.log(`✅ ${storageName}: ${expectedKey} (存在: ${exists})`);
      } catch (error) {
        const errorMsg = `检查 ${storageName} 失败: ${error.message}`;
        report.errors.push(errorMsg);
        report.isConsistent = false;
        
        report.storageStates[storageName] = {
          expectedKey: 'ERROR',
          actualKey: 'ERROR',
          exists: false,
          consistent: false
        };
        
        console.error(`❌ ${errorMsg}`);
      }
    }

    // 检查全局存储（确保它们没有被错误地用户隔离）
    for (const storageName of GLOBAL_STORAGES) {
      try {
        const data = await AsyncStorage.getItem(storageName);
        const exists = data !== null;
        
        report.storageStates[storageName] = {
          expectedKey: storageName,
          actualKey: storageName,
          exists,
          consistent: true
        };

        console.log(`🌐 ${storageName}: 全局存储 (存在: ${exists})`);
      } catch (error) {
        const errorMsg = `检查全局存储 ${storageName} 失败: ${error.message}`;
        report.errors.push(errorMsg);
        console.error(`❌ ${errorMsg}`);
      }
    }

    if (report.isConsistent) {
      console.log('✅ 存储一致性检查通过');
    } else {
      console.warn('⚠️ 存储一致性检查发现问题');
    }

  } catch (error) {
    const errorMsg = `存储一致性检查失败: ${error.message}`;
    report.errors.push(errorMsg);
    report.isConsistent = false;
    console.error(`❌ ${errorMsg}`);
  }

  return report;
};

/**
 * 验证特定存储的一致性
 */
export const validateStorageKey = async (storageName: string): Promise<boolean> => {
  try {
    if (USER_ISOLATED_STORAGES.includes(storageName as any)) {
      const expectedKey = await generateStorageName(storageName);
      const userId = await getUserId();
      return expectedKey.includes(`user-${userId}`);
    } else if (GLOBAL_STORAGES.includes(storageName as any)) {
      return true; // 全局存储总是一致的
    } else {
      console.warn(`未知的存储类型: ${storageName}`);
      return false;
    }
  } catch (error) {
    console.error(`验证存储键失败: ${storageName}`, error);
    return false;
  }
};

/**
 * 获取存储健康状态摘要
 */
export const getStorageHealthSummary = async (): Promise<{
  healthy: boolean;
  userIsolatedCount: number;
  globalCount: number;
  errorCount: number;
}> => {
  const report = await checkStorageConsistency();
  
  const userIsolatedCount = USER_ISOLATED_STORAGES.length;
  const globalCount = GLOBAL_STORAGES.length;
  const errorCount = report.errors.length;
  
  return {
    healthy: report.isConsistent,
    userIsolatedCount,
    globalCount,
    errorCount
  };
};

/**
 * 打印详细的存储状态报告
 */
export const printStorageReport = async (): Promise<void> => {
  const report = await checkStorageConsistency();
  
  console.log('\n📊 存储状态报告');
  console.log('================');
  console.log(`用户ID: ${report.userId}`);
  console.log(`检查时间: ${report.timestamp.toLocaleString()}`);
  console.log(`整体状态: ${report.isConsistent ? '✅ 健康' : '❌ 有问题'}`);
  
  if (report.errors.length > 0) {
    console.log('\n❌ 错误列表:');
    report.errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
  }
  
  console.log('\n📁 存储详情:');
  Object.entries(report.storageStates).forEach(([name, state]) => {
    const status = state.consistent ? '✅' : '❌';
    const exists = state.exists ? '存在' : '不存在';
    console.log(`  ${status} ${name}: ${state.expectedKey} (${exists})`);
  });
  
  console.log('================\n');
};
