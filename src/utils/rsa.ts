import JSEncrypt from 'jsencrypt';

const PUBLIC_KEY =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCLMAV+D2c42RSHYrkMDucWLwCbNVphM3lPZ2JHnLdYQIvfJnwgb/2hSnLz5ASpeIVEgZlCHW4WuFHNFXlt4XRbuolV+dymNcXG7PwPw94XUGYw0cuD31EzfbQp+JxeigBv931Bx8CIUsqweagCdXQqCWAL4SPnZ3yu9h3ZLu3qFQIDAQAB';

export const rsaEncryptFun = (req: string): string => {
  const encrypt = new JSEncrypt();
  encrypt.setPublicKey(PUBLIC_KEY);
  const res = encrypt.encrypt(req);
  // 直接返回加密结果，不进行URL编码
  return res || '';
};
