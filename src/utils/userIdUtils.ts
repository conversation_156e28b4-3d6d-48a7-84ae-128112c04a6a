import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * 获取当前登录用户ID
 * 如果未找到用户信息，返回默认ID -1（负数避免与真实用户ID冲突）
 */
export const getUserId = async (): Promise<number> => {
  try {
    const userInfo = await AsyncStorage.getItem('userInfo');
    if (!userInfo) {
      console.warn('用户信息未找到，使用默认用户ID');
      return -1; // 使用负数，避免与真实用户ID冲突
    }
    const parsedUserInfo = JSON.parse(userInfo);
    const userId = Number(parsedUserInfo?.attributes?.userInfo?.userId);

    // 确保返回的是正数（真实用户ID）
    if (!userId || userId <= 0) {
      console.warn('用户ID无效，使用默认用户ID');
      return -1;
    }

    return userId;
  } catch (error) {
    console.error('获取用户ID失败:', error);
    return -1; // 错误情况也使用负数
  }
};

/**
 * 生成存储名称
 * @param baseName 基础存储名称
 * @returns 包含用户ID的完整存储名称
 */
export const generateStorageName = async (
  baseName: string
): Promise<string> => {
  const userId = await getUserId();
  return `${baseName}-user-${userId}`;
};
