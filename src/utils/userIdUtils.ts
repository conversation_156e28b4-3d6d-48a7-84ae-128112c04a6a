import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * 获取当前登录用户ID
 * 如果未找到用户信息，返回默认ID 1
 */
export const getUserId = async (): Promise<number> => {
  try {
    const userInfo = await AsyncStorage.getItem('userInfo');
    if (!userInfo) {
      console.warn('用户信息未找到');
      return 1;
    }
    const parsedUserInfo = JSON.parse(userInfo);
    return Number(parsedUserInfo?.attributes?.userInfo?.userId) || 1;
  } catch (error) {
    console.error('获取用户ID失败:', error);
    return 1;
  }
};

/**
 * 生成存储名称
 * @param baseName 基础存储名称
 * @returns 包含用户ID的完整存储名称
 */
export const generateStorageName = async (
  baseName: string
): Promise<string> => {
  const userId = await getUserId();
  return `${baseName}-user-${userId}`;
};
