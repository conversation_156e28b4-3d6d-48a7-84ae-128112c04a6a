import React from 'react';
import Dialog from '../components/common/Dialog';
import {
  MessageContent,
  ErrorContent,
  WarningContent,
  InputContent,
} from '../components/common/Dialog/ModalContents';

/**
 * 全局对话框工具函数
 */
export const DialogUtil = {
  /**
   * 显示简单提示弹窗
   */
  alert: (title: string, message: string, onConfirm?: () => void) => {
    Dialog.show({
      title,
      children: <MessageContent message={message} />,
      onConfirm: onConfirm || (() => {}),
    });
  },

  /**
   * 显示确认弹窗
   */
  confirm: (
    title: string,
    message: string,
    onConfirm: () => void,
    confirmText = '确定'
  ) => {
    Dialog.show({
      title,
      children: <MessageContent message={message} />,
      confirmText,
      onConfirm,
    });
  },

  /**
   * 显示输入弹窗
   */
  prompt: (
    title: string,
    placeholder = '',
    defaultValue = '',
    onConfirm: (text: string) => void
  ) => {
    let inputValue = defaultValue;

    Dialog.show({
      title,
      children: (
        <InputContent
          placeholder={placeholder}
          defaultValue={defaultValue}
          onChangeText={(text) => {
            inputValue = text;
          }}
        />
      ),
      onConfirm: async () => {
        await onConfirm(inputValue);
      },
    });
  },

  /**
   * 显示自定义内容弹窗
   */
  custom: (
    title: string,
    content: React.ReactNode,
    onConfirm?: () => void,
    options?: {
      confirmText?: string;
      cancelText?: string;
      confirmDisabled?: boolean;
    }
  ) => {
    Dialog.show({
      title,
      children: content,
      onConfirm: onConfirm || (() => {}),
      ...(options || {}),
    });
  },

  /**
   * 显示成功提示弹窗
   */
  success: (message: string, onConfirm?: () => void) => {
    Dialog.show({
      title: '成功',
      children: <MessageContent message={message} />,
      onConfirm: onConfirm || (() => {}),
    });
  },

  /**
   * 显示错误提示弹窗
   */
  error: (message: string, onConfirm?: () => void) => {
    Dialog.show({
      title: '错误',
      children: <ErrorContent message={message} />,
      onConfirm: onConfirm || (() => {}),
    });
  },

  /**
   * 显示警告提示弹窗
   */
  warning: (message: string, onConfirm?: () => void) => {
    Dialog.show({
      title: '警告',
      children: <WarningContent message={message} />,
      onConfirm: onConfirm || (() => {}),
      confirmButtonType: 'warning',
    });
  },

  /**
   * 显示删除确认弹窗
   */
  confirmDelete: (title: string, message: string, onConfirm: () => void) => {
    Dialog.show({
      title,
      confirmText: '删除',
      children: <ErrorContent message={message} />,
      onConfirm,
      confirmButtonType: 'danger',
    });
  },

  /**
   * 隐藏当前显示的模态框
   */
  hide: () => {
    Dialog.hide();
  },
};
