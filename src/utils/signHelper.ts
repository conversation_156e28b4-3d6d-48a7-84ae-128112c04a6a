import CryptoJS from 'crypto-js';

/**
 * 生成X-SIGN签名
 * 基于参考项目的签名算法实现
 */

const DEFAULT_SALT_KEY = 'BOTE_SESSION-X-BT-N-ID';

export interface SignConfig {
  saltKey?: string;
  serverTime?: number;
}

/**
 * 生成X-SIGN签名
 * 格式: {signature}.{timestamp}.{randomNumber}
 */
export function generateXSign(
  params: Record<string, unknown> = {},
  config: SignConfig = {}
): string {
  try {
    const saltKey = config.saltKey || DEFAULT_SALT_KEY;
    const timestamp = config.serverTime || Date.now();
    const randomNumber = Math.floor(Math.random() * 100);

    // 1. 构建待签名字符串
    const sortedKeys = Object.keys(params).sort();
    const queryString = sortedKeys
      .map((key) => `${key}=${params[key]}`)
      .join('&');

    // 2. 添加salt和时间戳
    const signString = `${queryString}&saltKey=${saltKey}&timestamp=${timestamp}`;

    // 3. HMAC-SHA256签名
    const signature = CryptoJS.HmacSHA256(signString, saltKey).toString();

    // 4. 返回完整签名格式
    return `${signature}.${timestamp}.${randomNumber}`;
  } catch (error) {
    console.error('生成X-SIGN失败:', error);
    // 返回一个简单的签名格式作为备用
    return `${CryptoJS.MD5('fallback').toString()}.${Date.now()}.${Math.floor(Math.random() * 100)}`;
  }
}

/**
 * 简化的签名生成（用于测试）
 */
export function generateSimpleSign(): string {
  const timestamp = Date.now();
  const randomStr = Math.random().toString(36).substring(2, 15);
  const hash = CryptoJS.MD5(`${timestamp}${randomStr}`).toString();
  return `${hash}.${timestamp}.${Math.floor(Math.random() * 100)}`;
}
