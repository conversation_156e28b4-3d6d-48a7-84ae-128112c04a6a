import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import { Recording } from '../types';
import { lcdpService } from '@/services/api';
import Toast from 'react-native-toast-message';
import { generateStorageName } from '../utils/userIdUtils';

// 更新存储名称的函数
export const updateRecordingStorageName = async (): Promise<string> => {
  return generateStorageName('recording-storage');
};

export interface RecordingState {
  // 录音状态
  recordings: Recording[];
  currentRecording: Recording | null;
  isRecording: boolean;

  // Actions
  setRecordings: (recordings: Recording[]) => void;
  addRecording: (recording: Recording) => void;
  updateRecording: (id: string, updates: Partial<Recording>) => void;
  deleteRecording: (id: string) => void;
  batchDeleteRecordings: (ids: string[]) => void;
  setCurrentRecording: (recording: Recording | null) => void;
  setIsRecording: (isRecording: boolean) => void;
  retryUpload: (recordingId: string) => void; // 新增重试上传方法
  uploadRecording: (recording: Recording) => Promise<void>; // 通用上传方法
}

export const useRecordingStore = create<RecordingState>()(
  persist(
    (set, get) => ({
      // 初始状态
      recordings: [],
      currentRecording: null,
      isRecording: false,

      // Actions
      setRecordings: (recordings) => {
        // 确保所有录音对象的日期字段都是 Date 对象
        const recordingsWithDates = recordings.map((recording) => ({
          ...recording,
          createdAt:
            recording.createdAt instanceof Date
              ? recording.createdAt
              : new Date(recording.createdAt || new Date()),
          updatedAt:
            recording.updatedAt instanceof Date
              ? recording.updatedAt
              : new Date(recording.updatedAt || new Date()),
        }));
        set({ recordings: recordingsWithDates });
      },

      addRecording: async (recording) => {
        const { recordings } = get();

        // 🎯 立即添加到本地列表，不阻塞界面
        const recordingWithDates = {
          ...recording,
          createdAt:
            recording.createdAt instanceof Date
              ? recording.createdAt
              : new Date(recording.createdAt || new Date()),
          updatedAt:
            recording.updatedAt instanceof Date
              ? recording.updatedAt
              : new Date(recording.updatedAt || new Date()),
          // 🆕 添加上传状态，初始为待上传
          uploadStatus: recording.uploadStatus || 'pending',
        };

        set({ recordings: [recordingWithDates, ...recordings] });

        // 🚀 异步上传，不阻塞（复用通用上传逻辑）
        setTimeout(async () => {
          const { uploadRecording } = get();
          try {
            await uploadRecording(recordingWithDates);
          } catch (error) {
            // 错误已在 uploadRecording 方法中处理，这里只需要静默处理
            // 静默处理错误，避免控制台警告
          }
        }, 100); // 短暂延迟确保UI更新后再开始上传
      },

      updateRecording: (id, updates) => {
        const { recordings } = get();
        const updatedRecordings = recordings.map((recording) => {
          if (recording.id === id) {
            const updatedRecording = { ...recording, ...updates };
            // 确保日期字段是 Date 对象
            if (
              updatedRecording.createdAt &&
              !(updatedRecording.createdAt instanceof Date)
            ) {
              updatedRecording.createdAt = new Date(updatedRecording.createdAt);
            }
            if (
              updatedRecording.updatedAt &&
              !(updatedRecording.updatedAt instanceof Date)
            ) {
              updatedRecording.updatedAt = new Date(updatedRecording.updatedAt);
            }
            return updatedRecording;
          }
          return recording;
        });
        set({ recordings: updatedRecordings });
      },

      deleteRecording: async (id) => {
        const { recordings } = get();
        const filteredRecordings = recordings.filter(
          (recording) => recording.id !== id
        );
        const res = await lcdpService.deleteRecordingRecord({ appFileId: id });
        if (res.resultCode === '0') {
          set({ recordings: filteredRecordings });
          Toast.show({
            type: 'success',
            text1: '删除成功',
          });
        }
      },

      batchDeleteRecordings: async (ids) => {
        const { recordings } = get();
        const filteredRecordings = recordings.filter(
          (recording) => !ids.includes(recording.id)
        );
        const res = await lcdpService.batchDeleteRecordings({
          appFileIds: ids.map((id) => ({ appFileId: id })),
        });
        if (res.resultCode === '0') {
          set({ recordings: filteredRecordings });
          Toast.show({
            type: 'success',
            text1: '删除成功',
          });
        }
      },

      setCurrentRecording: (recording) => set({ currentRecording: recording }),
      setIsRecording: (isRecording) => set({ isRecording }),

      // 通用上传方法（提取重复逻辑）
      uploadRecording: async (recording: Recording) => {
        try {
          // 更新状态为上传中
          get().updateRecording(recording.id, {
            uploadStatus: 'uploading',
            uploadError: undefined,
          });

          // 检查文件是否存在
          const fileInfo = await FileSystem.getInfoAsync(recording.filePath);
          if (!fileInfo.exists) {
            throw new Error('文件不存在');
          }

          // 从文件路径获取文件名和扩展名
          const fileName =
            recording.filePath.split('/').pop() || 'recording.m4a';
          const fileExtension =
            fileName.split('.').pop()?.toLowerCase() || 'm4a';

          // 根据扩展名确定MIME类型
          const mimeType =
            fileExtension === 'mp3'
              ? 'audio/mpeg'
              : fileExtension === 'wav'
                ? 'audio/wav'
                : fileExtension === 'm4a'
                  ? 'audio/mp4'
                  : 'audio/mpeg';

          // 创建React Native文件对象用于上传
          const fileObject = {
            uri: recording.filePath,
            type: mimeType,
            name: fileName,
          };

          const fileResponse = await lcdpService.uploadSingleFile(fileObject);

          if (fileResponse.resultCode !== '0') {
            // 上传失败
            get().updateRecording(recording.id, {
              uploadStatus: 'failed',
              uploadError: fileResponse.resultMsg || '文件上传失败',
            });
            throw new Error(fileResponse.resultMsg || '文件上传失败');
          }

          // 调用API添加录音记录
          const res = await lcdpService.addNewRecording({
            file_id: fileResponse.resultObject.fileId,
            app_file_id: recording.id,
            name: recording.title,
            duration: recording.duration,
            source: recording.type,
            location: recording.location,
          });

          if (res.resultCode === '0') {
            // 上传成功
            get().updateRecording(recording.id, {
              uploadStatus: 'completed',
              uploadError: undefined,
            });
          } else {
            // API调用失败
            get().updateRecording(recording.id, {
              uploadStatus: 'failed',
              uploadError: res.resultMsg || 'API调用失败',
            });
            throw new Error(res.resultMsg || 'API调用失败');
          }
        } catch (error) {
          // 上传失败
          const errorMessage =
            error instanceof Error ? error.message : '上传失败';
          get().updateRecording(recording.id, {
            uploadStatus: 'failed',
            uploadError: errorMessage,
          });
          throw error; // 重新抛出错误，让调用者处理UI反馈
        }
      },

      // 重试上传方法（复用通用上传逻辑）
      retryUpload: async (recordingId: string) => {
        const { recordings, uploadRecording } = get();
        const recording = recordings.find((r) => r.id === recordingId);

        if (!recording) {
          Toast.show({
            type: 'error',
            text1: '录音未找到',
            text2: '无法重新上传',
          });
          return;
        }

        try {
          await uploadRecording(recording);
          Toast.show({
            type: 'success',
            text1: '上传成功',
            text2: '录音已成功上传到服务器',
          });
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : '上传失败';
          Toast.show({
            type: 'error',
            text1: '上传失败',
            text2: errorMessage,
          });
        }
      },
    }),
    {
      name: 'recording-storage',
      storage: createJSONStorage(() => ({
        // 创建一个支持动态存储键的存储对象
        getItem: async (name: string) => {
          const storageName = await generateStorageName(name);
          const data = await AsyncStorage.getItem(storageName);
          return data ? JSON.parse(data) : null;
        },
        setItem: async (name: string, value: string) => {
          const storageName = await generateStorageName(name);
          await AsyncStorage.setItem(storageName, JSON.stringify(value));
        },
        removeItem: async (name: string) => {
          const storageName = await generateStorageName(name);
          await AsyncStorage.removeItem(storageName);
        },
      })),
      partialize: (state) => ({
        recordings: state.recordings.map((recording) => ({
          ...recording,
          createdAt:
            recording.createdAt instanceof Date
              ? recording.createdAt.toISOString()
              : recording.createdAt || new Date().toISOString(),
          updatedAt:
            recording.updatedAt instanceof Date
              ? recording.updatedAt.toISOString()
              : recording.updatedAt || new Date().toISOString(),
        })),
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.recordings = state.recordings.map((recording) => ({
            ...recording,
            createdAt: recording.createdAt
              ? new Date(recording.createdAt)
              : new Date(),
            updatedAt: recording.updatedAt
              ? new Date(recording.updatedAt)
              : new Date(),
          }));
        }
      },
    }
  )
);
