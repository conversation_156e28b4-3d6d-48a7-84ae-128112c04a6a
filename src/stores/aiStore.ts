import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AIChatSession, AIChatMessage, AIServiceConfig } from '@/types/ai';
import { aiService } from '@/services/ai/aiService';
import { generateChatSessionId, generateMessageId } from '@/utils/uuid';

export interface AIState {
  // 配置状态
  config: Partial<AIServiceConfig>;
  isConfigured: boolean;
  
  // 聊天会话
  sessions: AIChatSession[];
  currentSession: AIChatSession | null;
  
  // UI状态
  isLoading: boolean;
  error: string | null;
  
  // 使用统计
  usage: {
    totalTokens: number;
    totalCost: number;
    requestCount: number;
  };
  
  // Actions
  setConfig: (config: Partial<AIServiceConfig>) => void;
  clearConfig: () => void;
  
  createSession: (title?: string) => void;
  setCurrentSession: (session: AIChatSession | null) => void;
  deleteSession: (sessionId: string) => void;
  
  addMessage: (sessionId: string, message: Omit<AIChatMessage, 'id' | 'timestamp'>) => void;
  sendMessage: (content: string, context?: string) => Promise<void>;
  
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  updateUsage: (tokens: number, cost: number) => void;
  resetUsage: () => void;
}

export const useAIStore = create<AIState>()(
  persist(
    (set, get) => ({
      // 初始状态
      config: {},
      isConfigured: false,
      sessions: [],
      currentSession: null,
      isLoading: false,
      error: null,
      usage: {
        totalTokens: 0,
        totalCost: 0,
        requestCount: 0,
      },
      
      // 配置相关
      setConfig: (config) => {
        const newConfig = { ...get().config, ...config };
        set({ config: newConfig, isConfigured: !!newConfig.apiKey });
        
        // 配置AI服务
        aiService.configure(newConfig);
      },
      
      clearConfig: () => {
        set({ 
          config: {}, 
          isConfigured: false,
          sessions: [],
          currentSession: null 
        });
      },
      
      // 会话管理
      createSession: (title) => {
        const newSession: AIChatSession = {
          id: generateChatSessionId(),
          title: title || `对话 ${new Date().toLocaleDateString()}`,
          messages: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          userId: 'current-user',
        };
        
        set({
          sessions: [newSession, ...get().sessions],
          currentSession: newSession,
        });
      },
      
      setCurrentSession: (session) => {
        set({ currentSession: session });
      },
      
      deleteSession: (sessionId) => {
        const { sessions, currentSession } = get();
        const updatedSessions = sessions.filter(s => s.id !== sessionId);
        
        set({
          sessions: updatedSessions,
          currentSession: currentSession?.id === sessionId ? null : currentSession,
        });
      },
      
      // 消息管理
      addMessage: (sessionId, messageData) => {
        const message: AIChatMessage = {
          ...messageData,
          id: generateMessageId(),
          timestamp: new Date(),
        };
        
        const { sessions } = get();
        const updatedSessions = sessions.map(session => {
          if (session.id === sessionId) {
            return {
              ...session,
              messages: [...session.messages, message],
              updatedAt: new Date(),
            };
          }
          return session;
        });
        
        set({ sessions: updatedSessions });
        
        // 更新当前会话
        const currentSession = get().currentSession;
        if (currentSession?.id === sessionId) {
          set({
            currentSession: {
              ...currentSession,
              messages: [...currentSession.messages, message],
              updatedAt: new Date(),
            }
          });
        }
      },
      
      sendMessage: async (content, _context) => {
        const { currentSession, addMessage, setLoading, setError, updateUsage } = get();
        
        if (!currentSession) {
          setError('没有活动的对话会话');
          return;
        }
        
        if (!aiService.isReady()) {
          setError('AI服务未配置，请先设置API密钥');
          return;
        }
        
        try {
          setLoading(true);
          setError(null);
          
          // 添加用户消息
          addMessage(currentSession.id, {
            role: 'user',
            content,
          });
          
          // 准备消息历史
          const messages = [...currentSession.messages, {
            id: generateMessageId(),
            role: 'user' as const,
            content,
            timestamp: new Date(),
          }];
          
          // 发送到AI服务
          const response = await aiService.chatCompletion(messages);
          
          if (response.success && response.data) {
            // 添加AI回复
            addMessage(currentSession.id, {
              role: 'assistant',
              content: response.data,
            });
            
            // 更新使用统计
            if (response.usage) {
              updateUsage(response.usage.tokens, response.usage.cost);
            }
          } else {
            setError(response.error || 'AI服务响应错误');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '发送消息失败';
          setError(errorMessage);
        } finally {
          setLoading(false);
        }
      },
      
      // UI状态
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
      
      // 使用统计
      updateUsage: (tokens, cost) => {
        const { usage } = get();
        set({
          usage: {
            totalTokens: usage.totalTokens + tokens,
            totalCost: usage.totalCost + cost,
            requestCount: usage.requestCount + 1,
          }
        });
      },
      
      resetUsage: () => {
        set({
          usage: {
            totalTokens: 0,
            totalCost: 0,
            requestCount: 0,
          }
        });
      },
    }),
    {
      name: 'ai-storage',
      storage: createJSONStorage(() => AsyncStorage),
      // 只持久化必要的数据
      partialize: (state) => ({
        config: state.config,
        isConfigured: state.isConfigured,
        sessions: state.sessions,
        usage: state.usage,
      }),
    }
  )
);