import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { WritingContent } from '../types';

export interface WritingState {
  // AI写作状态
  contents: WritingContent[];
  currentContent: WritingContent | null;
  isGenerating: boolean;
  
  // Actions
  setContents: (contents: WritingContent[]) => void;
  addContent: (content: WritingContent) => void;
  updateContent: (id: string, updates: Partial<WritingContent>) => void;
  deleteContent: (id: string) => void;
  setCurrentContent: (content: WritingContent | null) => void;
  setIsGenerating: (isGenerating: boolean) => void;
}

export const useWritingStore = create<WritingState>()(
  persist(
    (set, get) => ({
      // 初始状态
      contents: [],
      currentContent: null,
      isGenerating: false,
      
      // Actions
      setContents: (contents) => set({ contents }),
      
      addContent: (content) => {
        const { contents } = get();
        set({ contents: [content, ...contents] });
      },
      
      updateContent: (id, updates) => {
        const { contents } = get();
        const updatedContents = contents.map((content) =>
          content.id === id ? { ...content, ...updates } : content
        );
        set({ contents: updatedContents });
      },
      
      deleteContent: (id) => {
        const { contents } = get();
        const filteredContents = contents.filter(
          (content) => content.id !== id
        );
        set({ contents: filteredContents });
      },
      
      setCurrentContent: (content) => set({ currentContent: content }),
      setIsGenerating: (isGenerating) => set({ isGenerating }),
    }),
    {
      name: 'writing-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
