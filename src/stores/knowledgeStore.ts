import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { KnowledgeDocument, KnowledgeCategory } from '../types';
import { lcdpService } from '@/services/api';
import Toast from 'react-native-toast-message';
import { generateStorageName } from '../utils/userIdUtils';

// 更新存储名称的函数
export const updateKnowledgeStorageName = async (): Promise<string> => {
  return generateStorageName('knowledge-storage');
};

export interface KnowledgeState {
  // 知识库状态
  documents: KnowledgeDocument[];
  categories: KnowledgeCategory[];
  currentCategory: KnowledgeCategory | null;
  isLoading: boolean;

  // Actions
  setDocuments: (documents: KnowledgeDocument[]) => void;
  addDocument: (document: KnowledgeDocument) => void;
  updateDocument: (id: string, updates: Partial<KnowledgeDocument>) => void;
  deleteDocument: (id: string) => void;
  batchDeleteDocument: (ids: string[]) => void;

  setCategories: (categories: KnowledgeCategory[]) => void;
  addCategory: (category: KnowledgeCategory) => void;
  addCategoryBySceneName: (sceneName: string) => void;
  updateCategory: (id: string, updates: Partial<KnowledgeCategory>) => void;
  deleteCategory: (id: string) => void;
  setCurrentCategory: (category: KnowledgeCategory | null) => void;
  setLoading: (loading: boolean) => void;
  initCategory: () => void;
}

export const useKnowledgeStore = create<KnowledgeState>()(
  persist(
    (set, get) => ({
      // 初始状态
      documents: [],
      categories: [],
      currentCategory: null,
      isLoading: false,

      // Document Actions
      setDocuments: (documents) => set({ documents }),

      addDocument: async (document) => {
        const { documents } = get();
        const res = await lcdpService.addSpaceFile({
          small_office_space: {
            file_id: document.fileId,
            scene_id: document.sceneId,
            file_name: document.fileName,
            file_type: document.fileType,
            file_size: document.fileSize,
          },
        });
        if (res.resultCode === '0') {
          set({
            documents: [
              {
                ...document,
                id: res.resultObject.id,
              },
              ...documents,
            ],
          });
        }
      },

      updateDocument: (id, updates) => {
        const { documents } = get();
        const updatedDocuments = documents.map((doc) =>
          doc.id === id ? { ...doc, ...updates } : doc
        );
        set({ documents: updatedDocuments });
      },

      deleteDocument: async (id) => {
        const { documents } = get();
        const filteredDocuments = documents.filter((doc) => doc.id !== id);
        const res = await lcdpService.deleteSpaceFile({
          ids: [{ id }],
        });
        if (res.resultCode === '0') {
          Toast.show({
            text1: '删除成功',
          });
          set({ documents: filteredDocuments });
        }
      },

      batchDeleteDocument: async (ids) => {
        const { documents } = get();
        const filteredDocuments = documents.filter(
          (doc) => !ids.includes(doc.id)
        );
        const res = await lcdpService.deleteSpaceFile({
          ids: ids.map((c) => ({ id: c })),
        });
        if (res.resultCode === '0') {
          Toast.show({
            text1: '删除成功',
          });
          set({ documents: filteredDocuments });
        }
      },

      // Category Actions
      setCategories: (categories) => set({ categories }),

      addCategory: (category) => {
        const { categories } = get();
        set({ categories: [...categories, category] });
      },

      addCategoryBySceneName: async (sceneName) => {
        const { addCategory } = get();
        console.log('🔧 创建知识库分类调试 - 请求:', { scene_name: sceneName });
        const res = await lcdpService.addScene({ scene_name: sceneName });
        console.log(
          '🔧 创建知识库分类调试 - 响应:',
          JSON.stringify(res, null, 2)
        );
        if (res.resultCode === '0') {
          if (res.resultObject.result === '0') {
            const { knowledgeId, id } = res?.resultObject?.resultObject || {};
            console.log('🔧 提取的数据:', { knowledgeId, id, sceneName });
            addCategory({
              sceneName: sceneName,
              knowledgeId,
              id,
            });
          } else {
            Toast.show({ text1: res.resultObject.message });
          }
        }
      },

      updateCategory: (id, updates) => {
        const { categories } = get();
        const updatedCategories = categories.map((cat) =>
          cat.id === id ? { ...cat, ...updates } : cat
        );
        set({ categories: updatedCategories });
      },

      deleteCategory: async (id) => {
        const { categories } = get();
        const res = await lcdpService.deleteScene({
          id,
        });
        if (res.resultCode === '0') {
          const filteredCategories = categories.filter((cat) => cat.id !== id);
          set({ categories: filteredCategories });
        }
      },

      setCurrentCategory: (category) => set({ currentCategory: category }),

      setLoading: (loading) => set({ isLoading: loading }),

      initCategory: async () => {
        const { categories, addCategoryBySceneName } = get();
        console.log('🔧 初始化知识库分类调试 - 现有分类:', categories);
        if (!categories.length) {
          console.log('🔧 没有分类，创建默认分类');
          addCategoryBySceneName('文档');
          addCategoryBySceneName('音频');
        } else {
          console.log('🔧 已有分类，不需要创建');
        }
      },
    }),
    {
      name: 'knowledge-storage',
      storage: createJSONStorage(() => AsyncStorage),
      getStorage: () => {
        // 创建一个支持动态存储键的存储对象
        return {
          getItem: async (name) => {
            const storageName = await generateStorageName(name);
            const data = await AsyncStorage.getItem(storageName);
            return data ? JSON.parse(data) : null;
          },
          setItem: async (name, value) => {
            const storageName = await generateStorageName(name);
            await AsyncStorage.setItem(storageName, JSON.stringify(value));
          },
          removeItem: async (name) => {
            const storageName = await generateStorageName(name);
            await AsyncStorage.removeItem(storageName);
          },
        };
      },
    }
  )
);
