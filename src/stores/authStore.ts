import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User } from '../types';
import { authService, lcdpService } from '../services/api';
import { initUserStorage } from '@/utils/initUserStorage';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthState {
  // 认证状态
  user: User | null;
  isAuthenticated: boolean;
  token: string | null;

  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (user: Partial<User>) => void;
  setToken: (token: string | null) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      isAuthenticated: false,
      token: null,

      // Actions
      login: async (credentials) => {
        try {
          const response = await authService.login(credentials);
          await lcdpService.checkUserInfo({
            userName: response.user.username,
            userCode: response.user.email,
            userId: response.user.id,
          });
          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
          });
          await AsyncStorage.setItem('user', JSON.stringify(response.user));

          // 保存用户信息到AsyncStorage，用于用户ID的获取
          await AsyncStorage.setItem(
            'userInfo',
            JSON.stringify({
              attributes: {
                userInfo: {
                  userId: response.user.id,
                },
              },
            })
          );

          // 初始化用户存储
          await initUserStorage();
        } catch (error) {
          throw new Error('登录失败');
        }
      },

      logout: async () => {
        try {
          await authService.logout();

          set({
            user: null,
            token: null,
            isAuthenticated: false,
          });

          // 移除用户信息
          await AsyncStorage.removeItem('userInfo');

          // 重新初始化用户存储（使用默认ID）
          await initUserStorage();
        } catch (error) {
          console.error('登出失败:', error);
          // 即使API调用失败，也清除本地状态
          set({
            user: null,
            token: null,
            isAuthenticated: false,
          });
        }
      },

      updateUser: (userData) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData },
          });
        }
      },

      setToken: (token) => {
        set({ token, isAuthenticated: !!token });
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
