import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface AppState {
  // 应用状态
  isLoading: boolean;
  error: string | null;
  theme: 'light' | 'dark';
  language: 'zh' | 'en';
  
  // Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  setLanguage: (language: 'zh' | 'en') => void;
  clearError: () => void;
}

export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      // 初始状态
      isLoading: false,
      error: null,
      theme: 'light',
      language: 'zh',
      
      // Actions
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
      setTheme: (theme) => set({ theme }),
      setLanguage: (language) => set({ language }),
      clearError: () => set({ error: null }),
    }),
    {
      name: 'app-storage',
      storage: createJSONStorage(() => AsyncStorage),
      // 只持久化主题和语言设置
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
      }),
    }
  )
);