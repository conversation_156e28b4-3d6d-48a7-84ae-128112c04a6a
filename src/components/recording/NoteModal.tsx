import { LinearGradient } from 'expo-linear-gradient';
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Animated,
  PanResponder,
  TouchableWithoutFeedback,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { colors, spacing, typography } from '@/styles';
import { NoteData } from '@/types';
import { ScrollView } from 'react-native-gesture-handler';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface NoteModalProps {
  visible: boolean;
  onClose: () => void;
  onSave?: (note: string) => void;
  initialText?: string;
  noteData?: NoteData[];
}

const NoteModal: React.FC<NoteModalProps> = ({
  visible,
  onClose,
  onSave,
  initialText = '',
  noteData = [],
}) => {
  const [noteText, setNoteText] = useState(initialText);
  const insets = useSafeAreaInsets();
  const textInputRef = useRef<TextInput>(null);

  // 动画相关
  const translateY = useRef(new Animated.Value(screenHeight)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;

  // 手势处理
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return Math.abs(gestureState.dy) > 10;
      },
      onPanResponderMove: (_, gestureState) => {
        if (gestureState.dy > 0) {
          translateY.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dy > 100 || gestureState.vy > 0.5) {
          handleClose();
        } else {
          Animated.spring(translateY, {
            toValue: 0,
            useNativeDriver: true,
            tension: 100,
            friction: 8,
          }).start();
        }
      },
    })
  ).current;

  useEffect(() => {
    if (visible) {
      setNoteText(initialText);
      // 显示动画
      Animated.parallel([
        Animated.timing(backdropOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(translateY, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
      ]).start(() => {
        // 动画完成后自动聚焦输入框
        setTimeout(() => {
          textInputRef.current?.focus();
        }, 100);
      });
    } else {
      // 隐藏动画
      Animated.parallel([
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: screenHeight,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, initialText]);

  const handleClose = () => {
    textInputRef.current?.blur();
    Animated.parallel([
      Animated.timing(backdropOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: screenHeight,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onClose();
    });
  };

  const handleSave = () => {
    onSave?.(noteText);
    handleClose();
  };

  const handleBackdropPress = () => {
    handleClose();
  };

  if (!visible) {
    return null;
  }

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={handleClose}
      statusBarTranslucent
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* 背景遮罩 */}
        <Animated.View
          style={[
            styles.backdrop,
            {
              opacity: backdropOpacity,
            },
          ]}
        >
          <TouchableWithoutFeedback onPress={handleBackdropPress}>
            <View style={styles.backdropTouchable} />
          </TouchableWithoutFeedback>
        </Animated.View>
        {/* 对话框容器 */}
        <Animated.View
          style={[
            styles.dialogContainer,
            {
              transform: [{ translateY }],
              paddingBottom: Math.max(insets.bottom, 20),
            },
          ]}
          {...panResponder.panHandlers}
        >
          {/* 顶部指示条和手柄 */}
          <View style={styles.header}>
            <View style={styles.handleContainer}>
              <LinearGradient
                colors={['#FFD666', '#FFC53D']}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
                style={styles.handle}
              />
            </View>
          </View>

          {noteData?.length > 0 && (
            <ScrollView
              style={styles.noteList}
              showsVerticalScrollIndicator={false}
            >
              {noteData.map((note) => (
                <View style={styles.noteItem} key={note.markTime}>
                  <Text style={styles.noteTime}>{note.markTime}</Text>
                  <Text style={styles.noteContent}>{note.note}</Text>
                </View>
              ))}
            </ScrollView>
          )}

          {/* 主体内容 */}
          <View style={styles.body}>
            {/* 输入框 */}
            <View style={styles.inputContainer}>
              <TextInput
                ref={textInputRef}
                style={styles.textInput}
                placeholder="在这里输入你的想法..."
                placeholderTextColor="#9CA3B4"
                value={noteText}
                onChangeText={setNoteText}
                multiline
                textAlignVertical="top"
                maxLength={1000}
                returnKeyType="default"
                blurOnSubmit={false}
              />

              {/* 按钮容器 */}
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[
                    styles.saveButton,
                    noteText.trim().length === 0 && styles.saveButtonDisabled,
                  ]}
                  onPress={handleSave}
                  disabled={noteText.trim().length === 0}
                  activeOpacity={0.8}
                >
                  <Text style={styles.saveButtonText}>记一下</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Animated.View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdropTouchable: {
    flex: 1,
  },
  dialogContainer: {
    width: screenWidth,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden', // 确保内容不会超出圆角边界
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  header: {
    width: '100%',
    borderBottomColor: colors.border,
    borderBottomWidth: 1,
    borderStyle: 'dashed',
  },
  handleContainer: {
    height: 22,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    backgroundColor: 'transparent',
  },
  handle: {
    width: '100%',
    height: 16,
  },
  noteList: {
    paddingHorizontal: spacing.lg,
    flexDirection: 'column',
    maxHeight: 300,

    borderBottomColor: colors.border,
    borderBottomWidth: 1,
  },
  noteItem: {
    flexDirection: 'row',
    borderBottomColor: colors.border,
    borderBottomWidth: 1,
    paddingVertical: spacing.md,
  },
  noteTime: {
    color: colors.text.secondary,
    marginRight: spacing.md,
  },
  noteContent: {},
  body: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 8,
  },
  inputContainer: {
    backgroundColor: '#F7F8FA',
    borderRadius: 8,
    padding: 16,
    minHeight: 130,
  },
  textInput: {
    flex: 1,
    fontSize: 14,
    lineHeight: 22,
    color: '#414352',
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    textAlignVertical: 'top',
    minHeight: 60,
    marginBottom: 10,
  },
  buttonContainer: {
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    marginTop: 'auto',
  },
  saveButton: {
    backgroundColor: '#FAAF0C',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 4,
    minHeight: 32,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#FAAF0C',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  saveButtonDisabled: {
    backgroundColor: '#E8E8E8',
    shadowOpacity: 0,
    elevation: 0,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
    lineHeight: 24,
    fontFamily: typography.fontFamily,
    textAlign: 'center',
  },
});

export default NoteModal;
