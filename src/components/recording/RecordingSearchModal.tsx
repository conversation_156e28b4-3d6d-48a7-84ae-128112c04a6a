import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SwipeListView } from 'react-native-swipe-list-view';
import { colors } from '../../styles';
import { formatDuration, formatDate, formatFileSize } from '../../utils';
import { Recording } from '../../types/recording';

const { width: screenWidth } = Dimensions.get('window');

interface RecordingSearchModalProps {
  visible: boolean;
  onClose: () => void;
  recordings: Recording[];
  onRecordingPress: (recording: Recording) => void;
}

const RecordingSearchModal: React.FC<RecordingSearchModalProps> = ({
  visible,
  onClose,
  recordings,
  onRecordingPress,
}) => {
  const [searchText, setSearchText] = useState('');
  const [slideAnim] = useState(new Animated.Value(screenWidth));

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: screenWidth,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setSearchText('');
      });
    }
  }, [visible, slideAnim]);

  const handleClose = () => {
    onClose();
  };

  const filteredRecordings = recordings.filter((recording) =>
    recording.title.toLowerCase().includes(searchText.toLowerCase())
  );

  const renderRecordingItem = (data: { item: Recording }) => (
    <TouchableOpacity
      style={styles.recordingItem}
      onPress={() => {
        onRecordingPress(data.item);
        handleClose();
      }}
    >
      <View style={styles.recordingContent}>
        <Text style={styles.recordingTitle} numberOfLines={1}>
          {data.item.title}
        </Text>
        <View style={styles.recordingMeta}>
          <Ionicons
            name="time-outline"
            size={14}
            color={colors.text.secondary}
          />
          <Text style={styles.recordingMetaText}>
            {formatDuration(data.item.duration)}
          </Text>
          <Ionicons
            name="calendar-outline"
            size={14}
            color={colors.text.secondary}
          />
          <Text style={styles.recordingMetaText}>
            {formatDate(data.item.createdAt)}
          </Text>
          <Text style={styles.recordingMetaText}>
            {formatFileSize(data.item.size)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={handleClose}
    >
      <View style={styles.modalContainer}>
        <Animated.View
          style={[
            styles.searchPanel,
            {
              transform: [{ translateX: slideAnim }],
            },
          ]}
        >
          <View style={styles.searchHeader}>
            <TouchableOpacity onPress={handleClose}>
              <Ionicons
                name="arrow-back"
                size={24}
                color={colors.text.primary}
              />
            </TouchableOpacity>
            <TextInput
              style={styles.searchInput}
              placeholder="搜索录音..."
              value={searchText}
              onChangeText={setSearchText}
              autoFocus
              placeholderTextColor={colors.text.secondary}
            />
            {searchText.length > 0 && (
              <TouchableOpacity onPress={() => setSearchText('')}>
                <Ionicons
                  name="close-circle"
                  size={20}
                  color={colors.text.secondary}
                />
              </TouchableOpacity>
            )}
          </View>
          <View style={styles.searchContent}>
            {filteredRecordings.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Ionicons
                  name="search-outline"
                  size={48}
                  color={colors.text.secondary}
                />
                <Text style={styles.emptyText}>
                  {searchText ? '未找到匹配的录音' : '开始搜索录音'}
                </Text>
              </View>
            ) : (
              <SwipeListView
                data={filteredRecordings}
                renderItem={renderRecordingItem}
                keyExtractor={(item) => item.id}
                showsVerticalScrollIndicator={false}
                style={styles.searchList}
              />
            )}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  searchPanel: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    width: screenWidth,
    backgroundColor: colors.background.primary,
  },
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.background.primary,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: colors.text.primary,
    marginHorizontal: 12,
    paddingVertical: 8,
  },
  searchContent: {
    flex: 1,
    backgroundColor: colors.background.bg,
  },
  searchList: {
    flex: 1,
  },
  recordingItem: {
    backgroundColor: colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  recordingContent: {
    flex: 1,
  },
  recordingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },
  recordingMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  recordingMetaText: {
    fontSize: 12,
    color: colors.text.secondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 16,
    color: colors.text.secondary,
    marginTop: 16,
  },
});

export default RecordingSearchModal;
