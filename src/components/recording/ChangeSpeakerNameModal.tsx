import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Dialog from '../common/Dialog';
import { colors } from '../../styles';

interface ChangeSpeakerNameModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (newName: string, applyToAll: boolean) => void;
  currentSpeakerName: string;
}

const ChangeSpeakerNameModalRefactored: React.FC<
  ChangeSpeakerNameModalProps
> = ({ visible, onClose, onConfirm, currentSpeakerName }) => {
  const [newName, setNewName] = useState('');
  const [applyToAll, setApplyToAll] = useState(false);

  React.useEffect(() => {
    if (visible) {
      setNewName(currentSpeakerName);
      setApplyToAll(false);
    }
  }, [visible, currentSpeakerName]);

  const handleConfirm = () => {
    if (newName.trim()) {
      onConfirm(newName.trim(), applyToAll);
    }
  };

  // 渲染模态框内容
  const renderModalContent = () => (
    <>
      <TextInput
        style={styles.input}
        value={newName}
        onChangeText={setNewName}
        placeholder="请输入"
        placeholderTextColor={colors.text.secondary}
        autoFocus
        maxLength={20}
      />

      <TouchableOpacity
        style={styles.checkboxContainer}
        onPress={() => setApplyToAll(!applyToAll)}
      >
        <View style={[styles.checkbox, applyToAll && styles.checkboxChecked]}>
          {applyToAll && (
            <Ionicons
              name="checkmark"
              size={16}
              color={colors.background.primary}
            />
          )}
        </View>
        <Text style={styles.checkboxLabel}>
          修改全部的&quot;{currentSpeakerName}&quot;
        </Text>
      </TouchableOpacity>
    </>
  );

  return (
    <Dialog
      visible={visible}
      title="修改名称"
      onClose={onClose}
      onConfirm={handleConfirm}
      confirmDisabled={!newName.trim()}
    >
      {renderModalContent()}
    </Dialog>
  );
};

const styles = StyleSheet.create({
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.text.primary,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: colors.border,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  checkboxChecked: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  checkboxLabel: {
    fontSize: 14,
    color: colors.text.primary,
    flex: 1,
  },
});

export default ChangeSpeakerNameModalRefactored;
