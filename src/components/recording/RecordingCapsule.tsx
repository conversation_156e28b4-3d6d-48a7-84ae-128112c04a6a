import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  Image,
  DeviceEventEmitter,
  NativeModules,
} from 'react-native';
import {
  PanGestureHandler,
  PanGestureHandlerStateChangeEvent,
  State,
} from 'react-native-gesture-handler';
import { typography } from '@/styles';

const { FloatingWindowModule } = NativeModules;

interface RecordingCapsuleProps {
  visible: boolean;
  duration: number;
  isRecording: boolean;
  isPaused: boolean;
  onPress: () => void;
  onStop: () => void;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const RecordingCapsule: React.FC<RecordingCapsuleProps> = ({
  visible,
  duration,
  isRecording,
  isPaused,
  onPress,
  onStop,
}) => {
  // 计算初始位置：右下角20%，右侧紧贴屏幕边缘
  const capsuleWidth = 180;
  const capsuleHeight = 50;
  const initialX = screenWidth - capsuleWidth; // 右侧紧贴屏幕边缘
  const initialY = screenHeight * 0.8 - capsuleHeight; // 屏幕高度80%位置（即距离底部20%）

  const translateX = useRef(new Animated.Value(initialX)).current;
  const translateY = useRef(new Animated.Value(initialY)).current;
  const startPosition = useRef({ x: initialX, y: initialY });
  const isDragging = useRef(false);
  const isCollapsed = useRef(false);

  // 位置同步相关（新增）
  const currentPosition = useRef({ x: initialX, y: initialY });
  const positionSyncEnabled = useRef(true);

  // 位置同步和事件监听（新增）
  useEffect(() => {
    // 获取当前存储的位置并同步
    const initializePosition = async () => {
      if (FloatingWindowModule && FloatingWindowModule.getCurrentPosition) {
        try {
          const storedPosition =
            await FloatingWindowModule.getCurrentPosition();
          if (storedPosition && positionSyncEnabled.current) {
            console.log('从原生同步位置:', storedPosition);
            updatePositionFromNative(
              storedPosition.x,
              storedPosition.y,
              storedPosition.isCollapsed
            );
          }
        } catch (error) {
          console.log('获取存储位置失败:', error);
        }
      }
    };

    // 监听来自原生的位置同步事件
    const positionSyncListener = DeviceEventEmitter.addListener(
      'onCapsulePositionSync',
      (data) => {
        if (positionSyncEnabled.current && !isDragging.current) {
          console.log('接收到位置同步事件:', data);
          updatePositionFromNative(data.x, data.y, data.isCollapsed);
        }
      }
    );

    // 监听显示/隐藏胶囊事件
    const showCapsuleListener = DeviceEventEmitter.addListener(
      'onShowRNCapsule',
      (data) => {
        console.log('接收到显示RN胶囊事件:', data);
        // 这里可以处理显示逻辑，如果需要的话
      }
    );

    const hideCapsuleListener = DeviceEventEmitter.addListener(
      'onHideRNCapsule',
      (data) => {
        console.log('接收到隐藏RN胶囊事件:', data);
        // 这里可以处理隐藏逻辑，如果需要的话
      }
    );

    // 初始化位置
    initializePosition();

    // 清理监听器
    return () => {
      positionSyncListener.remove();
      showCapsuleListener.remove();
      hideCapsuleListener.remove();
    };
  }, []);

  // 从原生更新位置的函数（新增）
  const updatePositionFromNative = (
    x: number,
    y: number,
    collapsed: boolean
  ) => {
    const newX = x;
    const newY = y;

    // 更新当前位置记录
    currentPosition.current = { x: newX, y: newY };
    isCollapsed.current = collapsed;

    // 使用动画更新位置
    Animated.parallel([
      Animated.timing(translateX, {
        toValue: newX,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: newY,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();

    console.log(
      `RN胶囊位置已同步: (${newX}, ${newY}), collapsed: ${collapsed}`
    );
  };

  // 向原生同步位置的函数（新增）
  const syncPositionToNative = (x: number, y: number, collapsed: boolean) => {
    if (FloatingWindowModule && FloatingWindowModule.updateRNCapsulePosition) {
      FloatingWindowModule.updateRNCapsulePosition({
        x,
        y,
        isCollapsed: collapsed,
      }).catch((error: any) => {
        console.log('同步位置到原生失败:', error);
      });
    }
  };

  // 格式化录音时长（只显示到秒）
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 处理胶囊点击 - 如果是收缩状态则展开（修改为支持位置同步）
  const handleCapsulePress = () => {
    if (isCollapsed.current) {
      const expandedX = screenWidth - capsuleWidth;
      const currentY = currentPosition.current.y;

      // 更新当前位置记录
      currentPosition.current = { x: expandedX, y: currentY };

      // 展开胶囊到完全可见位置
      Animated.spring(translateX, {
        toValue: expandedX,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start(() => {
        // 动画完成后同步位置到原生
        syncPositionToNative(expandedX, currentY, false);
        console.log(`RN胶囊已展开，位置已同步: (${expandedX}, ${currentY})`);
      });
      isCollapsed.current = false;
    } else {
      onPress();
    }
  };

  // 处理拖拽手势 - 手动管理
  const onGestureEvent = (event: any) => {
    if (isDragging.current) {
      const newX = startPosition.current.x + event.nativeEvent.translationX;
      const newY = startPosition.current.y + event.nativeEvent.translationY;

      // 提高右侧收缩的响应性：当胶囊接近右边缘时，稍微向右拖拽就触发收缩预览
      const previewThreshold = screenWidth - capsuleWidth + 10;
      const waveformWidth = 40;

      if (newX > previewThreshold && newX <= screenWidth - waveformWidth) {
        // 预览状态：轻微向右移动但还没完全收缩
        translateX.setValue(Math.min(newX, screenWidth - waveformWidth));
      } else {
        translateX.setValue(newX);
      }
      translateY.setValue(newY);
    }
  };

  const onHandlerStateChange = (event: PanGestureHandlerStateChangeEvent) => {
    if (event.nativeEvent.state === State.BEGAN) {
      // 开始拖拽时，记录当前位置
      isDragging.current = true;
      startPosition.current = {
        x: (translateX as any)._value,
        y: (translateY as any)._value,
      };
    } else if (
      event.nativeEvent.state === State.END ||
      event.nativeEvent.state === State.CANCELLED
    ) {
      isDragging.current = false;

      const { translationX, translationY } = event.nativeEvent;

      // 计算最终位置
      const newX = startPosition.current.x + translationX;
      const newY = startPosition.current.y + translationY;

      // 边界限制
      const safeMargin = 20;
      const waveformWidth = 40; // 波形部分的宽度，调整到40px让字符"0"刚好隐藏

      const maxX = screenWidth - capsuleWidth;
      const maxY = screenHeight - capsuleHeight - safeMargin - 100;

      // 检查是否需要收缩 - 提高灵敏度，当拖拽超过屏幕右边缘时就触发
      const shouldCollapse = newX > screenWidth - capsuleWidth + 20; // 降低触发阈值，提高灵敏度

      let finalX;
      if (shouldCollapse) {
        // 收缩状态：胶囊大部分滑出屏幕，只露出波形部分
        finalX = screenWidth - waveformWidth;
        isCollapsed.current = true;
      } else {
        // 正常状态：应用常规边界限制
        finalX = Math.max(safeMargin, Math.min(maxX, newX));
        isCollapsed.current = false;
      }

      const finalY = Math.max(safeMargin + 50, Math.min(maxY, newY));

      // 更新当前位置记录（新增）
      currentPosition.current = { x: finalX, y: finalY };

      // 动画到最终位置
      Animated.parallel([
        Animated.spring(translateX, {
          toValue: finalX,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.spring(translateY, {
          toValue: finalY,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
      ]).start(() => {
        // 动画完成后同步位置到原生（新增）
        syncPositionToNative(finalX, finalY, shouldCollapse);
        console.log(
          `RN胶囊拖拽结束，位置已同步: (${finalX}, ${finalY}), collapsed: ${shouldCollapse}`
        );
      });
    }
  };

  if (!visible) return null;

  return (
    <PanGestureHandler
      onGestureEvent={onGestureEvent}
      onHandlerStateChange={onHandlerStateChange}
    >
      <Animated.View
        style={[
          styles.container,
          {
            transform: [{ translateX }, { translateY }],
          },
        ]}
      >
        <TouchableOpacity
          style={styles.capsule}
          onPress={handleCapsulePress}
          activeOpacity={0.8}
        >
          {/* 左侧录音状态指示器 - 5个音频波形条 */}
          <View style={styles.leftSection}>
            <View style={styles.statusIndicator}>
              <View
                style={[
                  styles.recordingDot,
                  { opacity: isRecording && !isPaused ? 1 : 0.3 },
                ]}
              />
              <View
                style={[
                  styles.recordingDotMedium,
                  { opacity: isRecording && !isPaused ? 1 : 0.3 },
                ]}
              />
              <View
                style={[
                  styles.recordingDotTall,
                  { opacity: isRecording && !isPaused ? 1 : 0.3 },
                ]}
              />
              <View
                style={[
                  styles.recordingDotMedium,
                  { opacity: isRecording && !isPaused ? 1 : 0.3 },
                ]}
              />
              <View
                style={[
                  styles.recordingDot,
                  { opacity: isRecording && !isPaused ? 1 : 0.3 },
                ]}
              />
            </View>
          </View>

          {/* 中间时长和状态 */}
          <View style={styles.centerSection}>
            <Text style={styles.statusText} numberOfLines={1}>
              {formatDuration(duration)}{' '}
              {isPaused ? '已暂停' : isRecording ? '录音中' : '准备录音'}
            </Text>
          </View>

          {/* 右侧停止按钮 */}
          <TouchableOpacity
            style={styles.rightSection}
            onPress={onStop}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <View style={styles.stopButton}>
              <Image
                source={require('../../assets/images/stop-button.png')}
                style={styles.stopButtonImage}
                resizeMode="contain"
              />
            </View>
          </TouchableOpacity>
        </TouchableOpacity>
      </Animated.View>
    </PanGestureHandler>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 100, // 初始位置
    left: 20,
    zIndex: 1000,
  },

  capsule: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderBottomLeftRadius: 24,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    paddingLeft: 16,
    paddingRight: 8,
    paddingVertical: 8,
    shadowColor: '#64677A',
    shadowOffset: {
      width: -8,
      height: 0,
    },
    shadowOpacity: 0.24,
    shadowRadius: 24,
    elevation: 12,
    minWidth: 180,
    gap: 8,
    overflow: 'hidden',
  },

  leftSection: {
    // 移除margin，使用gap统一控制间距
  },

  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  recordingDot: {
    width: 2,
    height: 8.44,
    backgroundColor: '#417FFB',
    marginHorizontal: 1,
    borderRadius: 1,
  },

  recordingDotMedium: {
    width: 2,
    height: 12.71,
    backgroundColor: '#417FFB',
    marginHorizontal: 1,
    borderRadius: 1,
  },

  recordingDotTall: {
    width: 2,
    height: 17,
    backgroundColor: '#417FFB',
    marginHorizontal: 1,
    borderRadius: 1,
  },

  centerSection: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },

  durationText: {
    fontSize: 15,
    fontWeight: '600',
    fontFamily: typography.fontFamily,
    color: '#2A2B33',
    lineHeight: 24,
  },

  statusText: {
    fontSize: 15,
    fontWeight: '600',
    fontFamily: typography.fontFamily,
    color: '#2A2B33',
    lineHeight: 24,
    numberOfLines: 1,
    flexShrink: 1,
  },

  rightSection: {
    // 移除margin，使用gap统一控制间距
  },

  stopButton: {
    width: 32,
    height: 32,
  },

  stopButtonImage: {
    width: 32,
    height: 32,
  },
});
