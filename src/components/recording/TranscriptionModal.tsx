import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, typography, spacing } from '../../styles';
import { Recording } from '../../types/recording';
import {
  transcriptionService,
  TranscriptionProgress,
} from '../../services/transcription';
import Toast from 'react-native-toast-message';

interface Props {
  visible: boolean;
  recording: Recording | null;
  onClose: () => void;
}

export const TranscriptionModal: React.FC<Props> = ({
  visible,
  recording,
  onClose,
}) => {
  const [transcriptionText, setTranscriptionText] = useState('');
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [progress, setProgress] = useState<TranscriptionProgress | null>(null);
  const [activeTab, setActiveTab] = useState<
    'transcription' | 'analysis' | 'qa'
  >('transcription');
  const [analysisData, setAnalysisData] = useState<any>(null);
  const [question, setQuestion] = useState('');
  const [answer, setAnswer] = useState('');
  const [isAsking, setIsAsking] = useState(false);

  useEffect(() => {
    if (visible && recording && !transcriptionText) {
      handleTranscribe();
    }
  }, [visible, recording]);

  const handleTranscribe = async () => {
    if (!recording) return;

    setIsTranscribing(true);
    try {
      const result = await transcriptionService.transcribeAudio(
        recording,
        (progress) => {
          setProgress(progress);
        }
      );

      setTranscriptionText(result.text);

      // 自动进行AI分析
      const analysis = await transcriptionService.analyzeContent(result.text);
      setAnalysisData(analysis);

      Toast.show({
        type: 'success',
        text1: '转写完成',
        text2: '语音已成功转换为文字',
      });
    } catch (error) {
      console.error('Transcription error:', error);
      Toast.show({
        type: 'error',
        text1: '转写失败',
        text2: '无法完成语音转写',
      });
    } finally {
      setIsTranscribing(false);
      setProgress(null);
    }
  };

  const handleAskQuestion = async () => {
    if (!question.trim() || !transcriptionText) return;

    setIsAsking(true);
    try {
      const response = await transcriptionService.askQuestion(
        transcriptionText,
        question
      );
      setAnswer(response);
    } catch (error) {
      console.error('Question error:', error);
      Toast.show({
        type: 'error',
        text1: '提问失败',
        text2: '无法获取回答',
      });
    } finally {
      setIsAsking(false);
    }
  };

  const handleExport = () => {
    // TODO: 实现导出功能
    Toast.show({
      type: 'info',
      text1: '导出功能',
      text2: '导出功能即将推出',
    });
  };

  const renderTabs = () => (
    <View style={styles.tabs}>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'transcription' && styles.tabActive]}
        onPress={() => setActiveTab('transcription')}
      >
        <Text
          style={[
            styles.tabText,
            activeTab === 'transcription' && styles.tabTextActive,
          ]}
        >
          转写文本
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'analysis' && styles.tabActive]}
        onPress={() => setActiveTab('analysis')}
      >
        <Text
          style={[
            styles.tabText,
            activeTab === 'analysis' && styles.tabTextActive,
          ]}
        >
          AI分析
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'qa' && styles.tabActive]}
        onPress={() => setActiveTab('qa')}
      >
        <Text
          style={[styles.tabText, activeTab === 'qa' && styles.tabTextActive]}
        >
          智能问答
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderTranscriptionContent = () => (
    <ScrollView
      style={styles.contentScroll}
      showsVerticalScrollIndicator={false}
    >
      {isTranscribing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.progressText}>
            {progress?.message || '正在转写...'}
          </Text>
          {progress && (
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: `${progress.progress}%` },
                ]}
              />
            </View>
          )}
        </View>
      ) : (
        <View style={styles.transcriptionContent}>
          <Text style={styles.transcriptionText}>
            {transcriptionText || '暂无转写内容'}
          </Text>
        </View>
      )}
    </ScrollView>
  );

  const renderAnalysisContent = () => (
    <ScrollView
      style={styles.contentScroll}
      showsVerticalScrollIndicator={false}
    >
      {analysisData ? (
        <View style={styles.analysisContent}>
          <View style={styles.analysisSection}>
            <Text style={styles.analysisSectionTitle}>会议总结</Text>
            <Text style={styles.analysisText}>{analysisData.summary}</Text>
          </View>

          <View style={styles.analysisSection}>
            <Text style={styles.analysisSectionTitle}>关键要点</Text>
            {analysisData.keyPoints.map((point: string, index: number) => (
              <View key={index} style={styles.keyPointItem}>
                <Text style={styles.keyPointBullet}>•</Text>
                <Text style={styles.keyPointText}>{point}</Text>
              </View>
            ))}
          </View>

          <View style={styles.analysisSection}>
            <Text style={styles.analysisSectionTitle}>行动事项</Text>
            {analysisData.actionItems.map((item: string, index: number) => (
              <View key={index} style={styles.actionItem}>
                <Ionicons
                  name="checkbox-outline"
                  size={16}
                  color={colors.primary}
                />
                <Text style={styles.actionItemText}>{item}</Text>
              </View>
            ))}
          </View>

          {analysisData.participants && (
            <View style={styles.analysisSection}>
              <Text style={styles.analysisSectionTitle}>参与人员</Text>
              <View style={styles.participantsList}>
                {analysisData.participants.map(
                  (participant: string, index: number) => (
                    <View key={index} style={styles.participantChip}>
                      <Text style={styles.participantName}>{participant}</Text>
                    </View>
                  )
                )}
              </View>
            </View>
          )}
        </View>
      ) : (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>正在分析内容...</Text>
        </View>
      )}
    </ScrollView>
  );

  const renderQAContent = () => (
    <View style={styles.qaContainer}>
      <ScrollView style={styles.qaScroll} showsVerticalScrollIndicator={false}>
        {answer && (
          <View style={styles.answerContainer}>
            <View style={styles.answerHeader}>
              <Ionicons
                name="chatbubble-ellipses"
                size={20}
                color={colors.primary}
              />
              <Text style={styles.answerLabel}>AI回答</Text>
            </View>
            <Text style={styles.answerText}>{answer}</Text>
          </View>
        )}
      </ScrollView>

      <View style={styles.questionInputContainer}>
        <TextInput
          style={styles.questionInput}
          placeholder="输入您的问题..."
          value={question}
          onChangeText={setQuestion}
          multiline
          maxLength={200}
        />
        <TouchableOpacity
          style={[styles.askButton, isAsking && styles.askButtonDisabled]}
          onPress={handleAskQuestion}
          disabled={isAsking || !question.trim()}
        >
          {isAsking ? (
            <ActivityIndicator size="small" color={colors.background} />
          ) : (
            <Ionicons name="send" size={20} color={colors.background} />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {recording?.title || '转写结果'}
            </Text>
            <View style={styles.headerActions}>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={handleExport}
              >
                <Ionicons
                  name="share-outline"
                  size={20}
                  color={colors.primary}
                />
              </TouchableOpacity>
              <TouchableOpacity style={styles.headerButton} onPress={onClose}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
          </View>

          {renderTabs()}

          <View style={styles.tabContent}>
            {activeTab === 'transcription' && renderTranscriptionContent()}
            {activeTab === 'analysis' && renderAnalysisContent()}
            {activeTab === 'qa' && renderQAContent()}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },

  modalContent: {
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
    paddingTop: spacing.lg,
  },

  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },

  modalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    flex: 1,
  },

  headerActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },

  headerButton: {
    padding: spacing.xs,
  },

  tabs: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.separator,
  },

  tab: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: 'center',
  },

  tabActive: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },

  tabText: {
    fontSize: typography.fontSize.base,
    color: colors.textSecondary,
  },

  tabTextActive: {
    color: colors.primary,
    fontWeight: typography.fontWeight.medium,
  },

  tabContent: {
    flex: 1,
  },

  contentScroll: {
    flex: 1,
  },

  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.xl * 2,
  },

  progressText: {
    fontSize: typography.fontSize.base,
    color: colors.text,
    marginTop: spacing.md,
  },

  progressBar: {
    width: 200,
    height: 4,
    backgroundColor: colors.separator,
    borderRadius: 2,
    marginTop: spacing.md,
    overflow: 'hidden',
  },

  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
  },

  loadingText: {
    fontSize: typography.fontSize.base,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },

  transcriptionContent: {
    padding: spacing.lg,
  },

  transcriptionText: {
    fontSize: typography.fontSize.base,
    lineHeight: 24,
    color: colors.text,
  },

  analysisContent: {
    padding: spacing.lg,
  },

  analysisSection: {
    marginBottom: spacing.xl,
  },

  analysisSectionTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    marginBottom: spacing.sm,
  },

  analysisText: {
    fontSize: typography.fontSize.base,
    lineHeight: 22,
    color: colors.text,
  },

  keyPointItem: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
  },

  keyPointBullet: {
    fontSize: typography.fontSize.base,
    color: colors.primary,
    marginRight: spacing.sm,
  },

  keyPointText: {
    flex: 1,
    fontSize: typography.fontSize.base,
    lineHeight: 22,
    color: colors.text,
  },

  actionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },

  actionItemText: {
    flex: 1,
    fontSize: typography.fontSize.base,
    lineHeight: 22,
    color: colors.text,
    marginLeft: spacing.sm,
  },

  participantsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },

  participantChip: {
    backgroundColor: colors.primary + '20',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: 16,
  },

  participantName: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
  },

  qaContainer: {
    flex: 1,
  },

  qaScroll: {
    flex: 1,
    padding: spacing.lg,
  },

  answerContainer: {
    backgroundColor: colors.backgroundSecondary,
    padding: spacing.md,
    borderRadius: 12,
    marginBottom: spacing.md,
  },

  answerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },

  answerLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: typography.fontWeight.medium,
  },

  answerText: {
    fontSize: typography.fontSize.base,
    lineHeight: 22,
    color: colors.text,
  },

  questionInputContainer: {
    flexDirection: 'row',
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.separator,
    backgroundColor: colors.background,
  },

  questionInput: {
    flex: 1,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 20,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.fontSize.base,
    color: colors.text,
    maxHeight: 100,
  },

  askButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: spacing.sm,
  },

  askButtonDisabled: {
    opacity: 0.5,
  },
});
