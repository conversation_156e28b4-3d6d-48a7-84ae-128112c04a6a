import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../styles';
import { NoteData } from '../../types/recording';

interface DisplayNoteModalProps {
  visible: boolean;
  onClose: () => void;
  notes: NoteData[];
}

const DisplayNoteModal: React.FC<DisplayNoteModalProps> = ({
  visible,
  onClose,
  notes,
}) => {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.noteModalOverlay}>
        <View style={styles.noteModalContainer}>
          <View style={styles.noteModalHeader}>
            <Text style={styles.noteModalTitle}>记一下</Text>
            <TouchableOpacity
              onPress={onClose}
              style={styles.noteModalCloseButton}
            >
              <Ionicons name="close" size={24} color={colors.text.primary} />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.noteModalContent}>
            {notes.map((note, index) => (
              <View key={index} style={styles.noteItem}>
                <View style={styles.noteHeader}>
                  <Ionicons
                    name="bookmark"
                    size={16}
                    color={colors.warning}
                    style={styles.noteIcon}
                  />
                  <Text style={styles.noteTime}>{note.markTime}</Text>
                </View>
                <Text style={styles.noteText}>{note.note}</Text>
              </View>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  noteModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noteModalContainer: {
    width: '90%',
    maxWidth: 400,
    backgroundColor: colors.background.primary,
    borderRadius: 12,
    maxHeight: '80%',
  },
  noteModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  noteModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  noteModalCloseButton: {
    padding: 4,
  },
  noteModalContent: {
    padding: 16,
    maxHeight: 400,
  },
  noteItem: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  noteHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  noteIcon: {
    marginRight: 8,
  },
  noteTime: {
    fontSize: 12,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  noteText: {
    fontSize: 16,
    lineHeight: 24,
    color: colors.text.primary,
  },
});

export default DisplayNoteModal;
