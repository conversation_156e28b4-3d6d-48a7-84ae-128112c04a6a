import { useState, useRef, useCallback, useEffect } from 'react';
import { ScrollView } from 'react-native';

// 打字机效果配置
const TYPING_SPEED = 50; // 正常打字速度（毫秒）
const CATCH_UP_SPEED = 20; // 追赶速度（毫秒）
const BATCH_SIZE = 1; // 正常批量大小（字符数）
const CATCH_UP_BATCH_SIZE = 3; // 追赶批量大小（字符数）
const MAX_LAG = 50; // 最大滞后字符数
const CURSOR_BLINK_SPEED = 500; // 光标闪烁速度（毫秒）

interface UseTypewriterProps {
  receivedContent: string;
  isStreamingComplete: boolean;
  scrollViewRef: React.RefObject<ScrollView>;
}

interface UseTypewriterReturn {
  displayedContent: string;
  isTyping: boolean;
  cursorVisible: boolean;
  startTypewriter: () => void;
  stopTypewriter: () => void;
  skipTypewriterAnimation: () => void;
  resetTypewriter: () => void;
}

export const useTypewriter = ({
  receivedContent,
  isStreamingComplete,
  scrollViewRef,
}: UseTypewriterProps): UseTypewriterReturn => {
  // 打字机状态
  const [displayedContent, setDisplayedContent] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [cursorVisible, setCursorVisible] = useState(true);

  // 定时器引用
  const typewriterTimerRef = useRef<NodeJS.Timeout | null>(null);
  const cursorTimerRef = useRef<NodeJS.Timeout | null>(null);
  const receivedContentRef = useRef<string>('');
  const isStreamingCompleteRef = useRef<boolean>(false);

  // 同步 receivedContent 和 isStreamingComplete 到 ref
  useEffect(() => {
    receivedContentRef.current = receivedContent;
  }, [receivedContent]);

  useEffect(() => {
    isStreamingCompleteRef.current = isStreamingComplete;
  }, [isStreamingComplete]);

  // 光标闪烁效果
  useEffect(() => {
    const blinkCursor = () => {
      setCursorVisible((prev) => !prev);
      cursorTimerRef.current = setTimeout(blinkCursor, CURSOR_BLINK_SPEED);
    };

    blinkCursor();

    return () => {
      if (cursorTimerRef.current) {
        clearTimeout(cursorTimerRef.current);
      }
    };
  }, []);

  // 启动打字机效果
  const startTypewriter = useCallback(() => {
    if (typewriterTimerRef.current || !receivedContent) return;

    console.log('⌨️ 启动打字机效果');
    setIsTyping(true);

    const typewriterTick = () => {
      setDisplayedContent((current) => {
        // 使用 ref 获取最新的 receivedContent
        const currentReceived = receivedContentRef.current;

        // 如果已显示完毕
        if (current.length >= currentReceived.length) {
          if (isStreamingCompleteRef.current) {
            console.log('⌨️ 打字机完成');
            setIsTyping(false);
            return current;
          }
          // 等待更多数据
          typewriterTimerRef.current = setTimeout(typewriterTick, TYPING_SPEED);
          return current;
        }

        // 计算显示速度和批量大小
        const lag = currentReceived.length - current.length;
        const speed = lag > MAX_LAG ? CATCH_UP_SPEED : TYPING_SPEED;
        const batchSize = lag > MAX_LAG ? CATCH_UP_BATCH_SIZE : BATCH_SIZE;

        // 逐字符显示（正确处理Unicode字符）
        const chars = Array.from(currentReceived);
        const nextLength = Math.min(current.length + batchSize, chars.length);
        const nextContent = chars.slice(0, nextLength).join('');

        // 继续定时器
        typewriterTimerRef.current = setTimeout(typewriterTick, speed);

        // 自动滚动到底部
        setTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 50);

        return nextContent;
      });
    };

    typewriterTick();
  }, [receivedContent, scrollViewRef]);

  // 停止打字机效果
  const stopTypewriter = useCallback(() => {
    if (typewriterTimerRef.current) {
      clearTimeout(typewriterTimerRef.current);
      typewriterTimerRef.current = null;
    }
    setIsTyping(false);
    console.log('⌨️ 停止打字机效果');
  }, []);

  // 跳过打字机动画
  const skipTypewriterAnimation = useCallback(() => {
    stopTypewriter();
    setDisplayedContent(receivedContent);
    console.log('⌨️ 跳过打字机动画');
  }, [receivedContent, stopTypewriter]);

  // 重置打字机状态
  const resetTypewriter = useCallback(() => {
    stopTypewriter();
    setDisplayedContent('');
    receivedContentRef.current = ''; // 重置 ref
    isStreamingCompleteRef.current = false; // 重置流式完成状态
    console.log('⌨️ 重置打字机状态');
  }, [stopTypewriter]);

  // 监听receivedContent变化，启动打字机
  useEffect(() => {
    if (receivedContent && !isTyping && !typewriterTimerRef.current) {
      startTypewriter();
    }
  }, [receivedContent, isTyping, startTypewriter]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (typewriterTimerRef.current) {
        clearTimeout(typewriterTimerRef.current);
      }
      if (cursorTimerRef.current) {
        clearTimeout(cursorTimerRef.current);
      }
    };
  }, []);

  return {
    displayedContent,
    isTyping,
    cursorVisible,
    startTypewriter,
    stopTypewriter,
    skipTypewriterAnimation,
    resetTypewriter,
  };
};
