import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  TextInput,
  Keyboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Recording } from '@/types';
import { useRecordingStore } from '../../../stores/recordingStore';
import { typography } from '@/styles';
import {
  sendStreamingMessageWithSession,
  SSECallbacks,
} from '../../../services/api/chatService';
import { DialogUtil } from '@/utils/dialogUtil';

import { useTypewriter } from './hooks/useTypewriter';
import MessageRenderer from './components/MessageRenderer';

interface AIChatContentProps {
  recording?: Recording;
}

// 常量定义
const MAX_CONTENT_LENGTH = 10000; // 最大内容长度，防止内存问题

const AIChatContent: React.FC<AIChatContentProps> = ({ recording }) => {
  const [inputText, setInputText] = useState('');
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentEventSource, setCurrentEventSource] =
    useState<EventSource | null>(null);
  const [isSending, setIsSending] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false); // 新增：是否正在流式输出
  const [streamingMessage, setStreamingMessage] = useState<
    import('@/types').ChatMessage | null
  >(null);

  // 打字机效果相关状态
  const [receivedContent, setReceivedContent] = useState(''); // SSE接收的完整内容
  const [isStreamingComplete, setIsStreamingComplete] = useState(false); // SSE是否完成

  const scrollViewRef = useRef<ScrollView>(null);
  const updateTimerRef = useRef<NodeJS.Timeout | null>(null);
  const insets = useSafeAreaInsets();

  // 使用打字机 Hook
  const {
    displayedContent,
    isTyping,
    cursorVisible,
    stopTypewriter,
    skipTypewriterAnimation,
    resetTypewriter,
  } = useTypewriter({
    receivedContent,
    isStreamingComplete,
    scrollViewRef,
  });

  // 使用 recording store
  const updateRecording = useRecordingStore((state) => state.updateRecording);

  // 从 recording 中获取聊天历史，如果没有则显示欢迎界面
  const baseMessages = recording?.chatHistory || [];

  // 合并基础消息和流式消息 - 避免重复显示相同ID的消息
  const messages = streamingMessage
    ? [
        ...baseMessages.filter((msg) => msg.id !== streamingMessage.id), // 过滤掉相同ID的消息
        streamingMessage,
      ]
    : baseMessages;
  const showWelcome = baseMessages.length === 0 && !streamingMessage;

  // 确保 messages 始终是数组
  const safeMessages = Array.isArray(messages) ? messages : [];

  // 调试信息 - 检查历史消息状态
  React.useEffect(() => {
    if (baseMessages.length > 0) {
      console.log('📚 历史消息状态检查:', {
        baseMessagesCount: baseMessages.length,
        lastMessage: baseMessages[baseMessages.length - 1],
        lastMessageContent:
          baseMessages[baseMessages.length - 1]?.content?.substring(0, 50) +
          '...',
        hasStreamingMessage: !!streamingMessage,
        totalSafeMessages: safeMessages.length,
      });
    }
  }, [baseMessages.length, streamingMessage?.id]);

  // 简化调试信息 - 只在有问题时输出
  // if (streamingMessage) {
  //   console.log('🔍 流式消息状态:', {
  //     streamingMessageId: streamingMessage?.id,
  //     streamingMessageContent: streamingMessage?.content?.substring(0, 30) + '...',
  //     receivedContentLength: receivedContent.length,
  //     displayedContentLength: displayedContent.length,
  //     isTyping,
  //     isStreamingComplete,
  //   });
  // }

  // 检查是否有有效的会话数据
  const hasValidSession = recording?.sessionData?.sessionId;

  // 调试信息
  React.useEffect(() => {
    if (recording) {
      console.log('🔍 录音数据:', {
        id: recording.id,
        hasSessionData: !!recording.sessionData,
        sessionId: recording.sessionData?.sessionId,
        sessionDataStructure: recording.sessionData
          ? Object.keys(recording.sessionData)
          : null,
      });
    }
  }, [recording]);

  // 组件卸载时清理SSE连接和定时器
  useEffect(() => {
    return () => {
      // 清理SSE连接
      if (currentEventSource) {
        console.log('🧹 组件卸载，清理SSE连接');
        currentEventSource.close();
      }

      // 清理定时器
      if (updateTimerRef.current) {
        clearTimeout(updateTimerRef.current);
        updateTimerRef.current = null;
      }

      // 注意：不在这里重置状态，因为这可能在组件重新渲染时被意外触发
      // 状态重置应该在具体的业务逻辑中处理
    };
  }, [currentEventSource]);

  // 键盘事件监听
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      (e) => {
        setKeyboardHeight(e.endCoordinates.height);
        setIsKeyboardVisible(true);
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardHeight(0);
        setIsKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  // 预设问题
  const presetQuestions = [
    '帮我整理一下会议的待办事项',
    '帮我总结一下会议的核心要点',
  ];

  // 停止当前的SSE流
  const handleStopStreaming = () => {
    console.log('🛑 用户主动停止流式输出');

    if (currentEventSource) {
      currentEventSource.close();
      setCurrentEventSource(null);
    }

    // 重置所有相关状态
    setIsStreaming(false);
    setIsSending(false);
    setIsLoading(false);
    setIsStreamingComplete(true);

    // 停止打字机效果
    stopTypewriter();

    // 如果有正在流式输出的消息，保存当前内容到store
    if (streamingMessage && receivedContent) {
      const finalBotMessage: import('@/types').ChatMessage = {
        ...streamingMessage,
        content: receivedContent,
        status: 'sent',
      };

      const currentHistory = recording?.chatHistory || [];
      const updatedHistory = [...currentHistory, finalBotMessage];

      if (recording) {
        updateRecording(recording.id, {
          chatHistory: updatedHistory,
        });
        console.log('💾 停止时保存消息到store:', finalBotMessage.id);
      }
    }

    // 清理流式消息状态
    setStreamingMessage(null);
    setReceivedContent('');
  };

  // 发送聊天消息（流式响应）
  const handleSendMessage = async (message: string) => {
    if (!message.trim() || !hasValidSession || isLoading || isSending) {
      if (!hasValidSession) {
        DialogUtil.alert('提示', '请先打开AI功能界面创建会话');
      }
      if (isSending) {
        console.log('⚠️ 消息正在发送中，忽略重复请求');
      }
      if (isLoading) {
        console.log('⚠️ 正在加载中，忽略请求');
      }
      return;
    }

    console.log('🚀 开始发送消息:', message.trim());

    // 立即设置发送状态，防止快速重复点击
    setIsSending(true);
    setIsLoading(true);

    // 关闭之前的SSE连接
    if (currentEventSource) {
      console.log('🔄 关闭之前的SSE连接');
      currentEventSource.close();
      setCurrentEventSource(null);
    }

    // 创建用户消息
    const userMessage: import('@/types').ChatMessage = {
      id: Date.now().toString() + '_user',
      role: 'user',
      content: message.trim(),
      type: 'input',
      timestamp: new Date(),
      status: 'sending',
    };

    // 立即显示用户消息并更新store
    const updatedHistory = [
      ...baseMessages,
      { ...userMessage, status: 'sent' as const },
    ];
    if (recording) {
      updateRecording(recording.id, {
        chatHistory: updatedHistory,
      });
    }
    setInputText('');

    // 创建AI回复消息（使用本地状态，不立即存储到store）
    const botMessageId = Date.now().toString() + '_bot';
    const initialBotMessage: import('@/types').ChatMessage = {
      id: botMessageId,
      role: 'assistant',
      content: '', // 初始为空，让加载状态显示
      type: 'text',
      timestamp: new Date(),
      status: 'sending',
    };

    // 重置打字机状态和流式状态
    resetTypewriter();
    setReceivedContent(''); // 重置接收内容
    setIsStreamingComplete(false);

    // 设置流式消息到本地状态 - 立即显示"AI正在思考中"
    console.log('🤖 设置流式消息:', initialBotMessage);
    setStreamingMessage(initialBotMessage);

    // 自动滚动到底部显示新消息
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);

    // 用于累积文本内容的变量
    let accumulatedContent = '';

    try {
      // 设置SSE回调
      const sseCallbacks: SSECallbacks = {
        onopen: () => {
          console.log('🔗 开始接收AI回复...');
          setIsStreaming(true); // 开始流式输出
        },

        onmessage: (event) => {
          console.log('📨 SSE事件:', event.type, event.data);

          switch (event.type) {
            case 'text': {
              // 清理文本内容：移除不必要的引号和处理换行
              let cleanText = event.data;

              // 移除开头和结尾的引号（但不是所有引号，只有完全包裹的）
              if (typeof cleanText === 'string') {
                if (
                  cleanText.startsWith('"') &&
                  cleanText.endsWith('"') &&
                  cleanText.length > 1
                ) {
                  cleanText = cleanText.slice(1, -1);
                }
                // 处理换行符和其他转义字符
                cleanText = cleanText
                  .replace(/\\n/g, '\n')
                  .replace(/\\r/g, '\r')
                  .replace(/\\t/g, '\t')
                  .replace(/\\"/g, '"');
              }

              // 累积文本内容到本地变量
              accumulatedContent += cleanText;

              // 内容长度限制，防止内存问题
              if (accumulatedContent.length > MAX_CONTENT_LENGTH) {
                console.warn('⚠️ 内容长度超过限制，截断处理');
                accumulatedContent =
                  accumulatedContent.substring(0, MAX_CONTENT_LENGTH) +
                  '\n\n[内容过长，已截断]';
              }

              // 更新接收内容，触发打字机效果
              setReceivedContent(accumulatedContent);

              // 更新现有streamingMessage的内容（streamingMessage应该在开始时已经设置）
              setStreamingMessage((prev) => {
                if (!prev) {
                  // 如果streamingMessage不存在，说明有问题，但我们仍然创建一个
                  console.warn(
                    '⚠️ streamingMessage不存在，可能被意外清空，重新创建:',
                    botMessageId
                  );
                  return {
                    id: botMessageId,
                    role: 'assistant' as const,
                    content: accumulatedContent,
                    type: 'text' as const,
                    timestamp: new Date(),
                    status: 'sending' as const,
                  };
                }
                // 正常情况：更新现有streamingMessage的内容
                return {
                  ...prev,
                  content: accumulatedContent,
                };
              });

              break;
            }

            case 'done': {
              // 完成响应
              console.log('🏁 对话完成，数据:', event.data);

              // 立即保存最终消息到store，不等待打字机效果
              const finalBotMessage: import('@/types').ChatMessage = {
                id: botMessageId,
                role: 'assistant',
                content: accumulatedContent, // 只保留实际内容
                type: 'text',
                timestamp: new Date(),
                status: 'sent',
              };

              // 更新到store，包含用户消息和最终的AI消息
              const finalHistory = [...updatedHistory, finalBotMessage];

              if (recording) {
                updateRecording(recording.id, {
                  chatHistory: finalHistory,
                });
                console.log(
                  '💾 消息已保存到store:',
                  finalBotMessage.id,
                  finalBotMessage.content?.substring(0, 50) + '...'
                );
              }

              // 标记流式传输完成
              setIsStreamingComplete(true);

              // 立即清理流式消息状态，避免重复渲染
              setStreamingMessage(null);
              console.log('🧹 立即清理流式消息状态');

              // 主动关闭EventSource连接
              if (currentEventSource) {
                console.log('🔒 主动关闭SSE连接');
                currentEventSource.close();
              }

              // 清理EventSource引用和发送状态
              setCurrentEventSource(null);
              setIsLoading(false);
              setIsSending(false);
              setIsStreaming(false); // 停止流式输出状态
              break;
            }

            case 'error': {
              // 处理错误
              console.error('❌ SSE错误:', event.data);

              // 清理打字机状态
              stopTypewriter();

              // 根据错误类型显示不同的错误信息
              const errorData = event.data || '';
              let errorMessage = '抱歉，处理您的消息时出现错误，请重试。';
              if (errorData.includes('NullPointerException')) {
                errorMessage = '服务暂时不可用，请稍后重试。';
              }

              // 更新错误状态 - 显示错误消息
              const errorBotMessage: import('@/types').ChatMessage = {
                id: botMessageId,
                role: 'assistant',
                content: errorMessage,
                type: 'text',
                timestamp: new Date(),
                status: 'error',
              };

              const errorHistory = [...updatedHistory, errorBotMessage];

              if (recording) {
                updateRecording(recording.id, {
                  chatHistory: errorHistory,
                });
              }

              // 清理所有状态
              setCurrentEventSource(null);
              setStreamingMessage(null);
              setReceivedContent('');
              setIsStreamingComplete(false);
              setIsLoading(false);
              setIsSending(false);
              setIsStreaming(false);
              break;
            }
          }
        },

        onclose: () => {
          console.log('🔚 SSE连接已关闭');

          // 标记流式传输完成
          setIsStreamingComplete(true);

          // 清理打字机状态
          stopTypewriter();

          // 如果有未完成的内容，跳过动画直接显示
          if (
            receivedContent &&
            displayedContent.length < receivedContent.length
          ) {
            skipTypewriterAnimation();
          }

          setCurrentEventSource(null);
          setIsLoading(false);
          setIsSending(false);
          setIsStreaming(false);
        },

        onerror: (error) => {
          console.error('❌ SSE连接错误:', error);

          // 检查错误类型
          const errorMessage = error?.message || error?.toString() || '';
          const errorData = error?.data || '';

          // 如果是服务端业务错误（已通过error事件处理），不需要额外处理
          if (
            errorData.includes('NullPointerException') ||
            errorData.includes('error')
          ) {
            console.log('🔄 服务端错误已通过error事件处理，跳过onerror处理');
            return;
          }

          // 只处理真正的网络连接错误
          const isNetworkError =
            errorMessage.includes('network') ||
            errorMessage.includes('timeout') ||
            errorMessage.includes('connection') ||
            errorMessage.includes('fetch');

          if (isNetworkError) {
            // 网络错误时创建错误消息
            const networkErrorMessage: import('@/types').ChatMessage = {
              id: botMessageId,
              role: 'assistant',
              content: '网络连接出现问题，请检查网络后重试。',
              type: 'text',
              timestamp: new Date(),
              status: 'error',
            };

            const networkErrorHistory = [
              ...updatedHistory,
              networkErrorMessage,
            ];

            if (recording) {
              updateRecording(recording.id, {
                chatHistory: networkErrorHistory,
              });
            }

            setIsLoading(false);
            setIsSending(false);
            setIsStreaming(false);
            setStreamingMessage(null);
            DialogUtil.alert('连接错误', '网络连接出现问题，请重试');
          }
          // 对于其他类型错误，只记录日志
        },
      };

      // 发送流式消息
      const eventSource = await sendStreamingMessageWithSession(
        recording!.sessionData!,
        message.trim(),
        sseCallbacks,
        {
          // 只有在录音有有效转写结果时才传递files参数
          // 暂时不传递files参数，避免服务端NullPointerException
          // files: recording?.transcription && recording.transcription.length > 0 ? [{
          //   fileId: recording.id,
          //   fileName: recording.title,
          //   fileType: 'audio',
          // }] : undefined,
          knowledgeId: recording?.knowledgeId || null,
        }
      );

      // 保存EventSource引用以便后续管理
      setCurrentEventSource(eventSource as unknown as EventSource);
    } catch (error) {
      console.error('❌ 消息发送失败:', error);

      // 更新用户消息状态为错误
      const errorUserMessage = { ...userMessage, status: 'error' as const };
      const errorBotMessage: import('@/types').ChatMessage = {
        id: botMessageId,
        role: 'assistant',
        content: '发送失败，请重试',
        type: 'text',
        timestamp: new Date(),
        status: 'error',
      };

      const errorHistory = [...baseMessages, errorUserMessage, errorBotMessage];

      if (recording) {
        updateRecording(recording.id, {
          chatHistory: errorHistory,
        });
      }

      setIsLoading(false);
      setIsSending(false);
      setIsStreaming(false);
      setStreamingMessage(null);
      DialogUtil.alert('发送失败', '消息发送失败，请重试');
    }
  };

  const renderPresetQuestion = (question: string, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.presetQuestionCard}
      onPress={() => handleSendMessage(question)}
    >
      <Image
        source={require('../../../../assets/images/question-icon.png')}
        style={styles.questionIconImage}
        resizeMode="contain"
      />
      <Text style={styles.presetQuestionText}>{question}</Text>
      <Image
        source={require('../../../../assets/images/chevron-right.png')}
        style={styles.chevronIconImage}
        resizeMode="contain"
      />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* 内容区域 */}
      <ScrollView
        ref={scrollViewRef}
        style={[
          styles.contentWrapper,
          {
            marginBottom: 90,
          },
        ]}
        contentContainerStyle={[
          styles.contentContainer,
          !showWelcome && styles.chatContainer,
          {
            paddingBottom: isKeyboardVisible ? keyboardHeight + 32 : 32,
          },
        ]}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="interactive"
      >
        {showWelcome ? (
          <>
            {/* 助手简介 */}
            <View style={styles.assistantIntro}>
              {/* Logo */}
              <View style={styles.logoContainer}>
                <Image
                  source={require('../../../../assets/logo.png')}
                  style={styles.logoImage}
                  resizeMode="contain"
                />
              </View>

              {/* 欢迎语 */}
              <View style={styles.welcomeContainer}>
                <View style={styles.welcomeHeader}>
                  <Text style={styles.welcomeText}>Hi！我是</Text>
                  <Image
                    source={require('../../../../assets/images/gradient-name-xiaozi.png')}
                    style={styles.gradientNameImage}
                    resizeMode="contain"
                  />
                  <Text style={styles.roleText}>你的全能办公搭子</Text>
                </View>
              </View>

              <Text style={styles.description}>
                我能学习和理解人类语言，提供业务知识解答服务
              </Text>
            </View>

            {/* 预设问题 */}
            <View style={styles.presetQuestions}>
              {presetQuestions.map(renderPresetQuestion)}
            </View>

            {/* 会话状态提示 */}
            {!hasValidSession && (
              <View style={styles.sessionTipContainer}>
                <Ionicons
                  name="information-circle-outline"
                  size={16}
                  color="#FF6B6B"
                />
                <Text style={styles.sessionTipText}>
                  请先打开AI功能界面创建会话后再进行对话
                </Text>
              </View>
            )}
          </>
        ) : (
          <>
            {/* 聊天消息 */}
            {safeMessages
              .map((message) => {
                // 安全检查消息对象
                if (!message || !message.id || !message.role) {
                  console.warn('⚠️ 无效的消息对象:', message);
                  return null;
                }

                // console.log(
                //   '🎨 渲染消息:',
                //   message.id,
                //   message.role,
                //   message.content?.substring(0, 30) + '...',
                //   'isStreamingMessage:', streamingMessage && message.id === streamingMessage.id
                // );

                try {
                  return (
                    <MessageRenderer
                      key={message.id}
                      message={message}
                      streamingMessage={streamingMessage}
                      displayedContent={displayedContent}
                      isTyping={isTyping}
                      cursorVisible={cursorVisible}
                      receivedContent={receivedContent}
                      isStreamingComplete={isStreamingComplete}
                      skipTypewriterAnimation={skipTypewriterAnimation}
                      styles={styles}
                    />
                  );
                } catch (error) {
                  console.error('❌ 渲染消息时出错:', error, message);
                  return null;
                }
              })
              .filter(Boolean)}
          </>
        )}
      </ScrollView>

      {/* 底部输入框 - 固定在底部 */}
      <View
        style={[styles.inputWrapper, { bottom: Math.max(insets.bottom, 16) }]}
      >
        <LinearGradient
          colors={['#3053ED', '#5279FF80', '#3EDDB6']}
          start={{ x: -0.12, y: -0.038 }}
          end={{ x: 1.024, y: 1.047 }}
          style={styles.inputBorder}
        >
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="点击输入与录音相关的问题"
              placeholderTextColor="#B0B2BF"
              value={inputText}
              onChangeText={setInputText}
              multiline={true}
              autoCorrect={false}
              returnKeyType="send"
              onSubmitEditing={() => {
                if (inputText.trim() && !isLoading && !isSending) {
                  handleSendMessage(inputText);
                }
              }}
              editable={!isLoading && !isSending}
              textAlignVertical="top"
            />
            <View style={styles.inputActions}>
              {isStreaming ? (
                // 流式输出时显示停止按钮
                <TouchableOpacity
                  style={styles.stopButtonContainer}
                  onPress={handleStopStreaming}
                >
                  <LinearGradient
                    colors={['#3053ED', '#5279FF80', '#3EDDB6']}
                    start={{ x: -0.12, y: -0.038 }}
                    end={{ x: 1.024, y: 1.047 }}
                    style={styles.stopButton}
                  >
                    <Ionicons name="stop" size={16} color="#FFFFFF" />
                  </LinearGradient>
                </TouchableOpacity>
              ) : (
                // 正常状态显示发送按钮
                <TouchableOpacity
                  style={[
                    styles.sendButton,
                    (!inputText.trim() || isLoading || isSending) &&
                      styles.sendButtonDisabled,
                  ]}
                  onPress={() => handleSendMessage(inputText)}
                  disabled={!inputText.trim() || isLoading || isSending}
                >
                  <Image
                    source={require('../../../../assets/images/send-icon.png')}
                    style={styles.sendIconImage}
                    resizeMode="contain"
                  />
                </TouchableOpacity>
              )}
            </View>
          </View>
        </LinearGradient>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    backgroundColor: 'transparent',
  },
  contentWrapper: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  contentContainer: {
    alignItems: 'center',
    padding: 16,
    paddingBottom: 16,
    gap: 16,
    minHeight: '100%',
    backgroundColor: 'transparent',
  },
  chatContainer: {
    alignItems: 'stretch',
    gap: 12,
    paddingBottom: 32,
    paddingLeft: 10, // 增加左侧内间距，与右侧保持一致
    paddingRight: 10, // 保持右侧内间距
    minHeight: 'auto',
    backgroundColor: 'transparent',
  },
  inputWrapper: {
    position: 'absolute',
    left: 10, // 与chatContainer的paddingLeft保持一致
    right: 10, // 与chatContainer的paddingRight保持一致
    paddingTop: 16,
    backgroundColor: 'transparent',
  },
  assistantIntro: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  logoContainer: {
    width: 64,
    height: 64,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  logoImage: {
    width: 64,
    height: 64,
    borderRadius: 20,
  },
  welcomeContainer: {
    alignItems: 'center',
    width: 272,
  },
  welcomeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
    flexWrap: 'nowrap',
  },
  welcomeText: {
    fontSize: 18,
    fontFamily: typography.fontFamily,
    fontWeight: '500',
    color: '#2A2B33',
    lineHeight: 24, // 1.3333 * 18 = 24
  },
  gradientNameImage: {
    width: 35,
    height: 17,
  },
  roleText: {
    fontSize: 16,
    fontFamily: typography.fontFamily,
    fontWeight: '500',
    color: '#2A2B33',
    textAlign: 'center',
    lineHeight: 24, // 1.5 * 16 = 24
  },
  description: {
    fontSize: 12,
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    color: '#64677A',
    textAlign: 'center',
    width: 272,
    lineHeight: 18, // 1.5 * 12 = 18
  },
  presetQuestions: {
    width: '100%',
    gap: 8,
    alignItems: 'center',
  },
  presetQuestionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#F2F3F5',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8, // 增加垂直内边距，确保图标和文字有足够空间居中
    gap: 4,
    shadowColor: '#64677A',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 1,
    // 移除固定宽度，改为自适应宽度
    alignSelf: 'center', // 居中对齐
    minHeight: 36, // 设置最小高度，确保有足够空间
  },
  questionIconImage: {
    width: 20,
    height: 20,
  },
  presetQuestionText: {
    // 移除 flex: 1，改为自适应宽度
    fontSize: 12,
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    color: '#414352',
    textAlign: 'center',
    lineHeight: 20, // 1.6666 * 12 = 20
  },
  chevronIconImage: {
    width: 12,
    height: 12,
  },

  inputBorder: {
    borderRadius: 16,
    padding: 1.5,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center', // 改为居中对齐，让发送按钮在单行文本时居中
    backgroundColor: '#FFFFFF',
    borderRadius: 14.5,
    paddingLeft: 16,
    paddingRight: 8,
    paddingVertical: 8,
    gap: 8,
    minHeight: 40, // 设置最小高度
  },
  input: {
    flex: 1,
    fontSize: 15,
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    color: '#2A2B33',
    lineHeight: 22, // 调整行高以改善垂直对齐
    minHeight: 24,
    maxHeight: 120, // 限制最大高度，约5行文本
    paddingVertical: 0, // 移除垂直内边距，让文本自然居中
    textAlignVertical: 'center', // 保持居中对齐
  },
  inputActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  sendButton: {
    width: 32,
    height: 32,
    borderRadius: 6,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 4,
  },
  stopButtonContainer: {
    width: 32,
    height: 32,
    borderRadius: 6,
  },
  stopButton: {
    width: 32,
    height: 32,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendIconImage: {
    width: 24,
    height: 24,
  },
  userMessageContainer: {
    alignItems: 'flex-end',
    paddingLeft: 48,
    paddingRight: 0, // 移除右边距，让用户消息气泡贴到右边缘
    paddingBottom: 12,
    backgroundColor: 'transparent',
  },
  userMessageBubble: {
    borderRadius: 12,
    borderTopRightRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    maxWidth: '100%', // 确保气泡不超出容器
  },
  userMessageText: {
    fontSize: 14,
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    color: '#FFFFFF',
    lineHeight: 24,
    flexWrap: 'wrap', // 允许文字换行
  },
  botMessageContainer: {
    paddingHorizontal: 0, // 移除左右内边距
    paddingBottom: 8,
    gap: 10,
    maxWidth: '100%', // 确保内容不超出容器
    backgroundColor: 'transparent',
  },
  botMessageBubble: {
    backgroundColor: 'transparent',
    padding: 12,
    gap: 12,
    maxWidth: '100%',
  },
  botMessageText: {
    fontSize: 14,
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    color: '#414352',
    lineHeight: 24,
  },
  messageActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingLeft: 12, // 与气泡框内文字左对齐
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 4,
    borderRadius: 4,
  },
  actionSeparator: {
    width: 1,
    height: 12,
    backgroundColor: '#EBEBF0',
    borderRadius: 4,
  },
  sessionTipContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF5F5',
    borderRadius: 12,
    padding: 16,
    gap: 8,
    borderWidth: 1,
    borderColor: '#FFE0E0',
  },
  sessionTipText: {
    fontSize: 13,
    color: '#D64545',
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    textAlign: 'center',
    flex: 1,
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  thinkingText: {
    fontSize: 14,
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    color: '#9092A3',
    marginLeft: 8,
  },
});

export default AIChatContent;
