import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  ImageBackground,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../styles';
import AISummaryContent from './ai-features/AISummaryContent';
import AIMinutesContent from './ai-features/AIMinutesContent';
import AIChatContent from './ai-features/AIChatContent';
import { LinearGradient } from 'expo-linear-gradient';
import { Button } from '../common';
import { useSwipe } from '../../hooks/useSwipe';
import CustomModal from './CustomModal';
import { useRecordingStore } from '../../stores/recordingStore';
import {
  createSession,
  getDefaultSessionParams,
} from '../../services/api/sessionService';

interface AIFeaturesModalProps {
  visible: boolean;
  onClose: () => void;
  recordingId: string;
  onTimePress?: (time: string) => void;
}

type TabType = 'summary' | 'minutes' | 'chat';

// const { width } = Dimensions.get('window'); // 暂时不使用

const AIFeaturesModal: React.FC<AIFeaturesModalProps> = ({
  visible,
  onClose,
  recordingId,
  onTimePress,
}) => {
  const [activeTab, setActiveTab] = useState<TabType>('summary');
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [isCreatingSession, setIsCreatingSession] = useState(false);

  // 查询录音数据
  const recordings = useRecordingStore((state) => state.recordings);
  const updateRecording = useRecordingStore((state) => state.updateRecording);
  const recording = recordings.find((r) => r.id === recordingId);

  const tabs: { key: TabType; title: string }[] = useMemo(
    () => [
      { key: 'summary', title: 'AI速览' },
      { key: 'minutes', title: 'AI纪要' },
      { key: 'chat', title: 'AI问问' },
    ],
    []
  );

  const switchTab = (index: number) => {
    setActiveTabIndex(index);
    setActiveTab(tabs[index].key);
    // 不再需要 FlatList 滚动，因为我们使用条件渲染
  };

  // 手势处理函数
  const handleSwipeLeft = () => {
    if (activeTabIndex < tabs.length - 1) {
      switchTab(activeTabIndex + 1);
    }
  };

  const handleSwipeRight = () => {
    if (activeTabIndex > 0) {
      switchTab(activeTabIndex - 1);
    }
  };

  // 使用自定义的useSwipe hook
  const { onTouchStart, onTouchEnd } = useSwipe(
    handleSwipeLeft,
    handleSwipeRight,
    4
  );

  // 创建会话的函数
  const handleCreateSession = React.useCallback(async () => {
    if (!recording || recording.sessionId || isCreatingSession) {
      return; // 如果已经有sessionId或正在创建，则跳过
    }

    setIsCreatingSession(true);
    try {
      const params = getDefaultSessionParams(recordingId);
      // console.log('🚀 创建会话，参数:', params);

      const response = await createSession(params);
      // console.log('✅ 会话创建成功:', response);

      if (response.resultObject && response.resultObject.sessionId) {
        // 更新录音记录，关联sessionId
        updateRecording(recordingId, {
          sessionId: response.resultObject.sessionId,
          sessionData: response.resultObject as unknown as Record<
            string,
            unknown
          >,
        });
        // console.log('✅ 录音记录已更新，关联sessionId:', response.resultObject.sessionId);
      }
    } catch (error) {
      console.error('❌ 创建会话失败:', error);
    } finally {
      setIsCreatingSession(false);
    }
  }, [recording, recordingId, isCreatingSession, updateRecording]);

  useEffect(() => {
    if (visible) {
      // 模态框打开时，检查并创建会话
      handleCreateSession();
    }
  }, [visible, recordingId, handleCreateSession]);

  useEffect(() => {
    const index = tabs.findIndex((tab) => tab.key === activeTab);
    setActiveTabIndex(index);
  }, [activeTab, tabs]);

  // Reset to default tab when modal closes
  useEffect(() => {
    if (!visible) {
      // Delay resetting until after the modal close animation completes
      setTimeout(() => {
        setActiveTab('summary');
        setActiveTabIndex(0);
      }, 300);
    }
  }, [visible]);

  const renderTabButton = (tab: (typeof tabs)[0], _index: number) => {
    const isActive = activeTab === tab.key;

    if (isActive) {
      return (
        <TouchableOpacity
          key={tab.key}
          style={[styles.tab]}
          onPress={() => {
            setActiveTab(tab.key);
            // 不再需要 FlatList 滚动，因为我们使用条件渲染
          }}
        >
          <LinearGradient
            colors={[
              '#3053ED', // #3053ED 0%
              'rgba(82,121,255,0.8)', // rgba(11, 16, 31, 0.8) 50%
              '#3EDDB6', // #3EDDB6 100%
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.gradientButton}
          >
            <Text style={[styles.tabText, isActive && styles.activeTabText]}>
              {tab.title}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      );
    } else {
      return (
        <Button
          key={tab.key}
          style={[styles.tab]}
          onPress={() => {
            setActiveTab(tab.key);
            // 不再需要 FlatList 滚动，因为我们使用条件渲染
          }}
        >
          <Text style={[styles.tabText, isActive && styles.activeTabText]}>
            {tab.title}
          </Text>
        </Button>
      );
    }
  };

  return (
    <CustomModal visible={visible} onClose={onClose}>
      <View style={styles.modalWrapper}>
        <View style={styles.modalContainer}>
          {/* 上部分使用切图实现双向渐变 */}
          <ImageBackground
            source={require('../../../assets/images/modal-background.png')}
            style={styles.gradientSection}
            resizeMode="stretch"
            imageStyle={styles.backgroundImageStyle}
          />
          {/* 下部分白色背景区域 */}
          <View style={styles.whiteSection}>
            {/* 关闭按钮 - 独立定位 */}
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={20} color={colors.text.secondary} />
            </TouchableOpacity>

            <SafeAreaView style={styles.header}>
              {/* Tab导航 - 现在可以真正居中 */}
              <View style={styles.tabContainer}>
                {tabs.map((tab, index) => renderTabButton(tab, index))}
              </View>
            </SafeAreaView>

            {/* 内容区域 - 使用条件渲染替代FlatList，避免虚拟化导致的TextInput焦点问题 */}
            <View style={styles.contentContainer}>
              <View
                style={styles.contentWrapper}
                onTouchStart={onTouchStart}
                onTouchEnd={onTouchEnd}
              >
                {/* 根据当前选中的tab条件渲染内容，避免FlatList虚拟化问题 */}
                {activeTab === 'summary' && (
                  <View style={styles.tabContentContainer}>
                    <ScrollView
                      style={styles.contentScrollView}
                      showsVerticalScrollIndicator={false}
                    >
                      <AISummaryContent
                        recording={recording}
                        onTimePress={(time) => {
                          onClose(); // First close the modal
                          // Use setTimeout to ensure the modal is closed before seeking
                          setTimeout(() => {
                            if (onTimePress) {
                              onTimePress(time);
                            }
                          }, 300);
                        }}
                      />
                    </ScrollView>
                  </View>
                )}
                {activeTab === 'minutes' && (
                  <View style={styles.tabContentContainer}>
                    <ScrollView
                      style={styles.contentScrollView}
                      showsVerticalScrollIndicator={false}
                    >
                      <AIMinutesContent recording={recording} />
                    </ScrollView>
                  </View>
                )}
                {activeTab === 'chat' && (
                  <View style={styles.tabContentContainer}>
                    {/* AIChatContent 内部已经有滚动处理，不需要额外的 ScrollView */}
                    <AIChatContent recording={recording} />
                  </View>
                )}
              </View>
            </View>
          </View>
        </View>
      </View>
    </CustomModal>
  );
};

const styles = StyleSheet.create({
  modalWrapper: {
    flex: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background.primary,
    justifyContent: 'center',
    position: 'relative',
  },
  gradientSection: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 260, // 对应设计稿的260px高度
    zIndex: 0,
  },
  whiteSection: {
    flex: 1,
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  backgroundImageStyle: {
    width: '100%',
    height: '100%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center', // 现在只需要居中tab容器
    paddingHorizontal: 16,
    paddingVertical: 12,
    height: 80,
  },
  closeButton: {
    padding: 4,
    position: 'absolute',
    right: 20,
    top: 36, // 调整到与tab容器垂直居中对齐
    zIndex: 10, // 确保在最上层
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  placeholder: {
    width: 32,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFFCE',
    borderRadius: 30,
    borderWidth: 1,
    borderColor: colors.primary,
    paddingHorizontal: 4,
    paddingVertical: 4,
    width: '70%',
    top: 10,
  },
  tab: {
    flex: 1,
    borderRadius: 30,
    paddingHorizontal: 0,
    marginHorizontal: 3,
    backgroundColor: 'transparent',
  },
  activeTab: {
    backgroundColor: colors.primary,
  },
  tabText: {
    fontSize: 15,
    color: colors.text.primary,
    fontWeight: '500',
  },
  activeTabText: {
    color: '#fff',
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
  },
  swipeIndicator: {
    paddingVertical: 8,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  swipeText: {
    fontSize: 12,
    color: colors.text.secondary,
    opacity: 0.6,
  },
  contentWrapper: {
    flex: 1,
  },
  tabContentContainer: {
    flex: 1,
  },
  contentScrollView: {
    flex: 1,
  },
  gradientButton: {
    flex: 1,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
  },
});

export default AIFeaturesModal;
