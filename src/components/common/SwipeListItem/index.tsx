import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Swipeable } from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';
import { colors, typography, spacing, radius } from '../../../styles';
import { SwipeListItemProps, SwipeAction } from './types';

/**
 * 支持左右滑动操作的列表项组件
 * 集成react-native-gesture-handler实现流畅滑动体验
 */
const SwipeListItem: React.FC<SwipeListItemProps> = ({
  children,
  leftActions,
  rightActions,
  onPress,
  style,
  contentStyle,
  disabled = false,
  ...props
}) => {
  // 渲染滑动操作按钮
  const renderAction = (
    action: SwipeAction,
    _isRight: boolean = false
  ): React.ReactElement => (
    <TouchableOpacity
      key={action.id}
      style={[
        styles.actionButton,
        { backgroundColor: action.backgroundColor || colors.gray[400] },
        action.style,
      ]}
      onPress={action.onPress}
      activeOpacity={0.7}
    >
      {action.icon && (
        <Ionicons
          name={action.icon}
          size={action.iconSize || 20}
          color={action.iconColor || colors.white}
        />
      )}
      {action.text && (
        <Text
          style={[
            styles.actionText,
            { color: action.textColor || colors.white },
            action.textStyle,
          ]}
        >
          {action.text}
        </Text>
      )}
    </TouchableOpacity>
  );

  // 渲染右侧操作区域
  const renderRightActions = (
    progress: Animated.AnimatedAddition<number>,
    dragX: Animated.AnimatedAddition<number>
  ): React.ReactElement | null => {
    if (!rightActions || rightActions.length === 0) return null;

    const trans = dragX.interpolate({
      inputRange: [-100, -50, 0],
      outputRange: [0, 50, 100],
      extrapolate: 'clamp',
    });

    return (
      <View style={styles.rightActionsContainer}>
        <Animated.View
          style={[
            styles.actionsRow,
            {
              transform: [{ translateX: trans }],
            },
          ]}
        >
          {rightActions.map((action) => renderAction(action, true))}
        </Animated.View>
      </View>
    );
  };

  // 渲染左侧操作区域
  const renderLeftActions = (
    progress: Animated.AnimatedAddition<number>,
    dragX: Animated.AnimatedAddition<number>
  ): React.ReactElement | null => {
    if (!leftActions || leftActions.length === 0) return null;

    const trans = dragX.interpolate({
      inputRange: [0, 50, 100],
      outputRange: [-100, -50, 0],
      extrapolate: 'clamp',
    });

    return (
      <View style={styles.leftActionsContainer}>
        <Animated.View
          style={[
            styles.actionsRow,
            {
              transform: [{ translateX: trans }],
            },
          ]}
        >
          {leftActions.map((action) => renderAction(action, false))}
        </Animated.View>
      </View>
    );
  };

  // 列表项内容
  const itemContent = (
    <TouchableOpacity
      style={[styles.content, disabled && styles.disabledContent, contentStyle]}
      onPress={onPress}
      disabled={disabled || !onPress}
      activeOpacity={0.7}
    >
      {children}
    </TouchableOpacity>
  );

  // 如果没有滑动操作，直接返回内容
  if (
    (!leftActions || leftActions.length === 0) &&
    (!rightActions || rightActions.length === 0)
  ) {
    return (
      <View style={[styles.container, style]} {...props}>
        {itemContent}
      </View>
    );
  }

  return (
    <View style={[styles.container, style]} {...props}>
      <Swipeable
        renderLeftActions={leftActions ? renderLeftActions : undefined}
        renderRightActions={rightActions ? renderRightActions : undefined}
        rightThreshold={40}
        leftThreshold={40}
      >
        {itemContent}
      </Swipeable>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.background.primary,
  },

  content: {
    backgroundColor: colors.background.primary,
    borderRadius: radius.md,
    marginHorizontal: spacing.md,
    marginVertical: spacing.xs,
    // iOS阴影
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    // Android阴影
    elevation: 1,
  },

  disabledContent: {
    opacity: 0.6,
  },

  leftActionsContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },

  rightActionsContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },

  actionsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  actionButton: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 70,
    height: '100%',
    paddingHorizontal: spacing.xs,
  },

  actionText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    marginTop: spacing.xs / 2,
    textAlign: 'center',
  },
});

export default SwipeListItem;
