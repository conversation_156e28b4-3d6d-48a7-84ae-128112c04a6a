import { ViewProps, ViewStyle, TextStyle } from 'react-native';

/**
 * 滑动操作配置
 */
export interface SwipeAction {
  /** 操作唯一标识 */
  id: string;

  /** 操作文字 */
  text?: string;

  /** 图标名称 */
  icon?: keyof typeof import('@expo/vector-icons').Ionicons.glyphMap;

  /** 图标大小 */
  iconSize?: number;

  /** 图标颜色 */
  iconColor?: string;

  /** 文字颜色 */
  textColor?: string;

  /** 背景颜色 */
  backgroundColor?: string;

  /** 点击事件处理函数 */
  onPress: () => void;

  /** 自定义样式 */
  style?: ViewStyle;

  /** 自定义文字样式 */
  textStyle?: TextStyle;
}

/**
 * 滑动列表项组件属性
 */
export interface SwipeListItemProps extends ViewProps {
  /** 列表项内容 */
  children: React.ReactNode;

  /** 左滑操作配置 */
  leftActions?: SwipeAction[];

  /** 右滑操作配置 */
  rightActions?: SwipeAction[];

  /** 点击事件处理函数 */
  onPress?: () => void;

  /** 自定义样式 */
  style?: ViewStyle;

  /** 内容区域样式 */
  contentStyle?: ViewStyle;

  /** 是否禁用 */
  disabled?: boolean;
}
