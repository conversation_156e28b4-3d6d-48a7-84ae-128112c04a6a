import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, radius } from '../../../styles';
import { GradientCardProps, GradientCardVariant } from './types';

/**
 * 渐变卡片组件
 * 支持多种渐变样式、阴影效果和自定义内容
 */
const GradientCard: React.FC<GradientCardProps> = ({
  children,
  variant = 'none',
  shadow = true,
  style,
  contentStyle,
  header,
  footer,
  borderRadius = radius.card,
  ...props
}) => {
  // 获取渐变颜色配置
  const getGradientColors = (
    variant: GradientCardVariant
  ): [string, string, ...string[]] | null => {
    switch (variant) {
      case 'primary':
        return colors.gradient.primary as [string, string, ...string[]];
      case 'secondary':
        return colors.gradient.secondary as [string, string, ...string[]];
      case 'success':
        return colors.gradient.success as [string, string, ...string[]];
      case 'accent':
        return colors.gradient.accent as [string, string, ...string[]];
      case 'warm':
        return colors.gradient.warm as [string, string, ...string[]];
      case 'cool':
        return colors.gradient.cool as [string, string, ...string[]];
      case 'none':
      default:
        return null;
    }
  };

  const gradientColors = getGradientColors(variant);

  // 卡片基础样式
  const cardStyle: ViewStyle = {
    borderRadius,
    backgroundColor: gradientColors ? 'transparent' : colors.surface,
    overflow: 'hidden',
    ...(shadow && styles.shadow),
  };

  // 内容区域样式
  const innerContentStyle: ViewStyle = {
    padding: spacing.lg,
    ...contentStyle,
  };

  const renderContent = () => (
    <View style={styles.container}>
      {header && <View style={styles.header}>{header}</View>}
      <View style={[innerContentStyle]}>{children}</View>
      {footer && <View style={styles.footer}>{footer}</View>}
    </View>
  );

  if (gradientColors) {
    // 使用渐变背景
    return (
      <View style={[cardStyle, style]} {...props}>
        <LinearGradient
          colors={gradientColors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradientContainer}
        >
          {renderContent()}
        </LinearGradient>
      </View>
    );
  }

  // 使用纯色背景
  return (
    <View style={[cardStyle, style]} {...props}>
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  gradientContainer: {
    flex: 1,
  },

  header: {
    borderBottomWidth: 1,
    borderBottomColor: colors.separator,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
  },

  footer: {
    borderTopWidth: 1,
    borderTopColor: colors.separator,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
    paddingBottom: spacing.lg,
  },

  shadow: {
    // iOS阴影
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    // Android阴影
    elevation: 3,
  },
});

export default GradientCard;
