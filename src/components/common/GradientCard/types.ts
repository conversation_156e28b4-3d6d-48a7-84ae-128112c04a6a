import { ViewProps, ViewStyle } from 'react-native';

/**
 * 渐变卡片变体类型
 */
export type GradientCardVariant =
  | 'primary'
  | 'secondary'
  | 'success'
  | 'accent'
  | 'warm'
  | 'cool'
  | 'none'; // 无渐变，使用纯色背景

/**
 * 渐变卡片组件属性
 */
export interface GradientCardProps extends ViewProps {
  /** 卡片内容 */
  children: React.ReactNode;

  /** 卡片变体 */
  variant?: GradientCardVariant;

  /** 是否显示阴影 */
  shadow?: boolean;

  /** 自定义样式 */
  style?: ViewStyle;

  /** 内容区域样式 */
  contentStyle?: ViewStyle;

  /** 头部内容 */
  header?: React.ReactNode;

  /** 底部内容 */
  footer?: React.ReactNode;

  /** 自定义圆角大小 */
  borderRadius?: number;
}
