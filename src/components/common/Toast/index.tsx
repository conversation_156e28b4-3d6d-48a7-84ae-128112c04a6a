import React from 'react';
import Toast, {
  ToastConfig,
  ToastConfigParams,
} from 'react-native-toast-message';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { spacing } from '../../../styles';

// Toast配置选项
interface ToastOptions {
  showMessage?: boolean; // 是否显示text2（副标题），默认false
}

// 全局Toast配置
let toastGlobalOptions: ToastOptions = {
  showMessage: false, // 默认不显示text2
};

// Toast configuration
export const toastConfig: ToastConfig = {
  success: ({ text1, text2 }: ToastConfigParams<unknown>) => (
    <View style={styles.container}>
      <View style={[styles.iconContainer, styles.successIcon]}>
        <Ionicons name="checkmark" size={12} color="#FFFFFF" />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.title}>{text1}</Text>
        {text2 && toastGlobalOptions.showMessage && (
          <Text style={styles.message}>{text2}</Text>
        )}
      </View>
    </View>
  ),

  error: ({ text1, text2 }: ToastConfigParams<unknown>) => (
    <View style={styles.container}>
      <View style={[styles.iconContainer, styles.errorIcon]}>
        <Ionicons name="close" size={12} color="#FFFFFF" />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.title}>{text1}</Text>
        {text2 && toastGlobalOptions.showMessage && (
          <Text style={styles.message}>{text2}</Text>
        )}
      </View>
    </View>
  ),

  info: ({ text1, text2 }: ToastConfigParams<unknown>) => (
    <View style={styles.container}>
      <View style={[styles.iconContainer, styles.infoIcon]}>
        <Ionicons name="information" size={12} color="#FFFFFF" />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.title}>{text1}</Text>
        {text2 && toastGlobalOptions.showMessage && (
          <Text style={styles.message}>{text2}</Text>
        )}
      </View>
    </View>
  ),

  warning: ({ text1, text2 }: ToastConfigParams<unknown>) => (
    <View style={styles.container}>
      <View style={[styles.iconContainer, styles.warningIcon]}>
        <Ionicons name="warning" size={12} color="#FFFFFF" />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.title}>{text1}</Text>
        {text2 && toastGlobalOptions.showMessage && (
          <Text style={styles.message}>{text2}</Text>
        )}
      </View>
    </View>
  ),
};

// Toast service for easy usage
export class ToastService {
  /**
   * 配置Toast全局选项
   * @param options Toast配置选项
   */
  static configure(options: Partial<ToastOptions>) {
    toastGlobalOptions = { ...toastGlobalOptions, ...options };
  }

  /**
   * 获取当前Toast配置
   */
  static getConfig(): ToastOptions {
    return { ...toastGlobalOptions };
  }

  /**
   * 设置是否显示详细信息（text2）
   * @param show 是否显示详细信息
   */
  static setShowMessage(show: boolean) {
    toastGlobalOptions.showMessage = show;
  }

  static show(config: {
    type: 'success' | 'error' | 'info' | 'warning';
    title: string;
    message?: string;
    duration?: number;
  }) {
    Toast.show({
      type: config.type,
      text1: config.title,
      text2: config.message,
      visibilityTime: config.duration || 3000,
      autoHide: true,
      topOffset: 50,
      bottomOffset: 40,
    });
  }

  static success(title: string, message?: string) {
    this.show({ type: 'success', title, message });
  }

  static error(title: string, message?: string) {
    this.show({ type: 'error', title, message });
  }

  static info(title: string, message?: string) {
    this.show({ type: 'info', title, message });
  }

  static warning(title: string, message?: string) {
    this.show({ type: 'warning', title, message });
  }

  static hide() {
    Toast.hide();
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingVertical: 12, // 增加垂直padding
    paddingHorizontal: 20, // 增加水平padding
    borderRadius: 999,
    marginHorizontal: spacing.md,
    // 根据设计稿的阴影值调整: box-shadow: 0px 8px 24px 0px rgba(100,103,122,0.24)
    shadowColor: '#64677A', // rgba(100,103,122) 转换为hex
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.24,
    shadowRadius: 24,
    elevation: 12, // Android阴影，稍微增加
    minHeight: 40, // 增加最小高度
    alignSelf: 'center', // 让容器自适应内容宽度
  },

  iconContainer: {
    width: 20, // 增加图标容器大小
    height: 20,
    borderRadius: 10, // 圆形背景
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10, // 增加右边距
  },

  // 不同类型的图标背景色
  successIcon: {
    backgroundColor: '#21BF55',
  },

  errorIcon: {
    backgroundColor: '#FF3B30',
  },

  infoIcon: {
    backgroundColor: '#007AFF',
  },

  warningIcon: {
    backgroundColor: '#FF9500',
  },

  textContainer: {
    justifyContent: 'center',
  },

  title: {
    fontSize: 16, // 增加主标题字体大小
    fontWeight: '400',
    color: '#414352',
    fontFamily: 'PingFang SC',
    lineHeight: 24, // 相应调整行高
  },

  message: {
    fontSize: 14, // 增加副标题字体大小
    color: '#414352',
    opacity: 0.8,
    fontFamily: 'PingFang SC',
    lineHeight: 20, // 相应调整行高
    marginTop: 2,
  },
});

export default Toast;
