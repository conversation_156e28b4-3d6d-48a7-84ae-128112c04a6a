import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, typography, spacing, radius } from '../../../styles';
import { GradientButtonProps, GradientButtonVariant } from './types';

/**
 * 渐变按钮组件
 * 支持多种渐变样式、尺寸和状态
 */
const GradientButton: React.FC<GradientButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  textStyle,
  children,
  ...props
}) => {
  // 获取渐变颜色配置
  const getGradientColors = (
    variant: GradientButtonVariant
  ): [string, string, ...string[]] => {
    switch (variant) {
      case 'primary':
        return colors.gradient.primary as [string, string, ...string[]];
      case 'secondary':
        return colors.gradient.secondary as [string, string, ...string[]];
      case 'success':
        return colors.gradient.success as [string, string, ...string[]];
      case 'accent':
        return colors.gradient.accent as [string, string, ...string[]];
      case 'warm':
        return colors.gradient.warm as [string, string, ...string[]];
      case 'cool':
        return colors.gradient.cool as [string, string, ...string[]];
      default:
        return colors.gradient.primary as [string, string, ...string[]];
    }
  };

  // 获取按钮尺寸样式
  const getSizeStyle = (): ViewStyle => {
    switch (size) {
      case 'small':
        return {
          paddingHorizontal: spacing.md,
          paddingVertical: spacing.sm,
          minHeight: 36,
        };
      case 'medium':
        return {
          paddingHorizontal: spacing.lg,
          paddingVertical: spacing.md,
          minHeight: 44,
        };
      case 'large':
        return {
          paddingHorizontal: spacing.xl,
          paddingVertical: spacing.lg,
          minHeight: 52,
        };
      default:
        return {
          paddingHorizontal: spacing.lg,
          paddingVertical: spacing.md,
          minHeight: 44,
        };
    }
  };

  // 获取文字尺寸样式
  const getTextSizeStyle = (): TextStyle => {
    switch (size) {
      case 'small':
        return {
          fontSize: typography.fontSize.sm,
          fontWeight: typography.fontWeight.medium,
        };
      case 'medium':
        return {
          fontSize: typography.fontSize.base,
          fontWeight: typography.fontWeight.semibold,
        };
      case 'large':
        return {
          fontSize: typography.fontSize.lg,
          fontWeight: typography.fontWeight.semibold,
        };
      default:
        return {
          fontSize: typography.fontSize.base,
          fontWeight: typography.fontWeight.semibold,
        };
    }
  };

  const gradientColors = getGradientColors(variant);
  const sizeStyle = getSizeStyle();
  const textSizeStyle = getTextSizeStyle();

  const buttonContent = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size="small"
          color={colors.white}
          style={styles.loadingIndicator}
        />
      );
    }

    if (children) {
      return children;
    }

    return (
      <Text style={[styles.buttonText, textSizeStyle, textStyle]}>{title}</Text>
    );
  };

  if (disabled) {
    // 禁用状态使用灰色背景
    return (
      <TouchableOpacity
        style={[styles.button, sizeStyle, styles.disabledButton, style]}
        disabled={true}
        {...props}
      >
        <Text
          style={[
            styles.buttonText,
            textSizeStyle,
            styles.disabledText,
            textStyle,
          ]}
        >
          {title}
        </Text>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[styles.button, sizeStyle, style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      {...props}
    >
      <LinearGradient
        colors={gradientColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradient}
      >
        {buttonContent()}
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: radius.button,
    overflow: 'hidden',
    // iOS阴影
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    // Android阴影
    elevation: 2,
  },

  gradient: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },

  buttonText: {
    color: colors.white,
    textAlign: 'center',
    includeFontPadding: false,
  },

  disabledButton: {
    backgroundColor: colors.gray[300],
  },

  disabledText: {
    color: colors.gray[500],
  },

  loadingIndicator: {
    marginHorizontal: spacing.xs,
  },
});

export default GradientButton;
