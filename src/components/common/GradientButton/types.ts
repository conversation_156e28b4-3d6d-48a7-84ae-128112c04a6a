import { TouchableOpacityProps, ViewStyle, TextStyle } from 'react-native';

/**
 * 渐变按钮变体类型
 */
export type GradientButtonVariant =
  | 'primary'
  | 'secondary'
  | 'success'
  | 'accent'
  | 'warm'
  | 'cool';

/**
 * 按钮尺寸类型
 */
export type GradientButtonSize = 'small' | 'medium' | 'large';

/**
 * 渐变按钮组件属性
 */
export interface GradientButtonProps
  extends Omit<TouchableOpacityProps, 'children'> {
  /** 按钮文字 */
  title?: string;

  /** 点击事件处理函数 */
  onPress?: () => void;

  /** 按钮变体 */
  variant?: GradientButtonVariant;

  /** 按钮尺寸 */
  size?: GradientButtonSize;

  /** 是否禁用 */
  disabled?: boolean;

  /** 是否显示加载状态 */
  loading?: boolean;

  /** 自定义样式 */
  style?: ViewStyle;

  /** 自定义文字样式 */
  textStyle?: TextStyle;

  /** 自定义子组件 */
  children?: React.ReactNode;
}
