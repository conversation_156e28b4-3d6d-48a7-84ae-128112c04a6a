import React from 'react';
import { Text, StyleSheet, TextInput } from 'react-native';

/**
 * 简单消息内容组件
 */
export const MessageContent: React.FC<{ message: string }> = ({ message }) => {
  return <Text style={styles.message}>{message}</Text>;
};

/**
 * 错误消息内容组件
 */
export const ErrorContent: React.FC<{ message: string }> = ({ message }) => {
  return <Text style={[styles.message, styles.errorText]}>{message}</Text>;
};

/**
 * 警告消息内容组件
 */
export const WarningContent: React.FC<{ message: string }> = ({ message }) => {
  return <Text style={[styles.message, styles.warningText]}>{message}</Text>;
};

/**
 * 输入框内容组件
 */
export interface InputContentProps {
  placeholder: string;
  defaultValue: string;
  onChangeText: (text: string) => void;
}

export const InputContent: React.FC<InputContentProps> = ({
  placeholder,
  defaultValue,
  onChangeText,
}) => {
  return (
    <TextInput
      style={styles.input}
      placeholder={placeholder}
      defaultValue={defaultValue}
      onChangeText={onChangeText}
      autoFocus
    />
  );
};

const styles = StyleSheet.create({
  message: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    color: '#333',
  },
  errorText: {
    color: '#e53935',
  },
  warningText: {
    color: '#f57c00',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 4,
    fontSize: 16,
  },
});
