import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { colors } from '../../../styles';

export interface DialogProps {
  /**
   * 控制对话框是否可见
   */
  visible: boolean;
  /**
   * 对话框标题
   */
  title: string;
  /**
   * 关闭对话框的回调函数
   */
  onClose: () => void;
  /**
   * 点击确认按钮的回调函数
   */
  onConfirm: () => void;
  /**
   * 确认按钮的文本，默认为"确定"
   */
  confirmText?: string;
  /**
   * 取消按钮的文本，默认为"取消"
   */
  cancelText?: string;
  /**
   * 确认按钮是否禁用
   */
  confirmDisabled?: boolean;
  /**
   * 确认按钮的类型，影响按钮颜色
   */
  confirmButtonType?: 'primary' | 'warning' | 'danger';
  /**
   * 对话框内容
   */
  children?: React.ReactNode;
}

/**
 * 全局对话框上下文
 */
interface DialogContextType {
  showDialog: (options: Omit<DialogProps, 'visible' | 'onClose'>) => void;
  hideDialog: () => void;
}

const DialogContext = createContext<DialogContextType | null>(null);

/**
 * 对话框提供者组件
 * 将此组件放在应用根组件中，使全局对话框可用
 */
export const DialogProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [dialogProps, setDialogProps] = useState<DialogProps | null>(null);

  const showDialog = useCallback(
    (options: Omit<DialogProps, 'visible' | 'onClose'>) => {
      setDialogProps({
        ...options,
        visible: true,
        onClose: () => {
          setDialogProps(null);
        },
      });
    },
    []
  );

  const hideDialog = useCallback(() => {
    setDialogProps(null);
  }, []);

  return (
    <DialogContext.Provider value={{ showDialog, hideDialog }}>
      {children}
      {dialogProps && <DialogComponent {...dialogProps} />}
    </DialogContext.Provider>
  );
};

/**
 * 使用对话框的钩子
 */
export const useDialog = () => {
  const context = useContext(DialogContext);
  if (!context) {
    throw new Error('useDialog must be used within a DialogProvider');
  }
  return context;
};

// 单例模式 - 用于全局调用
let globalDialogRef: DialogContextType | null = null;

/**
 * 通用对话框组件类型定义，包含静态方法
 */
interface DialogType {
  (props: DialogProps): JSX.Element;
  setGlobalDialogRef: (ref: DialogContextType) => void;
  show: (
    options: Omit<DialogProps, 'visible' | 'onClose' | 'onConfirm'> & {
      onConfirm?: () => void;
    }
  ) => void;
  hide: () => void;
}

/**
 * 通用对话框组件
 *
 * 提供标准的对话框布局，包括标题、内容区域、确认和取消按钮
 */
const DialogComponent: React.FC<DialogProps> = ({
  visible,
  title,
  onClose,
  onConfirm,
  confirmText = '确定',
  cancelText = '取消',
  confirmDisabled = false,
  confirmButtonType = 'primary',
  children,
}) => {
  const handleConfirm = async () => {
    await onConfirm();
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  // 根据按钮类型获取样式
  const getConfirmButtonStyle = () => {
    if (confirmDisabled) return styles.confirmButtonDisabled;

    switch (confirmButtonType) {
      case 'warning':
        return styles.confirmButtonWarning;
      case 'danger':
        return styles.confirmButtonDanger;
      default:
        return styles.confirmButton;
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.overlay}>
          <View style={styles.modalContainer}>
            <View style={styles.header}>
              <Text style={styles.title}>{title}</Text>
            </View>

            <View style={styles.content}>{children}</View>

            <View style={styles.footer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={handleCancel}
              >
                <Text style={[styles.buttonText, styles.cancelButtonText]}>
                  {cancelText}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, getConfirmButtonStyle()]}
                onPress={handleConfirm}
                disabled={confirmDisabled}
              >
                <Text style={[styles.buttonText, styles.confirmButtonText]}>
                  {confirmText}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

/**
 * 通用对话框带静态方法
 */
const Dialog: DialogType = DialogComponent as DialogType;

/**
 * 设置全局对话框引用
 */
Dialog.setGlobalDialogRef = (ref: DialogContextType) => {
  globalDialogRef = ref;
};

/**
 * 显示全局对话框
 * @param options 对话框配置
 */
Dialog.show = (
  options: Omit<DialogProps, 'visible' | 'onClose' | 'onConfirm'> & {
    onConfirm?: () => void;
  }
) => {
  if (!globalDialogRef) {
    console.warn(
      '请确保在应用入口处添加了 DialogProvider，并已初始化全局对话框引用'
    );
    return;
  }

  globalDialogRef.showDialog({
    ...options,
    onConfirm: options.onConfirm || (() => {}),
  });
};

/**
 * 隐藏全局对话框
 */
Dialog.hide = () => {
  if (!globalDialogRef) {
    console.warn('全局对话框引用未初始化');
    return;
  }

  globalDialogRef.hideDialog();
};

/**
 * 初始化全局对话框的组件
 * 将此组件放在应用根组件中
 */
export const GlobalDialogInitializer: React.FC = () => {
  const dialog = useDialog();

  // 在组件挂载时设置全局对话框引用
  useEffect(() => {
    Dialog.setGlobalDialogRef(dialog);
    return () => {
      Dialog.setGlobalDialogRef(null as unknown as DialogContextType);
    };
  }, [dialog]);

  return null;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: colors.background.primary,
    borderRadius: 16,
    width: '100%',
    maxWidth: 320,
    padding: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  content: {
    marginBottom: 20,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 30,
    alignItems: 'center',
  },
  cancelButton: {
    // backgroundColor: colors.background.secondary,
    borderWidth: 1,
    borderColor: colors.border,
  },
  confirmButton: {
    backgroundColor: colors.primary,
  },
  confirmButtonWarning: {
    backgroundColor: colors.warning,
  },
  confirmButtonDanger: {
    backgroundColor: colors.error,
  },
  confirmButtonDisabled: {
    backgroundColor: colors.gray[300],
    opacity: 0.7,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButtonText: {
    color: colors.text.primary,
  },
  confirmButtonText: {
    color: colors.background.primary,
  },
});

export default Dialog;
