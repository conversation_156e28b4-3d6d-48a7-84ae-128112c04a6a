# 全局对话框（Dialog）使用指南

这个模块提供了一个全局通用的对话框系统，允许在应用的任何地方通过简单的函数调用来显示对话框，而无需在每个组件中都定义对话框组件。

## 特性

- 在任何组件或函数中通过简单的静态方法调用显示对话框
- 支持多种类型的对话框：提示、确认、输入、成功、错误、警告等
- 可自定义对话框的内容、按钮文本、回调函数等
- 统一的视觉风格和交互行为

## 设置

1. 确保在应用的根组件中添加了 `DialogProvider` 和 `GlobalDialogInitializer`：

```tsx
// App.tsx
import {
  DialogProvider,
  GlobalDialogInitializer,
} from './src/components/common/Dialog';

export default function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <DialogProvider>
          {/* 其他 Provider */}
          <YourApp />
          <GlobalDialogInitializer />
        </DialogProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
```

## 基础用法

在任何组件或函数中，只需要导入 `Dialog` 并调用其静态方法即可显示对话框：

```tsx
import Dialog from '../components/common/Dialog';

// 显示一个简单的提示弹窗
Dialog.show({
  title: '提示',
  children: <Text>这是一个提示信息</Text>,
  onConfirm: () => {
    console.log('用户点击了确认按钮');
  },
});
```

## 使用 DialogUtil 工具函数

为了更方便的使用，我们提供了 `DialogUtil` 工具函数，包含了各种常用的对话框类型：

```tsx
import { DialogUtil } from '../utils/dialogUtil';

// 显示提示弹窗
DialogUtil.alert('提示', '这是一个提示信息', () => {
  console.log('用户关闭了提示');
});

// 显示确认弹窗
DialogUtil.confirm('确认', '确定要执行此操作吗？', () => {
  console.log('用户确认了操作');
});

// 显示输入弹窗
DialogUtil.prompt('输入', '请输入内容', '', (text) => {
  console.log('用户输入了:', text);
});

// 显示成功提示
DialogUtil.success('操作成功完成！');

// 显示错误提示
DialogUtil.error('发生错误，请重试');

// 显示警告提示
DialogUtil.warning('请注意，这是一个警告');

// 显示删除确认
DialogUtil.confirmDelete('确认删除', '此操作无法撤销，确定要删除吗？', () => {
  console.log('用户确认了删除操作');
});

// 显示自定义内容
DialogUtil.custom(
  '自定义内容',
  <YourCustomComponent />,
  () => {
    console.log('用户确认了操作');
  },
  {
    confirmText: '自定义确认按钮',
    cancelText: '自定义取消按钮',
  }
);
```

## API 参考

### Dialog.show()

显示一个对话框。

```tsx
Dialog.show({
  title: string;               // 对话框标题
  children: React.ReactNode;   // 对话框内容
  onConfirm: () => void;       // 确认按钮回调函数
  confirmText?: string;        // 确认按钮文本，默认为"确定"
  cancelText?: string;         // 取消按钮文本，默认为"取消"
  confirmDisabled?: boolean;   // 确认按钮是否禁用
});
```

### Dialog.hide()

隐藏当前显示的对话框。

```tsx
Dialog.hide();
```

### DialogUtil 工具函数

DialogUtil 提供了以下便捷方法：

- `alert(title: string, message: string, onConfirm?: () => void)`
- `confirm(title: string, message: string, onConfirm: () => void, confirmText?: string)`
- `prompt(title: string, placeholder?: string, defaultValue?: string, onConfirm: (text: string) => void)`
- `success(message: string, onConfirm?: () => void)`
- `error(message: string, onConfirm?: () => void)`
- `warning(message: string, onConfirm?: () => void)`
- `confirmDelete(title: string, message: string, onConfirm: () => void)`
- `custom(title: string, content: React.ReactNode, onConfirm?: () => void, options?: { confirmText?: string, cancelText?: string, confirmDisabled?: boolean })`
- `hide()`

## 最佳实践

1. **在合适的地方使用**：建议在用户操作需要确认、提示或输入信息的场景使用对话框。
2. **避免嵌套**：避免在一个对话框的回调中立即打开另一个对话框，除非确实需要这样的用户流程。
3. **提供清晰的反馈**：在对话框中给用户提供清晰、简洁的信息，帮助用户理解当前情况和可能的操作。
4. **使用有意义的按钮文本**：不要总是使用"确定"和"取消"，而应该使用能够描述操作的文本，如"删除"、"保存"等。

## 示例

详细的使用示例可以参考 `usageDemo.tsx` 文件。
