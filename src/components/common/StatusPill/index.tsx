import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { colors, typography, spacing, radius } from '../../../styles';
import { StatusPillProps, StatusPillVariant } from './types';

/**
 * 状态胶囊组件
 * 支持多种状态类型和进度显示
 */
const StatusPill: React.FC<StatusPillProps> = ({
  text,
  variant = 'info',
  progress,
  size = 'medium',
  style,
  textStyle,
  ...props
}) => {
  // 获取变体颜色配置
  const getVariantColors = (variant: StatusPillVariant) => {
    switch (variant) {
      case 'success':
        return {
          backgroundColor: colors.success + '15',
          borderColor: colors.success,
          textColor: colors.success,
        };
      case 'warning':
        return {
          backgroundColor: colors.warning + '15',
          borderColor: colors.warning,
          textColor: colors.warning,
        };
      case 'error':
        return {
          backgroundColor: colors.error + '15',
          borderColor: colors.error,
          textColor: colors.error,
        };
      case 'info':
        return {
          backgroundColor: colors.info + '15',
          borderColor: colors.info,
          textColor: colors.info,
        };
      case 'neutral':
      default:
        return {
          backgroundColor: colors.gray[100],
          borderColor: colors.gray[300],
          textColor: colors.text.secondary,
        };
    }
  };

  // 获取尺寸样式
  const getSizeStyle = (): ViewStyle => {
    switch (size) {
      case 'small':
        return {
          paddingHorizontal: spacing.sm,
          paddingVertical: spacing.xs / 2,
          minHeight: 20,
        };
      case 'medium':
        return {
          paddingHorizontal: spacing.md,
          paddingVertical: spacing.xs,
          minHeight: 24,
        };
      case 'large':
        return {
          paddingHorizontal: spacing.lg,
          paddingVertical: spacing.sm,
          minHeight: 32,
        };
      default:
        return {
          paddingHorizontal: spacing.md,
          paddingVertical: spacing.xs,
          minHeight: 24,
        };
    }
  };

  // 获取文字尺寸样式
  const getTextSize = () => {
    switch (size) {
      case 'small':
        return typography.fontSize.xs;
      case 'medium':
        return typography.fontSize.sm;
      case 'large':
        return typography.fontSize.base;
      default:
        return typography.fontSize.sm;
    }
  };

  const variantColors = getVariantColors(variant);
  const sizeStyle = getSizeStyle();
  const fontSize = getTextSize();

  const pillStyle: ViewStyle = {
    ...sizeStyle,
    backgroundColor: variantColors.backgroundColor,
    borderWidth: 1,
    borderColor: variantColors.borderColor,
    borderRadius: radius.statusPill,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-start',
  };

  const renderProgress = () => {
    if (typeof progress !== 'number') return null;

    return (
      <View style={styles.progressContainer}>
        <View
          style={[
            styles.progressBar,
            { backgroundColor: variantColors.borderColor },
          ]}
        />
        <View
          style={[
            styles.progressFill,
            {
              width: `${Math.min(Math.max(progress, 0), 100)}%`,
              backgroundColor: variantColors.textColor,
            },
          ]}
        />
        <Text
          style={[
            styles.progressText,
            {
              fontSize,
              color: variantColors.textColor,
              fontWeight: typography.fontWeight.medium,
            },
            textStyle,
          ]}
        >
          {Math.round(progress)}%
        </Text>
      </View>
    );
  };

  return (
    <View style={[pillStyle, style]} {...props}>
      {typeof progress === 'number' ? (
        renderProgress()
      ) : (
        <Text
          style={[
            styles.text,
            {
              fontSize,
              color: variantColors.textColor,
              fontWeight: typography.fontWeight.medium,
            },
            textStyle,
          ]}
        >
          {text}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  text: {
    includeFontPadding: false,
    textAlign: 'center',
  },

  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    minWidth: 60,
  },

  progressBar: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 2,
    opacity: 0.2,
    borderRadius: 1,
  },

  progressFill: {
    position: 'absolute',
    left: 0,
    height: 2,
    borderRadius: 1,
  },

  progressText: {
    marginLeft: spacing.xs,
    includeFontPadding: false,
  },
});

export default StatusPill;
