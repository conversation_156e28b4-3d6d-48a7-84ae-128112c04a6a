import { ViewProps, ViewStyle, TextStyle } from 'react-native';

/**
 * 状态胶囊变体类型
 */
export type StatusPillVariant =
  | 'success'
  | 'warning'
  | 'error'
  | 'info'
  | 'neutral';

/**
 * 胶囊尺寸类型
 */
export type StatusPillSize = 'small' | 'medium' | 'large';

/**
 * 状态胶囊组件属性
 */
export interface StatusPillProps extends ViewProps {
  /** 显示文字 */
  text?: string;

  /** 状态变体 */
  variant?: StatusPillVariant;

  /** 进度值（0-100），设置后将显示进度模式 */
  progress?: number;

  /** 胶囊尺寸 */
  size?: StatusPillSize;

  /** 自定义样式 */
  style?: ViewStyle;

  /** 自定义文字样式 */
  textStyle?: TextStyle;
}
