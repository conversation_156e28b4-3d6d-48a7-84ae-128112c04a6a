import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  createSession,
  getDefaultSessionParams,
} from '@/services/api/sessionService';
import {
  sendStreamingMessageWithSession,
  SSECallbacks,
} from '@/services/api/chatService';

// 🎯 全局调试计数器和重入保护
let sendMessageCallCount = 0;
let sseConnectionCount = 0;
let isExecutingSendMessage = false;
let activeSSEConnection: EventSource | null = null;

// 会话接口定义
export interface KnowledgeAISession {
  id: string;
  title: string;
  sessionData: any; // 后端返回的会话数据
  knowledgeId?: string; // 关联的知识库ID
  messages: KnowledgeAIMessage[];
  createdAt: Date;
  updatedAt: Date;
}

// 消息接口定义
export interface KnowledgeAIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  status?: 'sending' | 'sent' | 'error';
}

// Store状态接口
export interface KnowledgeAIChatState {
  // 会话管理
  sessions: KnowledgeAISession[];
  currentSession: KnowledgeAISession | null;

  // UI状态
  isLoading: boolean;
  error: string | null;
  isCreatingSession: boolean;
  isStreaming: boolean; // 是否正在流式输出
  currentEventSource: EventSource | null; // 当前SSE连接

  // Actions
  createSession: (title?: string, knowledgeId?: string) => Promise<void>;
  setCurrentSession: (session: KnowledgeAISession | null) => void;
  deleteSession: (sessionId: string) => void;
  updateSessionTitle: (sessionId: string, title: string) => void;

  addMessage: (
    sessionId: string,
    message: Omit<KnowledgeAIMessage, 'id' | 'timestamp'>,
    customId?: string
  ) => void;
  sendMessage: (content: string, knowledgeId?: string) => Promise<void>;
  stopStreaming: () => void; // 停止流式输出

  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useKnowledgeAIChatStore = create<KnowledgeAIChatState>()(
  persist(
    (set, get) => ({
      // 初始状态
      sessions: [],
      currentSession: null,
      isLoading: false,
      error: null,
      isCreatingSession: false,
      isStreaming: false,
      currentEventSource: null,

      // 创建新会话
      createSession: async (title, knowledgeId) => {
        const { sessions } = get();
        set({ isCreatingSession: true, error: null });

        try {
          // 调用后端创建会话
          const params = getDefaultSessionParams('knowledge-space');
          const sessionResponse = await createSession(params);

          if (
            sessionResponse.resultCode === '0' &&
            sessionResponse.resultObject
          ) {
            // 创建本地会话对象
            const newSession: KnowledgeAISession = {
              id: Date.now().toString(),
              title: title || `对话 ${new Date().toLocaleDateString()}`,
              sessionData: sessionResponse.resultObject,
              knowledgeId,
              messages: [],
              createdAt: new Date(),
              updatedAt: new Date(),
            };

            set({
              sessions: [newSession, ...sessions],
              currentSession: newSession,
              isCreatingSession: false,
            });

            console.log('✅ 会话创建成功:', newSession.id);
          } else {
            throw new Error(sessionResponse.resultMsg || '创建会话失败');
          }
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : '创建会话失败';
          set({
            error: errorMessage,
            isCreatingSession: false,
          });
          console.error('❌ 创建会话失败:', error);
        }
      },

      // 设置当前会话
      setCurrentSession: (session) => {
        set({ currentSession: session });
      },

      // 删除会话
      deleteSession: (sessionId) => {
        const { sessions, currentSession } = get();
        const updatedSessions = sessions.filter((s) => s.id !== sessionId);

        set({
          sessions: updatedSessions,
          currentSession:
            currentSession?.id === sessionId ? null : currentSession,
        });
      },

      // 更新会话标题
      updateSessionTitle: (sessionId, title) => {
        const { sessions } = get();
        const updatedSessions = sessions.map((session) =>
          session.id === sessionId
            ? { ...session, title, updatedAt: new Date() }
            : session
        );

        set({ sessions: updatedSessions });

        // 如果是当前会话，也要更新
        const { currentSession } = get();
        if (currentSession?.id === sessionId) {
          set({
            currentSession: {
              ...currentSession,
              title,
              updatedAt: new Date(),
            },
          });
        }
      },

      // 添加消息
      addMessage: (sessionId, messageData, customId?) => {
        const message: KnowledgeAIMessage = {
          ...messageData,
          id: customId || Date.now().toString(),
          timestamp: new Date(),
        };

        const { sessions } = get();
        const updatedSessions = sessions.map((session) => {
          if (session.id === sessionId) {
            return {
              ...session,
              messages: [...session.messages, message],
              updatedAt: new Date(),
            };
          }
          return session;
        });

        set({ sessions: updatedSessions });

        // 更新当前会话
        const { currentSession } = get();
        if (currentSession?.id === sessionId) {
          set({
            currentSession: {
              ...currentSession,
              messages: [...currentSession.messages, message],
              updatedAt: new Date(),
            },
          });
        }
      },

      // 发送消息 - 极简版本
      sendMessage: async (content, knowledgeId) => {
        sendMessageCallCount++;
        console.log(`🚨 sendMessage 被调用 [第${sendMessageCallCount}次]`);

        // 🎯 严格重入保护
        if (isExecutingSendMessage) {
          console.log('🛑 检测到重入调用，强制忽略');
          return;
        }

        isExecutingSendMessage = true;
        console.log('🔒 设置执行标记，防止重入');

        try {
          const {
            currentSession,
            addMessage,
            isStreaming,
            currentEventSource,
          } = get();

          // 🎯 强制单一性检查 - 多重保护
          if (isStreaming || currentEventSource || activeSSEConnection) {
            console.log('⚠️ 检测到活跃连接，强制忽略请求');
            isExecutingSendMessage = false;
            return;
          }

          if (!currentSession?.sessionData || !content.trim()) {
            console.log('❌ 会话或内容无效');
            isExecutingSendMessage = false;
            return;
          }
          // 🎯 立即设置状态，防止重复调用
          set({ isLoading: true, isStreaming: true, error: null });

          // 添加用户消息
          addMessage(currentSession.id, {
            role: 'user',
            content,
            status: 'sent',
          });

          // 创建AI回复消息
          const botMessageId = Date.now().toString() + '_bot';
          let botMessageContent = '';

          addMessage(
            currentSession.id,
            {
              role: 'assistant',
              content: '',
              status: 'sending',
            },
            botMessageId
          );

          // 🎯 极简SSE回调
          const sseCallbacks: SSECallbacks = {
            onopen: () => {
              sseConnectionCount++;
              console.log(`🔗 SSE已建立 [第${sseConnectionCount}次连接]`);
            },
            onmessage: (event) => {
              console.log('📨 收到SSE消息:', event);

              // 🎯 极简文本处理
              if (event.type === 'text' && event.data) {
                let text = event.data.toString();
                if (text.startsWith('"') && text.endsWith('"'))
                  text = text.slice(1, -1);

                // 🔧 修复换行符显示问题：将转义的换行符转换为真正的换行符
                text = text.replace(/\\n/g, '\n');

                console.log('🔧 接收文本片段:', JSON.stringify(text));
                botMessageContent += text;

                // 🎯 极简状态更新 - 只更新内容，不触发复杂状态传播
                const state = get();
                for (const session of state.sessions) {
                  if (session.id === currentSession.id) {
                    for (const msg of session.messages) {
                      if (msg.id === botMessageId) {
                        msg.content = botMessageContent;
                        break; // 找到后退出内层循环
                      }
                    }
                    break; // 退出外层循环
                  }
                }
                // 🎯 仅通知UI更新，不传播状态变化
                set({ sessions: [...state.sessions] });
              }

              // 🎯 极简完成处理
              else if (event.type === 'done') {
                console.log(
                  '🏁 对话完成，最终内容:',
                  JSON.stringify(botMessageContent)
                );

                // 🎯 彻底关闭SSE连接，防止重复触发
                const { currentEventSource } = get();
                if (currentEventSource) {
                  console.log('🔒 立即关闭SSE连接');
                  currentEventSource.close();
                }
                if (activeSSEConnection) {
                  console.log('🔒 关闭全局SSE连接');
                  activeSSEConnection.close();
                  activeSSEConnection = null;
                }

                // 🎯 直接修改状态并清理
                const state = get();
                for (const session of state.sessions) {
                  if (session.id === currentSession.id) {
                    for (const msg of session.messages) {
                      if (msg.id === botMessageId) {
                        msg.content = botMessageContent;
                        msg.status = 'sent';
                        break;
                      }
                    }
                    break;
                  }
                }

                // 🎯 一次性清理所有状态
                set({
                  isLoading: false,
                  isStreaming: false,
                  currentEventSource: null,
                });

                console.log('✅ 消息处理完成，所有连接已关闭');
              }
            },
            onclose: () => {
              console.log('🔚 SSE连接已关闭');

              // 🎯 彻底清理所有连接引用
              activeSSEConnection = null;
              set({
                isLoading: false,
                isStreaming: false,
                currentEventSource: null,
              });

              console.log('🧹 所有状态和连接已清理');
            },
            onerror: (error) => {
              console.error('❌ SSE连接错误:', error);

              // 🎯 极简错误处理
              set({
                error: '连接错误，请重试',
                isLoading: false,
                isStreaming: false,
                currentEventSource: null,
              });
            },
          };

          // 🎯 异步函数执行追踪
          const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          console.log(
            `🚀 [${executionId}] 开始异步执行 sendStreamingMessageWithSession`
          );

          const beforeSendState = get();
          console.log(`📊 [${executionId}] 发送前状态检查:`, {
            isStreaming: beforeSendState.isStreaming,
            hasCurrentEventSource: !!beforeSendState.currentEventSource,
            botMessageId,
            callCount: sendMessageCallCount,
            sseCount: sseConnectionCount,
          });

          // 🎯 添加执行标记，防止重复执行
          console.log(
            `📡 [${executionId}] 即将调用 sendStreamingMessageWithSession`
          );
          const eventSource = await sendStreamingMessageWithSession(
            currentSession.sessionData,
            content,
            sseCallbacks,
            {
              knowledgeId: knowledgeId || currentSession.knowledgeId,
            }
          );

          console.log(
            `✅ [${executionId}] sendStreamingMessageWithSession 执行完成`
          );
          // 🎯 双重保存EventSource引用
          const eventSourceTyped = eventSource as unknown as EventSource;
          activeSSEConnection = eventSourceTyped; // 全局跟踪
          set({ currentEventSource: eventSourceTyped }); // 状态管理
          console.log(`🔗 [${executionId}] EventSource 已保存到状态和全局跟踪`);
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : '发送消息失败';
          set({
            error: errorMessage,
            isLoading: false,
            isStreaming: false,
            currentEventSource: null,
          });
          console.error('❌ 发送消息失败:', error);
        } finally {
          // 🎯 确保无论如何都清理执行标记
          isExecutingSendMessage = false;
          console.log('🔓 清理执行标记');
        }
      },

      // 停止流式输出
      stopStreaming: () => {
        const { currentEventSource, currentSession, sessions } = get();

        console.log('🛑 用户主动停止流式输出');

        if (currentEventSource) {
          currentEventSource.close();
        }

        // 将所有发送中的消息标记为已发送
        if (currentSession) {
          const updatedSessions = sessions.map((session) => {
            if (session.id === currentSession.id) {
              const updatedMessages = session.messages.map((msg) => {
                if (msg.role === 'assistant' && msg.status === 'sending') {
                  return { ...msg, status: 'sent' as const };
                }
                return msg;
              });
              return {
                ...session,
                messages: updatedMessages,
                updatedAt: new Date(),
              };
            }
            return session;
          });

          set({ sessions: updatedSessions });

          // 更新当前会话
          const updatedCurrentSession = updatedSessions.find(
            (s) => s.id === currentSession.id
          );
          if (updatedCurrentSession) {
            set({ currentSession: updatedCurrentSession });
          }
        }

        // 重置所有相关状态
        set({
          isStreaming: false,
          isLoading: false,
          currentEventSource: null,
        });

        // 如果有正在发送的消息，将其标记为已发送
        if (currentSession) {
          const { sessions } = get();
          const updatedSessions = sessions.map((session) => {
            if (session.id === currentSession.id) {
              const updatedMessages = session.messages.map((msg) => {
                if (msg.status === 'sending') {
                  return {
                    ...msg,
                    status: 'sent' as const,
                  };
                }
                return msg;
              });
              return { ...session, messages: updatedMessages };
            }
            return session;
          });

          set({ sessions: updatedSessions });

          // 更新当前会话
          const updatedCurrentSession = updatedSessions.find(
            (s) => s.id === currentSession.id
          );
          if (updatedCurrentSession) {
            set({ currentSession: updatedCurrentSession });
          }
        }
      },

      // UI状态管理
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
      clearError: () => set({ error: null }),
    }),
    {
      name: 'knowledge-ai-chat-storage',
      storage: createJSONStorage(() => AsyncStorage),
      // 只持久化必要的数据
      partialize: (state) => ({
        sessions: state.sessions,
      }),
    }
  )
);
