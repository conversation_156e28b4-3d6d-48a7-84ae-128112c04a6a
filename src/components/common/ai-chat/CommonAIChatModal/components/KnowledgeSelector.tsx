import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@/styles';
import { useKnowledgeStore } from '@/stores/knowledgeStore';
import { KnowledgeCategory } from '@/types';

interface KnowledgeSelectorProps {
  selectedKnowledge: KnowledgeCategory | null;
  onKnowledgeSelect: (knowledge: KnowledgeCategory | null) => void;
}

const { width: screenWidth } = Dimensions.get('window');

const KnowledgeSelector: React.FC<KnowledgeSelectorProps> = ({
  selectedKnowledge,
  onKnowledgeSelect,
}) => {
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const { categories, initCategory } = useKnowledgeStore();

  useEffect(() => {
    // 初始化知识库分类
    initCategory();
  }, [initCategory]);

  const handleKnowledgeSelect = (knowledge: KnowledgeCategory | null) => {
    onKnowledgeSelect(knowledge);
    setIsDropdownVisible(false);
  };

  const renderKnowledgeItem = (knowledge: KnowledgeCategory) => (
    <TouchableOpacity
      key={knowledge.id}
      style={styles.dropdownItem}
      onPress={() => handleKnowledgeSelect(knowledge)}
    >
      <View style={styles.knowledgeInfo}>
        <Text style={styles.knowledgeName} numberOfLines={1}>
          {knowledge.sceneName}
        </Text>
        {knowledge.knowledgeId && (
          <Text style={styles.knowledgeId} numberOfLines={1}>
            ID: {knowledge.knowledgeId}
          </Text>
        )}
      </View>
      {selectedKnowledge?.id === knowledge.id && (
        <Ionicons name="checkmark" size={16} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* 选择器按钮 */}
      <TouchableOpacity
        style={styles.selectorButton}
        onPress={() => setIsDropdownVisible(true)}
      >
        <View style={styles.selectorContent}>
          <Ionicons
            name="library-outline"
            size={16}
            color={colors.text.secondary}
          />
          <Text style={styles.selectorText} numberOfLines={1}>
            {selectedKnowledge ? selectedKnowledge.sceneName : '选择知识库'}
          </Text>
          <Ionicons
            name="chevron-down"
            size={14}
            color={colors.text.secondary}
          />
        </View>
      </TouchableOpacity>

      {/* 下拉选择弹窗 */}
      <Modal
        visible={isDropdownVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsDropdownVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsDropdownVisible(false)}
        >
          <View style={styles.dropdownContainer}>
            <View style={styles.dropdownHeader}>
              <Text style={styles.dropdownTitle}>选择知识库</Text>
              <TouchableOpacity onPress={() => setIsDropdownVisible(false)}>
                <Ionicons name="close" size={20} color={colors.text.primary} />
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.dropdownList}
              showsVerticalScrollIndicator={false}
            >
              {/* 全部知识库选项 */}
              <TouchableOpacity
                style={styles.dropdownItem}
                onPress={() => handleKnowledgeSelect(null)}
              >
                <View style={styles.knowledgeInfo}>
                  <Text style={styles.knowledgeName}>全部知识库</Text>
                  <Text style={styles.knowledgeDescription}>
                    搜索所有知识库内容
                  </Text>
                </View>
                {!selectedKnowledge && (
                  <Ionicons name="checkmark" size={16} color={colors.primary} />
                )}
              </TouchableOpacity>

              {/* 分隔线 */}
              {categories.length > 0 && <View style={styles.separator} />}

              {/* 知识库列表 */}
              {categories.map(renderKnowledgeItem)}

              {/* 空状态 */}
              {categories.length === 0 && (
                <View style={styles.emptyState}>
                  <Ionicons
                    name="library-outline"
                    size={32}
                    color={colors.text.tertiary}
                  />
                  <Text style={styles.emptyText}>暂无知识库</Text>
                  <Text style={styles.emptySubText}>
                    请先上传文档创建知识库
                  </Text>
                </View>
              )}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  selectorButton: {
    height: 36,
    paddingHorizontal: spacing.sm,
    backgroundColor: colors.background.secondary,
    borderRadius: 18,
    justifyContent: 'center',
  },
  selectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  selectorText: {
    flex: 1,
    fontSize: 12,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdownContainer: {
    width: screenWidth * 0.8,
    maxHeight: 400,
    backgroundColor: colors.background.primary,
    borderRadius: 12,
    overflow: 'hidden',
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  dropdownTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    fontFamily: typography.fontFamily,
  },
  dropdownList: {
    maxHeight: 300,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  knowledgeInfo: {
    flex: 1,
    marginRight: spacing.sm,
  },
  knowledgeName: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text.primary,
    fontFamily: typography.fontFamily,
    marginBottom: 2,
  },
  knowledgeId: {
    fontSize: 12,
    color: colors.text.tertiary,
    fontFamily: typography.fontFamily,
  },
  knowledgeDescription: {
    fontSize: 12,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily,
  },
  separator: {
    height: 1,
    backgroundColor: colors.border.light,
    marginHorizontal: spacing.lg,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xl * 2,
    paddingHorizontal: spacing.lg,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.secondary,
    marginTop: spacing.sm,
    fontFamily: typography.fontFamily,
  },
  emptySubText: {
    fontSize: 12,
    color: colors.text.tertiary,
    marginTop: spacing.xs,
    textAlign: 'center',
    fontFamily: typography.fontFamily,
  },
});

export default KnowledgeSelector;
