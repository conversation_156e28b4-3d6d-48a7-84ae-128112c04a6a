import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Keyboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useKnowledgeAIChatStore } from '../stores/knowledgeAIChatStore';
import { typography } from '@/styles';

import MessageRenderer from '../../../../../components/recording/ai-features/components/MessageRenderer';
import ChatInput from './ChatInput';

interface ChatContentProps {
  // 专门为知识空间和写作空间设计，不需要录音参数
}

const ChatContent: React.FC<ChatContentProps> = () => {
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);
  const insets = useSafeAreaInsets();

  // 使用知识问答会话 store
  const { currentSession } = useKnowledgeAIChatStore();

  // 获取知识库聊天历史 - 简化版本
  const messages = (currentSession?.messages || []).map((msg) => ({
    id: msg.id,
    role: msg.role,
    content: msg.content,
    type: 'text' as const,
    timestamp: msg.timestamp,
    status: msg.status || ('sent' as const),
  }));

  const showWelcome = messages.length === 0;

  // 🎯 极简调试信息 - 避免频繁触发
  React.useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      // 只在关键状态变化时输出日志
      if (lastMessage.role === 'assistant' && lastMessage.status === 'sent') {
        console.log('📚 消息完成:', {
          messageCount: messages.length,
          content: lastMessage.content?.substring(0, 50) + '...',
        });
      }
    }
  }, [messages.length, messages]); // 依赖消息数量和消息变化

  // 检查是否有有效的会话数据
  const hasValidSession = currentSession?.sessionData?.sessionId;

  // 自动创建会话逻辑移至 CommonAIChatModal.tsx 中统一处理

  // 键盘事件监听
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      (e) => {
        setKeyboardHeight(e.endCoordinates.height);
        setIsKeyboardVisible(true);
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardHeight(0);
        setIsKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  // 预设问题
  const presetQuestions = [
    '帮我整理一下会议的待办事项',
    '帮我总结一下会议的核心要点',
  ];

  const renderPresetQuestion = (question: string, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.presetQuestionCard}
      onPress={() => {
        // TODO: 通过ChatInput组件处理预设问题，暂时禁用
      }}
    >
      <Image
        source={require('../../../../../../assets/images/question-icon.png')}
        style={styles.questionIconImage}
        resizeMode="contain"
      />
      <Text style={styles.presetQuestionText}>{question}</Text>
      <Image
        source={require('../../../../../../assets/images/chevron-right.png')}
        style={styles.chevronIconImage}
        resizeMode="contain"
      />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* 内容区域 */}
      <ScrollView
        ref={scrollViewRef}
        style={[
          styles.contentWrapper,
          {
            marginBottom: 90,
          },
        ]}
        contentContainerStyle={[
          styles.contentContainer,
          showWelcome && styles.contentContainerWelcome, // 欢迎页面使用居中布局
          !showWelcome && styles.chatContainer,
          {
            paddingBottom: isKeyboardVisible ? keyboardHeight + 32 : 32,
          },
        ]}
        showsVerticalScrollIndicator={true}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="interactive"
        scrollEnabled={true}
        nestedScrollEnabled={true}
      >
        {showWelcome ? (
          <>
            {/* 助手简介 */}
            <View style={styles.assistantIntro}>
              {/* Logo */}
              <View style={styles.logoContainer}>
                <Image
                  source={require('../../../../../../assets/logo.png')}
                  style={styles.logoImage}
                  resizeMode="contain"
                />
              </View>

              {/* 欢迎语 */}
              <View style={styles.welcomeContainer}>
                <View style={styles.welcomeHeader}>
                  <Text style={styles.welcomeText}>Hi！我是</Text>
                  <Image
                    source={require('../../../../../../assets/images/gradient-name-xiaozi.png')}
                    style={styles.gradientNameImage}
                    resizeMode="contain"
                  />
                  <Text style={styles.roleText}>你的全能办公搭子</Text>
                </View>
              </View>

              <Text style={styles.description}>
                我能学习和理解人类语言，提供业务知识解答服务
              </Text>
            </View>

            {/* 预设问题 */}
            <View style={styles.presetQuestions}>
              {presetQuestions.map(renderPresetQuestion)}
            </View>

            {/* 会话状态提示 */}
            {!hasValidSession && (
              <View style={styles.sessionTipContainer}>
                <Ionicons
                  name="information-circle-outline"
                  size={16}
                  color="#FF6B6B"
                />
                <Text style={styles.sessionTipText}>
                  请先打开AI功能界面创建会话后再进行对话
                </Text>
              </View>
            )}
          </>
        ) : (
          <>
            {/* 聊天消息 - 简化版本 */}
            {messages.map((message) => {
              // 安全检查消息对象
              if (!message || !message.id || !message.role) {
                return null;
              }

              return (
                <MessageRenderer
                  key={message.id}
                  message={message}
                  streamingMessage={null}
                  displayedContent={message.content}
                  isTyping={false}
                  cursorVisible={false}
                  receivedContent={message.content}
                  isStreamingComplete={true}
                  skipTypewriterAnimation={() => {}}
                  styles={styles}
                />
              );
            })}
          </>
        )}
      </ScrollView>

      {/* 底部输入框 - 使用ChatInput组件 */}
      <View
        style={[styles.inputWrapper, { bottom: Math.max(insets.bottom, 16) }]}
      >
        <ChatInput placeholder="点击输入与知识相关的问题" />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    backgroundColor: 'transparent',
  },
  contentWrapper: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  contentContainer: {
    alignItems: 'center',
    padding: 16,
    paddingBottom: 16,
    gap: 16,
    backgroundColor: 'transparent',
  },
  contentContainerWelcome: {
    minHeight: '100%',
    // 移除justifyContent: 'center'，让内容从顶部开始排列，与录音AI问问页面保持一致
  },
  chatContainer: {
    alignItems: 'stretch',
    gap: 12,
    paddingBottom: 32,
    paddingHorizontal: 16,
    backgroundColor: 'transparent',
  },
  inputWrapper: {
    position: 'absolute',
    left: 16,
    right: 16,
    paddingTop: 16,
    backgroundColor: 'transparent',
  },
  assistantIntro: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  logoContainer: {
    width: 64,
    height: 64,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  logoImage: {
    width: 64,
    height: 64,
    borderRadius: 20,
  },
  welcomeContainer: {
    alignItems: 'center',
    width: 272,
  },
  welcomeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
    flexWrap: 'nowrap',
  },
  welcomeText: {
    fontSize: 18,
    fontFamily: typography.fontFamily,
    fontWeight: '500',
    color: '#2A2B33',
    lineHeight: 24, // 1.3333 * 18 = 24
  },
  gradientNameImage: {
    width: 35,
    height: 17,
  },
  roleText: {
    fontSize: 16,
    fontFamily: typography.fontFamily,
    fontWeight: '500',
    color: '#2A2B33',
    textAlign: 'center',
    lineHeight: 24, // 1.5 * 16 = 24
  },
  description: {
    fontSize: 12,
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    color: '#64677A',
    textAlign: 'center',
    width: 272,
    lineHeight: 18, // 1.5 * 12 = 18
  },
  presetQuestions: {
    width: '100%',
    gap: 8,
    alignItems: 'center',
  },
  presetQuestionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#F2F3F5',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8, // 增加垂直内边距，确保图标和文字有足够空间居中
    gap: 4,
    shadowColor: '#64677A',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 1,
    // 移除固定宽度，改为自适应宽度
    alignSelf: 'center', // 居中对齐
    minHeight: 36, // 设置最小高度，确保有足够空间
  },
  questionIconImage: {
    width: 20,
    height: 20,
  },
  presetQuestionText: {
    // 移除 flex: 1，改为自适应宽度
    fontSize: 12,
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    color: '#414352',
    textAlign: 'center',
    lineHeight: 20, // 1.6666 * 12 = 20
  },
  chevronIconImage: {
    width: 12,
    height: 12,
  },
  sessionTipContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF5F5',
    borderRadius: 12,
    padding: 16,
    gap: 8,
    borderWidth: 1,
    borderColor: '#FFE0E0',
  },
  sessionTipText: {
    fontSize: 13,
    color: '#D64545',
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    textAlign: 'center',
    flex: 1,
  },
  userMessageContainer: {
    alignItems: 'flex-end',
    paddingLeft: 48,
    paddingRight: 16,
    paddingBottom: 12,
    backgroundColor: 'transparent',
  },
  userMessageBubble: {
    borderRadius: 12,
    borderTopRightRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    maxWidth: '100%', // 确保气泡不超出容器
  },
  userMessageText: {
    fontSize: 14,
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    color: '#FFFFFF',
    lineHeight: 24,
    flexWrap: 'wrap', // 允许文字换行
  },
  botMessageContainer: {
    alignItems: 'flex-start',
    paddingHorizontal: 0,
    paddingBottom: 8,
    gap: 10,
    maxWidth: '100%',
    backgroundColor: 'transparent',
  },
  botMessageBubble: {
    backgroundColor: 'transparent',
    padding: 12,
    gap: 12,
    width: '100%',
  },
  botMessageText: {
    fontSize: 14,
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    color: '#414352',
    lineHeight: 24,
  },
  messageActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 4,
    borderRadius: 4,
  },
  actionSeparator: {
    width: 1,
    height: 12,
    backgroundColor: '#EBEBF0',
    borderRadius: 4,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  thinkingText: {
    fontSize: 14,
    fontFamily: typography.fontFamily,
    fontWeight: '400',
    color: '#9092A3',
    marginLeft: 8,
  },
});

export default ChatContent;
