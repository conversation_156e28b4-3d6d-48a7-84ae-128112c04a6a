import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@/styles';
import {
  useKnowledgeAIChatStore,
  KnowledgeAISession,
} from '../stores/knowledgeAIChatStore';

interface SessionManagerProps {
  onSessionSelect?: (session: KnowledgeAISession) => void;
}

const SessionManager: React.FC<SessionManagerProps> = ({ onSessionSelect }) => {
  const [showSessionList, setShowSessionList] = useState(false);
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState('');

  const {
    sessions,
    currentSession,
    isCreatingSession,
    createSession,
    setCurrentSession,
    deleteSession,
    updateSessionTitle,
  } = useKnowledgeAIChatStore();

  // 处理新建会话
  const handleCreateSession = async () => {
    await createSession();
    setShowSessionList(false);
  };

  // 处理会话选择
  const handleSessionSelect = (session: KnowledgeAISession) => {
    setCurrentSession(session);
    onSessionSelect?.(session);
    setShowSessionList(false);
  };

  // 处理会话删除
  const handleDeleteSession = (sessionId: string) => {
    Alert.alert('删除会话', '确定要删除这个会话吗？删除后无法恢复。', [
      { text: '取消', style: 'cancel' },
      {
        text: '删除',
        style: 'destructive',
        onPress: () => deleteSession(sessionId),
      },
    ]);
  };

  // 处理会话重命名
  const handleStartEdit = (session: KnowledgeAISession) => {
    setEditingSessionId(session.id);
    setEditingTitle(session.title);
  };

  const handleSaveEdit = () => {
    if (editingSessionId && editingTitle.trim()) {
      updateSessionTitle(editingSessionId, editingTitle.trim());
    }
    setEditingSessionId(null);
    setEditingTitle('');
  };

  // const handleCancelEdit = () => {
  //   setEditingSessionId(null);
  //   setEditingTitle('');
  // };

  // 格式化时间
  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - new Date(date).getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    return new Date(date).toLocaleDateString();
  };

  const renderSessionItem = (session: KnowledgeAISession) => (
    <View key={session.id} style={styles.sessionItem}>
      <TouchableOpacity
        style={[
          styles.sessionContent,
          currentSession?.id === session.id && styles.activeSession,
        ]}
        onPress={() => handleSessionSelect(session)}
      >
        <View style={styles.sessionInfo}>
          {editingSessionId === session.id ? (
            <View style={styles.editContainer}>
              <TextInput
                style={styles.editInput}
                value={editingTitle}
                onChangeText={setEditingTitle}
                onSubmitEditing={handleSaveEdit}
                onBlur={handleSaveEdit}
                autoFocus
                selectTextOnFocus
              />
            </View>
          ) : (
            <>
              <Text style={styles.sessionTitle} numberOfLines={1}>
                {session.title}
              </Text>
              <Text style={styles.sessionTime}>
                {formatTime(session.updatedAt)}
              </Text>
              {session.messages.length > 0 && (
                <Text style={styles.lastMessage} numberOfLines={1}>
                  {session.messages[session.messages.length - 1]?.content}
                </Text>
              )}
            </>
          )}
        </View>

        {currentSession?.id === session.id && (
          <Ionicons name="checkmark-circle" size={16} color={colors.primary} />
        )}
      </TouchableOpacity>

      {/* 会话操作按钮 */}
      <View style={styles.sessionActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleStartEdit(session)}
        >
          <Ionicons name="pencil" size={14} color={colors.text.secondary} />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleDeleteSession(session.id)}
        >
          <Ionicons name="trash" size={14} color={colors.error} />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* 当前会话显示 */}
      <TouchableOpacity
        style={styles.currentSessionButton}
        onPress={() => setShowSessionList(true)}
      >
        <View style={styles.currentSessionInfo}>
          <Text style={styles.currentSessionTitle} numberOfLines={1}>
            {currentSession ? currentSession.title : '选择会话'}
          </Text>
          {currentSession && (
            <Text style={styles.currentSessionTime}>
              {formatTime(currentSession.updatedAt)}
            </Text>
          )}
        </View>
        <Ionicons name="chevron-down" size={16} color={colors.text.secondary} />
      </TouchableOpacity>

      {/* 会话列表弹窗 */}
      <Modal
        visible={showSessionList}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSessionList(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.sessionListContainer}>
            {/* 标题栏 */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>会话历史</Text>
              <View style={styles.headerActions}>
                <TouchableOpacity
                  style={styles.newSessionButton}
                  onPress={handleCreateSession}
                  disabled={isCreatingSession}
                >
                  <Ionicons
                    name="add"
                    size={20}
                    color={
                      isCreatingSession ? colors.text.tertiary : colors.primary
                    }
                  />
                </TouchableOpacity>
                <TouchableOpacity onPress={() => setShowSessionList(false)}>
                  <Ionicons
                    name="close"
                    size={20}
                    color={colors.text.primary}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* 会话列表 */}
            <ScrollView
              style={styles.sessionList}
              showsVerticalScrollIndicator={false}
            >
              {sessions.length > 0 ? (
                sessions.map(renderSessionItem)
              ) : (
                <View style={styles.emptyState}>
                  <Ionicons
                    name="chatbubbles-outline"
                    size={48}
                    color={colors.text.tertiary}
                  />
                  <Text style={styles.emptyText}>暂无会话</Text>
                  <Text style={styles.emptySubText}>
                    点击右上角 + 号创建新会话
                  </Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.md,
  },
  currentSessionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
  },
  currentSessionInfo: {
    flex: 1,
    marginRight: spacing.sm,
  },
  currentSessionTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text.primary,
    fontFamily: typography.fontFamily,
  },
  currentSessionTime: {
    fontSize: 12,
    color: colors.text.secondary,
    marginTop: 2,
    fontFamily: typography.fontFamily,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  sessionListContainer: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    fontFamily: typography.fontFamily,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  newSessionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sessionList: {
    maxHeight: 400,
  },
  sessionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  sessionContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.xs,
  },
  activeSession: {
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    paddingHorizontal: spacing.sm,
  },
  sessionInfo: {
    flex: 1,
    marginRight: spacing.sm,
  },
  sessionTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text.primary,
    fontFamily: typography.fontFamily,
  },
  sessionTime: {
    fontSize: 12,
    color: colors.text.secondary,
    marginTop: 2,
    fontFamily: typography.fontFamily,
  },
  lastMessage: {
    fontSize: 12,
    color: colors.text.tertiary,
    marginTop: 2,
    fontFamily: typography.fontFamily,
  },
  editContainer: {
    flex: 1,
  },
  editInput: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text.primary,
    fontFamily: typography.fontFamily,
    borderBottomWidth: 1,
    borderBottomColor: colors.primary,
    paddingVertical: 2,
  },
  sessionActions: {
    flexDirection: 'row',
    gap: spacing.xs,
  },
  actionButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xl * 2,
    paddingHorizontal: spacing.lg,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.secondary,
    marginTop: spacing.sm,
    fontFamily: typography.fontFamily,
  },
  emptySubText: {
    fontSize: 12,
    color: colors.text.tertiary,
    marginTop: spacing.xs,
    textAlign: 'center',
    fontFamily: typography.fontFamily,
  },
});

export default SessionManager;
