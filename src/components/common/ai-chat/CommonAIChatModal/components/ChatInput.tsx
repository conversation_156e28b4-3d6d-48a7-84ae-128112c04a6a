import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Keyboard,
  Platform,
  Image,
  Modal,
  ScrollView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, typography } from '@/styles';
import { useKnowledgeAIChatStore } from '../stores/knowledgeAIChatStore';
import { useKnowledgeStore } from '@/stores/knowledgeStore';
import { KnowledgeCategory } from '@/types';

interface ChatInputProps {
  placeholder?: string;
}

const { width: screenWidth } = Dimensions.get('window');

const ChatInput: React.FC<ChatInputProps> = ({
  placeholder = '请输入您的问题...',
}) => {
  const [inputText, setInputText] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isKnowledgeSelectorVisible, setIsKnowledgeSelectorVisible] =
    useState(false);
  const [selectedKnowledge, setSelectedKnowledge] =
    useState<KnowledgeCategory | null>(null);
  const inputRef = useRef<TextInput>(null);

  const {
    currentSession,
    sendMessage,
    isLoading,
    error,
    clearError,
    createSession,
    isCreatingSession,
    isStreaming,
    stopStreaming,
  } = useKnowledgeAIChatStore();

  // 知识库相关
  const { categories, initCategory } = useKnowledgeStore();

  // 初始化知识库分类
  React.useEffect(() => {
    initCategory();
  }, [initCategory]);

  // 处理知识库选择
  const handleKnowledgeSelect = (knowledge: KnowledgeCategory | null) => {
    console.log('🔧 知识库选择调试:', {
      knowledge: knowledge?.sceneName || '无选择',
      knowledgeId: knowledge?.knowledgeId || 'undefined',
      knowledgeData: knowledge,
    });
    setSelectedKnowledge(knowledge);
    setIsKnowledgeSelectorVisible(false);
  };

  // 处理文件夹图标点击
  const handleFolderIconPress = () => {
    if (selectedKnowledge) {
      // 如果已选择知识库，则清除选择
      console.log('🔧 清除知识库选择');
      setSelectedKnowledge(null);
    } else {
      // 否则打开选择器
      setIsKnowledgeSelectorVisible(true);
    }
  };

  // 处理发送消息
  const handleSendMessage = async () => {
    const message = inputText.trim();
    if (!message || isLoading || isStreaming) return;

    if (!currentSession) {
      // TODO: 显示需要创建会话的提示
      return;
    }

    // 清除之前的错误
    clearError();

    // 清空输入框
    setInputText('');
    Keyboard.dismiss();

    try {
      // 发送消息，传递选中的知识库ID
      console.log('🔧 发送消息调试:', {
        message,
        selectedKnowledge: selectedKnowledge?.sceneName || '无选择',
        knowledgeId: selectedKnowledge?.knowledgeId || 'undefined',
      });
      await sendMessage(message, selectedKnowledge?.knowledgeId);
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  };

  // 处理输入框聚焦
  const handleFocus = () => {
    setIsFocused(true);
    clearError();
  };

  // 处理输入框失焦
  const handleBlur = () => {
    setIsFocused(false);
  };

  // 获取输入框的实际值 - 当选择了知识库时，插入一个特殊字符以支持删除操作
  const getInputValue = () => {
    if (selectedKnowledge && inputText === '') {
      return '\u200B'; // 零宽度空格，不可见但能触发键盘事件
    }
    return inputText;
  };

  // 处理文本变化
  const handleTextChange = (text: string) => {
    // 如果文本是零宽度空格或空字符串，说明用户删除了内容
    if ((text === '\u200B' || text === '') && selectedKnowledge) {
      // 如果之前有输入内容，现在被删除了，只清空输入内容
      if (inputText !== '') {
        setInputText('');
      } else {
        // 如果之前没有输入内容，说明用户想删除知识库选择
        setSelectedKnowledge(null);
        setInputText('');
      }
    } else {
      // 过滤掉零宽度空格
      const cleanText = text.replace(/\u200B/g, '');
      setInputText(cleanText);
    }
  };

  // 处理键盘输入
  const handleKeyPress = (e: { nativeEvent: { key: string } }) => {
    // 当按删除键且只有零宽度空格时，清除知识库选择
    if (
      e.nativeEvent.key === 'Backspace' &&
      getInputValue() === '\u200B' &&
      selectedKnowledge
    ) {
      setSelectedKnowledge(null);
      setInputText('');
    }
  };

  // 检查是否可以发送
  const canSend =
    inputText.trim().length > 0 && !isLoading && !isStreaming && currentSession;

  return (
    <View style={styles.container}>
      {/* 错误提示 */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={clearError}>
            <Ionicons name="close" size={16} color={colors.error} />
          </TouchableOpacity>
        </View>
      )}

      {/* 输入框容器 */}
      <View style={styles.inputContainer}>
        {/* 输入框 */}
        <View
          style={[
            styles.inputWrapper,
            isFocused && styles.inputWrapperFocused,
            !currentSession && styles.inputWrapperDisabled,
          ]}
        >
          <LinearGradient
            colors={['#3053ED', '#5279FF80', '#3EDDB6']}
            start={{ x: -0.12, y: -0.038 }}
            end={{ x: 1.024, y: 1.047 }}
            style={styles.gradientBorder}
          >
            <View style={styles.inputInner}>
              {/* 左侧文件图标 */}
              <TouchableOpacity
                style={styles.fileIconContainer}
                onPress={handleFolderIconPress}
              >
                <Image
                  source={require('../../../../../../assets/images/ai-chat/folder-add-icon.png')}
                  style={styles.fileIcon}
                  resizeMode="contain"
                />
              </TouchableOpacity>

              {/* 知识库选择提示 */}
              {selectedKnowledge && (
                <Text style={styles.knowledgeIndicator} numberOfLines={1}>
                  @{selectedKnowledge.sceneName}
                </Text>
              )}

              <TextInput
                ref={inputRef}
                style={[
                  styles.textInput,
                  !currentSession && styles.textInputDisabled,
                ]}
                value={getInputValue()}
                onChangeText={handleTextChange}
                onKeyPress={handleKeyPress}
                placeholder={
                  !selectedKnowledge && currentSession ? placeholder : ''
                }
                placeholderTextColor="#B0B2BF"
                multiline
                maxLength={1000}
                onFocus={handleFocus}
                onBlur={handleBlur}
                onSubmitEditing={handleSendMessage}
                returnKeyType="send"
                blurOnSubmit={false}
                editable={!!currentSession && !isLoading && !isStreaming}
              />

              {/* 发送/停止按钮 */}
              <View style={styles.inputActions}>
                {isStreaming ? (
                  // 流式输出时显示停止按钮
                  <TouchableOpacity
                    style={styles.stopButtonContainer}
                    onPress={stopStreaming}
                  >
                    <LinearGradient
                      colors={['#3053ED', '#5279FF80', '#3EDDB6']}
                      start={{ x: -0.12, y: -0.038 }}
                      end={{ x: 1.024, y: 1.047 }}
                      style={styles.stopButton}
                    >
                      <Ionicons name="stop" size={16} color="#FFFFFF" />
                    </LinearGradient>
                  </TouchableOpacity>
                ) : (
                  // 正常状态显示发送按钮
                  <TouchableOpacity
                    style={[
                      styles.sendButton,
                      !canSend && styles.sendButtonDisabled,
                    ]}
                    onPress={handleSendMessage}
                    disabled={!canSend}
                  >
                    <Image
                      source={require('../../../../../../assets/images/send-icon.png')}
                      style={styles.sendIconImage}
                      resizeMode="contain"
                    />
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </LinearGradient>
        </View>
      </View>

      {/* 会话状态提示 */}
      {!currentSession && !isCreatingSession && (
        <View style={styles.sessionTipContainer}>
          <Ionicons
            name="information-circle-outline"
            size={14}
            color={colors.text.secondary}
          />
          <Text style={styles.sessionTipText}>正在创建会话...</Text>
          <TouchableOpacity
            onPress={() => createSession('知识问答会话')}
            style={styles.createSessionButton}
          >
            <Text style={styles.createSessionButtonText}>手动创建</Text>
          </TouchableOpacity>
        </View>
      )}

      {isCreatingSession && (
        <View style={styles.sessionTipContainer}>
          <Ionicons
            name="time-outline"
            size={14}
            color={colors.text.secondary}
          />
          <Text style={styles.sessionTipText}>正在创建会话，请稍候...</Text>
        </View>
      )}

      {/* 知识库选择弹窗 */}
      <Modal
        visible={isKnowledgeSelectorVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsKnowledgeSelectorVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsKnowledgeSelectorVisible(false)}
        >
          <View style={styles.knowledgeSelector}>
            <View style={styles.knowledgeHeader}>
              <Text style={styles.knowledgeTitle}>选择知识库</Text>
              <TouchableOpacity
                onPress={() => setIsKnowledgeSelectorVisible(false)}
              >
                <Ionicons name="close" size={20} color={colors.text.primary} />
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.knowledgeList}
              showsVerticalScrollIndicator={false}
            >
              {/* 知识库列表 */}
              {categories.map((knowledge) => (
                <TouchableOpacity
                  key={knowledge.id}
                  style={[
                    styles.knowledgeItem,
                    selectedKnowledge?.id === knowledge.id &&
                      styles.knowledgeItemSelected,
                  ]}
                  onPress={() => handleKnowledgeSelect(knowledge)}
                >
                  <View style={styles.knowledgeInfo}>
                    <Text style={styles.knowledgeName} numberOfLines={1}>
                      {knowledge.sceneName}
                    </Text>
                  </View>
                  {selectedKnowledge?.id === knowledge.id && (
                    <Ionicons
                      name="checkmark"
                      size={16}
                      color={colors.primary}
                    />
                  )}
                </TouchableOpacity>
              ))}

              {/* 空状态 */}
              {categories.length === 0 && (
                <View style={styles.emptyState}>
                  <Ionicons
                    name="library-outline"
                    size={32}
                    color={colors.text.tertiary}
                  />
                  <Text style={styles.emptyText}>暂无知识库</Text>
                  <Text style={styles.emptySubText}>
                    请先上传文档创建知识库
                  </Text>
                </View>
              )}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: spacing.sm,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.error + '20',
    borderRadius: 8,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    marginBottom: spacing.sm,
  },
  errorText: {
    flex: 1,
    fontSize: 12,
    color: colors.error,
    fontFamily: typography.fontFamily,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  inputWrapper: {
    flex: 1,
    borderRadius: 16,
    padding: 1.5,
    backgroundColor: colors.border,
  },
  inputWrapperFocused: {
    backgroundColor: 'transparent',
  },
  inputWrapperDisabled: {
    backgroundColor: colors.background.secondary,
  },
  gradientBorder: {
    borderRadius: 16,
    padding: 1.5,
  },
  inputInner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
    borderRadius: 14.5,
    paddingHorizontal: 8,
    paddingVertical: 8,
    minHeight: 48,
    gap: 8,
  },
  fileIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 6,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 4,
  },
  fileIcon: {
    width: 24,
    height: 24,
  },
  knowledgeIndicator: {
    fontSize: 14,
    color: '#3053ED',
    fontFamily: typography.fontFamily,
    fontWeight: '500',
    maxWidth: 100, // 限制最大宽度，避免过长
    marginRight: 4, // 与输入框保持小间距
  },
  textInput: {
    flex: 1,
    fontSize: 15,
    color: colors.text.primary,
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    lineHeight: 24,
    maxHeight: 120,
    paddingTop: Platform.OS === 'ios' ? 4 : 2,
    paddingBottom: Platform.OS === 'ios' ? 4 : 2,
    textAlignVertical: 'center',
  },
  textInputDisabled: {
    color: colors.text.tertiary,
  },
  inputActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  sendButton: {
    width: 32,
    height: 32,
    borderRadius: 6,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 4,
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  stopButtonContainer: {
    width: 32,
    height: 32,
    borderRadius: 6,
  },
  stopButton: {
    width: 32,
    height: 32,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendIconImage: {
    width: 24,
    height: 24,
  },
  sessionTipContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    marginTop: spacing.xs,
    gap: spacing.xs,
  },
  sessionTipText: {
    fontSize: 12,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily,
    flex: 1,
  },
  createSessionButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  createSessionButtonText: {
    fontSize: 12,
    color: colors.background.primary,
    fontFamily: typography.fontFamily,
    fontWeight: '500',
  },
  // 知识库选择器样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  knowledgeSelector: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: screenWidth * 1.2, // 最大高度为屏幕宽度的1.2倍
    overflow: 'hidden',
  },
  knowledgeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  knowledgeTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    fontFamily: typography.fontFamily,
  },
  knowledgeList: {
    maxHeight: 400,
  },
  knowledgeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F5F5',
  },
  knowledgeItemSelected: {
    backgroundColor: colors.primary + '10',
  },
  knowledgeInfo: {
    flex: 1,
    marginRight: spacing.sm,
  },
  knowledgeName: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
    fontFamily: typography.fontFamily,
    marginBottom: 2,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xl * 2,
    paddingHorizontal: spacing.lg,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.secondary,
    marginTop: spacing.sm,
    fontFamily: typography.fontFamily,
  },
  emptySubText: {
    fontSize: 12,
    color: colors.text.tertiary,
    marginTop: spacing.xs,
    textAlign: 'center',
    fontFamily: typography.fontFamily,
  },
});

export default ChatInput;
