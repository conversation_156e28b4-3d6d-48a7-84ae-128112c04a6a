import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TextInput,
  FlatList,
  Animated,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@/styles';
import { LinearGradient } from 'expo-linear-gradient';
import { KnowledgeDocument } from '@/types';

interface KnowledgeSearchModalProps {
  visible: boolean;
  onClose: () => void;
  documents: KnowledgeDocument[];
  onDocumentPress: (document: KnowledgeDocument) => void;
}
const { width: screenWidth } = Dimensions.get('window');

const KnowledgeSearchModal: React.FC<KnowledgeSearchModalProps> = ({
  visible,
  onClose,
  documents,
  onDocumentPress,
}) => {
  const [searchText, setSearchText] = useState('');
  const [searchResults, setSearchResults] = useState<KnowledgeDocument[]>([]);
  const [slideAnim] = useState(new Animated.Value(screenWidth));

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: screenWidth,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setSearchText('');
      });
    }
  }, [visible, slideAnim]);

  useEffect(() => {
    if (searchText.trim()) {
      const filteredDocs = performSearch(searchText);
      setSearchResults(filteredDocs);
    } else {
      setSearchResults([]);
    }
  }, [searchText, documents]);

  const performSearch = (query: string): KnowledgeDocument[] => {
    const lowerQuery = query.toLowerCase().trim();

    return documents.filter((doc) => {
      // 搜索标题
      const titleMatch = doc.fileName.toLowerCase().includes(lowerQuery);
      return titleMatch;
    });
  };

  const renderDocumentItem = ({ item }: { item: KnowledgeDocument }) => (
    <TouchableOpacity
      style={styles.documentItem}
      onPress={() => {
        onDocumentPress(item);
        onClose();
      }}
    >
      <View style={styles.documentIcon}>
        {['audio'].includes(item.fileType) ? (
          <LinearGradient
            colors={['#7570E7', '#B9B5FF']}
            style={styles.documentIconBg}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Ionicons name="mic" size={12} color={colors.white} />
          </LinearGradient>
        ) : (
          <LinearGradient
            colors={['#4080FF', '#94BFFF']}
            style={styles.documentIconBg}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Ionicons name="document-text" size={12} color={colors.white} />
          </LinearGradient>
        )}
      </View>

      <View style={styles.documentInfo}>
        <Text style={styles.documentTitle} numberOfLines={1}>
          {item.fileName}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="none"
      transparent={true}
      onRequestClose={onClose}
    >
      <Animated.View
        style={[styles.container, { transform: [{ translateX: slideAnim }] }]}
      >
        <SafeAreaView style={{ flex: 1 }}>
          <View style={styles.header}>
            <View style={styles.searchContainer}>
              <Ionicons name="search" size={20} color={colors.text.secondary} />
              <TextInput
                style={styles.searchInput}
                placeholder="搜索文档、标签..."
                value={searchText}
                onChangeText={setSearchText}
                autoFocus
                returnKeyType="search"
                clearButtonMode="while-editing"
              />
            </View>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelText}>取消</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.resultsContainer}>
            {searchText.trim() ? (
              <>
                <Text style={styles.resultCount}>
                  找到 {searchResults.length} 个结果
                </Text>
                <FlatList
                  data={searchResults}
                  renderItem={renderDocumentItem}
                  keyExtractor={(item) => item.id}
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={styles.resultsList}
                  ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                      <Ionicons
                        name="search"
                        size={48}
                        color={colors.text.secondary}
                      />
                      <Text style={styles.emptyText}>没有找到匹配的文档</Text>
                      <Text style={styles.emptySubText}>
                        尝试其他搜索词或检查拼写
                      </Text>
                    </View>
                  }
                />
              </>
            ) : (
              <View style={styles.placeholderContainer}>
                <Ionicons
                  name="search"
                  size={64}
                  color={colors.background.secondary}
                />
                <Text style={styles.placeholderText}>
                  输入关键词搜索文档和标签
                </Text>
              </View>
            )}
          </View>
        </SafeAreaView>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    height: 44,
  },
  searchInput: {
    flex: 1,
    marginLeft: spacing.sm,
    fontSize: 16,
    color: colors.text.primary,
    height: '100%',
  },
  cancelButton: {
    marginLeft: spacing.md,
    paddingVertical: spacing.sm,
  },
  cancelText: {
    fontSize: 16,
    color: colors.primary,
  },
  resultsContainer: {
    flex: 1,
    paddingTop: spacing.md,
  },
  resultCount: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.md,
    fontSize: 14,
    color: colors.text.secondary,
  },
  resultsList: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  documentIcon: {
    marginRight: spacing.md,
  },
  documentIconBg: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  documentInfo: {
    flex: 1,
  },
  documentTitle: {
    fontSize: 15,
    color: colors.text.primary,
    fontFamily: typography.fontFamily,
    marginBottom: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 80,
  },
  emptyText: {
    fontSize: 16,
    color: colors.text.secondary,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubText: {
    fontSize: 14,
    color: colors.text.tertiary,
  },
  placeholderContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    fontSize: 16,
    color: colors.text.secondary,
    marginTop: spacing.lg,
    textAlign: 'center',
    maxWidth: '70%',
  },
});

export default KnowledgeSearchModal;
