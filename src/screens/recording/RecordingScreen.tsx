import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  Image,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
// import * as Sharing from 'expo-sharing';
import { colors, typography, spacing } from '../../styles';
import { Button, Dialog } from '../../components/common';
import { useRecordingStore } from '../../stores';
import { useRealtimeAudioRecorder } from '../../hooks/useRealtimeAudioRecorder'; // 使用新的实时录音Hook
import { useRealtimeTranscription } from '../../hooks/useRealtimeTranscription';
import { useFloatingWindow } from '../../hooks/useFloatingWindow';
import { useLocation } from '../../hooks/useLocation';
import {
  formatDuration,
  // formatDurationWithMilliseconds,
  formatDate,
} from '../../utils';
import { NoteData, Recording } from '../../types/recording';
import { SwipeListView } from 'react-native-swipe-list-view';
import { TranscriptionModal } from '../../components/recording/TranscriptionModal';
import { RecordingModal } from '../../components/recording/RecordingModal';
import NoteModal from '../../components/recording/NoteModal';
import { useRecordingContext } from '../../contexts/RecordingContext';
import RecordingSearchModal from '../../components/recording/RecordingSearchModal';
import {
  XUNFEI_CONFIG,
  isTranscriptionConfigValid,
} from '../../config/transcription';
// import AudioWaveform from '../../components/recording/AudioWaveform';
import { LinearGradient } from 'expo-linear-gradient';
import Toast from 'react-native-toast-message';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../types/navigation';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as FileSystem from 'expo-file-system';
import { lcdpService } from '@/services/api';
import { DialogUtil } from '@/utils/dialogUtil';

// 计算响应式按钮尺寸，保持原始比例
const getButtonDimensions = () => {
  const screenWidth = Dimensions.get('window').width;
  const containerPadding = 32; // 左右边距总和
  const buttonGap = 12; // 按钮间距
  const availableWidth = screenWidth - containerPadding - buttonGap;

  // 原始设计尺寸和比例
  const originalRecordingWidth = 203;
  const originalImportWidth = 132;
  const originalTotalWidth = originalRecordingWidth + originalImportWidth; // 335
  const originalHeight = 64;

  // 计算缩放比例，但不放大超过原始尺寸
  const scale = Math.min(availableWidth / originalTotalWidth, 1);

  return {
    recordingWidth: originalRecordingWidth * scale,
    importWidth: originalImportWidth * scale,
    height: originalHeight * scale,
    scale,
  };
};

const RecordingScreen: React.FC = () => {
  const {
    recordings,
    addRecording,
    updateRecording,
    deleteRecording,
    retryUpload,
  } = useRecordingStore();

  // 获取安全区域信息，用于精确计算底部间距
  const insets = useSafeAreaInsets();

  // 获取响应式按钮尺寸
  const buttonDimensions = getButtonDimensions();

  // 精确计算底部间距：让最后一个录音项的下边框恰好被Tab栏遮挡
  // Tab栏高度为56px（来自TabNavigator.tsx中的tabBarStyle.height）
  const TAB_BAR_HEIGHT = 56;
  const BORDER_OVERLAP = 1; // 让列表项下边框被Tab栏遮挡，避免双边框
  const bottomPadding = TAB_BAR_HEIGHT + insets.bottom - BORDER_OVERLAP;

  // 用于转写的实时音频数据状态
  const [currentAudioData, setCurrentAudioData] = useState<
    Float32Array | undefined
  >();

  // 使用新的实时录音Hook
  // 🔧 直接集成转写服务，避免不必要的数据转换
  const {
    // isTranscribing: _isTranscribing, // 暂时未使用，注释掉
    isConnected: transcriptionConnected,
    transcriptionText,
    partialText,
    startTranscription,
    stopTranscription,
    sendAudioData: sendPCMData,
    clearTranscription,
  } = useRealtimeTranscription();

  const {
    isRecording,
    duration: recordingDuration,
    isPaused: hookIsPaused,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    hasPermission,
    requestPermissions,
  } = useRealtimeAudioRecorder({
    onAudioData: (audioData: Float32Array) => {
      setCurrentAudioData(audioData);
      console.log('📊 接收到音频数据，样本数:', audioData.length);
    },
    onPCMData: (pcmData: Uint8Array) => {
      // 🚀 直接将原生PCM数据发送给讯飞，避免任何转换损失！
      sendPCMData(pcmData);
      console.log('📤 直接发送原生PCM到讯飞:', {
        字节数: pcmData.length,
        格式: '16kHz、16bit、单声道',
        无损传输: true,
      });
    },
    onError: (error: Error) => {
      console.error('录音错误:', error);
      Toast.show({
        type: 'error',
        text1: '录音错误',
        text2: error.message,
      });
    },
  });

  // 🚀 新增：胶囊协调系统集成
  const {
    isSupported: floatingWindowSupported,
    isRecordingActive: isFloatingRecordingActive,
    startRecording: startFloatingRecording,
    stopRecording: stopFloatingRecording,
    updateRecording: updateFloatingRecording,
  } = useFloatingWindow();

  // TODO: 重新实现音频播放功能（暂时禁用以解决依赖问题）
  const playbackStatus = {
    positionMillis: 0,
    durationMillis: 0,
    isPlaying: false,
  };
  const playbackSpeed = 1.0;
  const playPause = () => {};
  const changeSpeed = (_speed: number) => {};
  const forward = () => {};
  const rewind = () => {};
  const unload = () => {};

  const {
    initializeLocation,
    getCachedLocation,
    formatLocationForDisplay,
    isInitialized,
  } = useLocation();

  const [selectedRecording, setSelectedRecording] = useState<Recording | null>(
    null
  );
  const [isPlayerModalVisible, setIsPlayerModalVisible] = useState(false);
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [newName, setNewName] = useState('');
  const [isTranscriptionModalVisible, setIsTranscriptionModalVisible] =
    useState(false);
  const [
    selectedRecordingForTranscription,
    setSelectedRecordingForTranscription,
  ] = useState<Recording | null>(null);

  const [refreshing, setRefreshing] = useState(false);
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const pollingIntervals = useRef(new Map<string, NodeJS.Timeout>());
  const swipeListRef = useRef<SwipeListView<Recording>>(null);
  // 使用全局录音状态
  const {
    setIsRecordingCapsuleVisible,
    setIsRecording: setGlobalIsRecording,
    setIsPaused: setGlobalIsPaused,
    setRecordingDuration: setGlobalRecordingDuration,
    setOnCapsulePress,
    setOnCapsuleStop,
  } = useRecordingContext();

  // 录音弹窗相关状态
  const [isRecordingModalVisible, setIsRecordingModalVisible] = useState(false);
  // const [isPaused, setIsPaused] = useState(false); // 🔧 移除本地状态，使用Hook中的状态
  const [isNoteModalVisible, setIsNoteModalVisible] = useState(false);
  const [tempNoteData, setTempNoteData] = useState<NoteData[]>([]);

  // 🔧 使用Hook中的暂停状态
  const isPaused = hookIsPaused;

  /**
   * 🔧 修复无限循环：同步录音状态到全局Context
   * 移除函数依赖项，避免每次渲染都触发useEffect
   */
  useEffect(() => {
    setGlobalIsRecording(isRecording);
  }, [isRecording]);

  useEffect(() => {
    setGlobalRecordingDuration(recordingDuration);
  }, [recordingDuration]);

  useEffect(() => {
    setGlobalIsPaused(hookIsPaused);
  }, [hookIsPaused]);

  // ❌ 移除自动转录进度模拟 - 改为手动触发转录
  // 现在转录由用户点击"转文字"按钮手动触发

  const processingRecordings = useMemo(
    () =>
      recordings.filter(
        (recording) => recording.transcriptionStatus === 'processing'
      ),
    [recordings]
  );

  // 🎯 监听转录状态为processing的录音，启动轮询
  useEffect(() => {
    if (processingRecordings.length === 0) {
      return;
    }

    processingRecordings.forEach((recording) => {
      // 避免重复创建轮询
      if (pollingIntervals.current.has(recording.id)) {
        return;
      }

      const pollRecordingStatus = async () => {
        try {
          // console.log(`🔄 轮询查询录音转录状态: ${recording.id}`);
          const response = await lcdpService.queryRecordingInfo({
            app_file_id: recording.id,
          });

          if (response.resultCode === '0' && response.resultObject) {
            const {
              transcription_status,
              transcription_progress,
              chapter_overview,
              full_summary,
              meeting_minutes,
              knowledge_id,
            } = response.resultObject?.result || {};

            // console.log(`📊 录音 ${recording.id} 转录状态:`, {
            //   status: transcription_status,
            //   progress: transcription_progress,
            // });

            if (transcription_status !== 'pending') {
              updateRecording(recording.id, {
                transcriptionStatus: transcription_status,
                transcriptionProgress: transcription_progress || '0%',
                chapterOverview: chapter_overview,
                fullSummary: full_summary,
                meetingMinutes: meeting_minutes,
                knowledgeId: knowledge_id,
              });
              const res = await lcdpService.queryRecordingRecord({
                app_file_id: recording.id,
              });
              if (res.resultCode === '0' && res.resultObject) {
                updateRecording(recording.id, {
                  transcriptionData: (res.resultObject.resultObject || []).map(
                    (c: any) => ({
                      begin: c.begin_time, // 开始时间(秒)
                      end: c.end_time, // 结束时间(秒)
                      beginTime:
                        (c.begin_time && c.begin_time.split('.')[0]) || '', // 格式化开始时间
                      endTime: c.end_time, // 格式化结束时间
                      speaker: c.speaker, // 说话人ID
                      text: c.text, // 识别文本
                      speakerName: `讲话人${parseInt(c.speaker.replace('说话人', '')) + 1}`,
                    })
                  ),
                });
              }
            }
            // 更新转录进度

            // 如果状态为完成或失败，停止轮询
            if (
              transcription_status === 'completed' ||
              transcription_status === 'failed'
            ) {
              console.log(
                `✅ 录音 ${recording.id} 转录${transcription_status === 'completed' ? '完成' : '失败'}，停止轮询`
              );
              const interval = pollingIntervals.current.get(recording.id);
              if (interval) {
                clearInterval(interval);
                pollingIntervals.current.delete(recording.id);
              }
            }
          }
        } catch (error) {
          console.error(`❌ 查询录音 ${recording.id} 转录状态失败:`, error);
          // 出错时也停止轮询，避免无限重试
          const interval = pollingIntervals.current.get(recording.id);
          if (interval) {
            clearInterval(interval);
            pollingIntervals.current.delete(recording.id);
          }
        }
      };

      // 立即执行一次查询
      pollRecordingStatus();

      // 每3秒轮询一次
      const interval = setInterval(pollRecordingStatus, 3000);
      pollingIntervals.current.set(recording.id, interval);
    });

    // 清理函数
    return () => {
      pollingIntervals.current.forEach((interval) => {
        clearInterval(interval);
      });
      pollingIntervals.current.clear();
    };
  }, [processingRecordings.length, updateRecording, pollingIntervals]);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    setTimeout(() => {
      // 你的数据获取逻辑，例如：
      // useRecordingStore.getState().fetchRecordings();
      setRefreshing(false);
      Toast.show({
        type: 'success',
        text1: '刷新成功',
      });
    }, 1000);
  }, []);

  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();

  useEffect(() => {
    // 检查录音权限
    if (!hasPermission) {
      requestPermissions();
    }
  }, [hasPermission, requestPermissions]);

  // 应用启动时初始化位置信息
  useEffect(() => {
    if (!isInitialized) {
      initializeLocation();
    }
  }, [isInitialized, initializeLocation]);

  const handleStartRecording = async () => {
    setTempNoteData([]);
    if (isRecording) {
      console.log('🛑 停止录音和转写...');
      await stopTranscription();
      await stopRecording();
      setIsRecordingModalVisible(false);
      setIsRecordingCapsuleVisible(false);
      // setIsPaused(false); // 🔧 移除本地状态设置，Hook会自动处理
      setGlobalIsPaused(false);
      setCurrentAudioData(undefined); // 清除音频数据
      console.log('✅ 录音和转写已停止');
    } else {
      console.log('🚀 启动转写和录音...');
      // 先启动转写服务
      await startTranscription();
      console.log('✅ 转写服务已启动');

      // 打开录音弹窗并开始录音
      setIsRecordingModalVisible(true);
      setIsRecordingCapsuleVisible(true); // 显示RN胶囊
      // setIsPaused(false); // 🔧 移除本地状态设置，Hook会自动处理
      setGlobalIsPaused(false);

      // 🚀 同时启动音频录制和胶囊协调系统
      await startRecording();
      if (floatingWindowSupported) {
        await startFloatingRecording();
        console.log('✅ 胶囊协调系统已启动');
      }
      console.log('✅ 录音已启动，PCM数据将直接无损传送给讯飞');
    }
  };

  // 录音弹窗控制函数
  const handleCloseRecordingModal = async () => {
    setIsRecordingModalVisible(false);
    setIsRecordingCapsuleVisible(false);
    if (isRecording) {
      await stopTranscription();
      await stopRecording();

      // 🚀 同时停止胶囊协调系统
      if (floatingWindowSupported && isFloatingRecordingActive) {
        await stopFloatingRecording();
        console.log('✅ 胶囊协调系统已停止');
      }
    }
    // 清除转文字内容
    clearTranscription();
    // setIsPaused(false); // 🔧 移除本地状态设置，Hook会自动处理
    setGlobalIsPaused(false);
    setCurrentAudioData(undefined);
  };

  // 收起到胶囊状态
  const handleMinimizeRecording = () => {
    setIsRecordingModalVisible(false);
    setIsRecordingCapsuleVisible(true);
  };

  // 从胶囊恢复到弹窗
  const handleExpandFromCapsule = React.useCallback(() => {
    setIsRecordingCapsuleVisible(false);
    setIsRecordingModalVisible(true);
  }, [setIsRecordingCapsuleVisible]);

  // 停止录音
  const handleStopRecording = React.useCallback(async () => {
    if (isRecording) {
      try {
        // 🚫 首先停止转写服务（断开讯飞连接）
        await stopTranscription();
        console.log('✅ 转写服务已停止');

        // 🎵 停止录音并获取录音数据
        const recordingData = await stopRecording();

        // 🚀 同时停止胶囊协调系统
        if (floatingWindowSupported && isFloatingRecordingActive) {
          await stopFloatingRecording();
          console.log('✅ 胶囊协调系统已停止');
        }

        if (recordingData && recordingData.uri) {
          // 📁 获取缓存的位置信息
          let locationString: string | undefined;
          try {
            const locationData = getCachedLocation();
            if (locationData) {
              const formattedLocation = formatLocationForDisplay(locationData);
              locationString = formattedLocation || undefined;
            }
          } catch (locationError) {
            console.warn('获取位置信息失败:', locationError);
          }

          // 📝 创建录音记录并添加到列表
          const newRecording: Recording = {
            id: Date.now().toString(), // 简单的ID生成
            title: `录音_${new Date().toLocaleString('zh-CN')}`,
            filePath: recordingData.uri,
            duration: recordingData.duration || 0,
            size: recordingData.size || 0,
            createdAt: new Date(),
            updatedAt: new Date(),
            userId: 'current-user',
            type: 'recording',
            transcriptionStatus: 'pending', // 等待用户手动触发转录
            transcriptionProgress: 0,
            location: locationString,
            transcription: transcriptionText,
            noteData: tempNoteData,
          };

          // ✅ 添加到录音列表（包含异步上传）
          await addRecording(newRecording);

          lcdpService.addRecordMark({
            appFileId: newRecording.id,
            recordMarks: tempNoteData,
          });

          setTempNoteData([]);

          console.log('✅ 录音已保存到列表:', {
            文件路径: recordingData.uri,
            时长: `${(recordingData.duration || 0).toFixed(1)}秒`,
            大小: `${((recordingData.size || 0) / 1024).toFixed(1)}KB`,
            位置: locationString || '未知',
          });

          Toast.show({
            type: 'success',
            text1: '录音完成',
            text2: `录音已保存，时长 ${Math.floor(
              (recordingData.duration || 0) / 60
            )}:${((recordingData.duration || 0) % 60)
              .toFixed(0)
              .padStart(2, '0')}`,
          });
        } else {
          console.warn('⚠️ 录音数据无效，未添加到列表');
          Toast.show({
            type: 'error',
            text1: '录音保存失败',
            text2: '录音文件可能未正确保存',
          });
        }
      } catch (error) {
        console.error('❌ 停止录音失败:', error);
        Toast.show({
          type: 'error',
          text1: '停止录音失败',
          text2: '请重试录音功能',
        });
        setTempNoteData([]);
      }
    }

    // 清除转文字内容
    clearTranscription();

    // 🧹 清理UI状态
    setIsRecordingModalVisible(false);
    setIsRecordingCapsuleVisible(false);
    // setIsPaused(false); // 🔧 移除本地状态设置，Hook会自动处理
    setGlobalIsPaused(false);
    setCurrentAudioData(undefined);
  }, [
    isRecording,
    stopRecording,
    stopTranscription,
    clearTranscription,
    addRecording,
    getCachedLocation,
    formatLocationForDisplay,
    setIsRecordingCapsuleVisible,
    setGlobalIsPaused,
  ]);

  // 设置胶囊回调函数
  React.useEffect(() => {
    setOnCapsulePress(handleExpandFromCapsule);
    setOnCapsuleStop(handleStopRecording);
  }, [
    handleExpandFromCapsule,
    handleStopRecording,
    setOnCapsulePress,
    setOnCapsuleStop,
  ]);

  // 🚀 同步录音状态到胶囊协调系统
  React.useEffect(() => {
    if (floatingWindowSupported && isFloatingRecordingActive) {
      updateFloatingRecording();
    }
  }, [
    floatingWindowSupported,
    isFloatingRecordingActive,
    updateFloatingRecording,
    recordingDuration,
    isRecording,
    hookIsPaused,
  ]);

  const handlePauseRecording = async () => {
    const newPausedState = !isPaused;

    try {
      if (newPausedState) {
        // 暂停录音
        const success = await pauseRecording();
        if (success) {
          console.log('⏸️ 录音已暂停，计时器已停止');
          setGlobalIsPaused(true);
        } else {
          throw new Error('暂停录音失败');
        }
      } else {
        // 恢复录音
        const success = await resumeRecording();
        if (success) {
          console.log('▶️ 录音已恢复，计时器已启动');
          setGlobalIsPaused(false);
        } else {
          throw new Error('恢复录音失败');
        }
      }
      // setIsPaused(newPausedState); // 🔧 移除本地状态设置，Hook会自动处理
    } catch (error) {
      console.error('❌ 暂停/恢复录音失败:', error);
      Toast.show({
        type: 'error',
        text1: '操作失败',
        text2: newPausedState ? '暂停录音失败' : '恢复录音失败',
      });
    }
  };

  const handleImportAudio = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'audio/*',
        copyToCacheDirectory: false, // 不复制到缓存目录
      });

      if (!result.canceled && result.assets.length > 0) {
        const asset = result.assets[0];

        // 📁 统一存储到recordings目录
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const fileExtension = asset.name?.split('.').pop() || 'unknown';
        const fileName = `imported_${timestamp}.${fileExtension}`;
        const targetPath = `${FileSystem.documentDirectory}recordings/`;

        // 确保目录存在
        await FileSystem.makeDirectoryAsync(targetPath, {
          intermediates: true,
        });
        const finalPath = `${targetPath}${fileName}`;

        // 🔄 复制文件到统一存储位置
        await FileSystem.copyAsync({
          from: asset.uri,
          to: finalPath,
        });

        // 📊 获取文件信息
        const fileInfo = await FileSystem.getInfoAsync(finalPath);
        const actualSize =
          fileInfo.exists && !fileInfo.isDirectory
            ? (fileInfo as any).size || asset.size || 0
            : asset.size || 0;

        // 🎵 获取音频文件实际时长
        let estimatedDuration = 0;
        try {
          const { sound } = await Audio.Sound.createAsync({ uri: finalPath });
          const status = await sound.getStatusAsync();

          if (status.isLoaded && status.durationMillis) {
            estimatedDuration = Math.round(status.durationMillis / 1000); // 转换为秒
            console.log('✅ 获取导入音频时长:', estimatedDuration, '秒');
          }

          // 清理临时音频对象
          await sound.unloadAsync();
        } catch (durationError) {
          console.warn('⚠️ 获取音频时长失败，使用默认值:', durationError);
          estimatedDuration = 0;
        }

        // 获取缓存的位置信息
        let locationString: string | undefined;
        try {
          const locationData = getCachedLocation();
          if (locationData) {
            const formattedLocation = formatLocationForDisplay(locationData);
            locationString = formattedLocation || undefined;
          }
        } catch (locationError) {
          console.warn(
            'Failed to get cached location for imported audio:',
            locationError
          );
          // 位置获取失败不影响音频导入
        }

        const recording: Recording = {
          id: Date.now().toString(),
          title: asset.name || '导入的音频',
          filePath: finalPath, // ✅ 使用统一的存储路径
          duration: estimatedDuration, // ✅ 已获取实际时长
          size: actualSize,
          createdAt: new Date(),
          updatedAt: new Date(),
          userId: 'current-user',
          type: 'import',
          transcriptionStatus: 'pending', // 导入的音频等待用户手动转录
          transcriptionProgress: 0,
          location: locationString, // 使用真实位置信息，如果获取失败则为 undefined
          transcription: transcriptionText,
        };

        // ✅ 添加录音到列表（包含异步上传）
        addRecording(recording);

        console.log('✅ 音频文件已导入到统一存储位置:', {
          原始文件: asset.name,
          存储路径: finalPath,
          文件大小: `${(actualSize / 1024).toFixed(1)}KB`,
        });

        Toast.show({
          type: 'success',
          text1: '导入成功',
          text2: `音频文件已导入到录音列表`,
        });
      }
    } catch (error) {
      console.error('Import audio error:', error);
      Toast.show({
        type: 'error',
        text1: '导入失败',
        text2: '无法导入音频文件',
      });
    }
  };

  // const handlePlayRecording = async (recording: Recording) => {
  //   setSelectedRecording(recording);
  //   setIsPlayerModalVisible(true);
  //   await loadAudio(recording.filePath);
  // };

  const handleRenameRecording = (recording: Recording) => {
    setSelectedRecording(recording);
    setNewName(recording.title);
    setIsRenameModalVisible(true);
  };

  const handleDeleteRecording = (recording: Recording) => {
    DialogUtil.alert('删除录音', '确定要删除这个录音吗？', () =>
      deleteRecording(recording.id)
    );
  };

  const handleSaveRename = () => {
    if (selectedRecording && newName.trim()) {
      updateRecording(selectedRecording.id, { title: newName.trim() });
      setIsRenameModalVisible(false);
      setSelectedRecording(null);
      setNewName('');
      // 关闭所有打开的swipe行
      if (swipeListRef.current) {
        swipeListRef.current.closeAllOpenRows();
      }
      Toast.show({
        type: 'success',
        text1: '修改成功',
      });
    }
  };

  const handleTranscription = async (recording: Recording) => {
    // 设置状态为处理中，触发轮询机制
    updateRecording(recording.id, {
      transcriptionStatus: 'processing',
    });

    const res = await lcdpService.transcribeAudioToText({
      minSpeakers: '1',
      maxSpeakers: '10',
      app_file_id: recording.id,
      duration: recording.duration,
    });
    if (res.resultCode === '0') {
      Toast.show({
        text1: '转写成功',
      });
    }
  };

  // 渲染转录状态
  const renderTranscriptionStatus = (recording: Recording) => {
    switch (recording.transcriptionStatus) {
      case 'processing': {
        const progress = recording.transcriptionProgress || '0%';
        return (
          <View style={styles.transcriptionProgressContainer}>
            <Text style={styles.transcriptionProgressText}>
              转写中 {progress}
            </Text>
          </View>
        );
      }
      case 'completed':
        return (
          <TouchableOpacity
            style={styles.statusButton}
            onPress={() =>
              navigation.navigate('RecordingDetail', {
                recordingId: recording.id,
                showAIFeatures: true,
              })
            }
          >
            <Ionicons
              name="checkmark-circle"
              size={16}
              color="#00C853"
              style={styles.statusButtonIcon}
            />
            <Text style={styles.statusButtonText}>已完成</Text>
          </TouchableOpacity>
        );
      case 'failed':
        return (
          <TouchableOpacity
            style={styles.statusButton}
            onPress={() => handleTranscription(recording)}
          >
            <Ionicons
              name="close-circle"
              size={16}
              color="#F44336"
              style={styles.statusButtonIcon}
            />
            <Text style={[styles.statusButtonText, { color: '#F44336' }]}>
              转录失败
            </Text>
          </TouchableOpacity>
        );
      case 'pending':
      default:
        return (
          <TouchableOpacity
            style={styles.transcriptionStatusContainer}
            onPress={() => handleTranscription(recording)}
            activeOpacity={0.7}
          >
            <Image
              source={require('../../../assets/images/icons/vuesax／bold／arrow-right.png')}
              style={styles.transcriptionArrowIcon}
            />
            <Text style={styles.transcriptionStatusText}>转文字</Text>
          </TouchableOpacity>
        );
    }
  };

  const handleRecordingPress = (recording: Recording) => {
    console.log(recording);

    // 根据录音状态决定导航行为
    if (recording.transcriptionStatus === 'completed') {
      // 已完成状态：直接进入AI功能页面
      navigation.navigate('RecordingDetail', {
        recordingId: recording.id,
        showAIFeatures: true,
      });
    } else {
      // 其他状态：进入转录文本页面
      navigation.navigate('RecordingDetail', { recordingId: recording.id });
    }
  };

  const renderRecordingItem = (data: { item: Recording }) => (
    <View key={data.item.id} style={styles.recordingItemContainer}>
      <TouchableOpacity
        style={styles.recordingItemInfoContainer}
        onPress={() => handleRecordingPress(data.item)}
      >
        <Text
          numberOfLines={1}
          ellipsizeMode="tail"
          style={styles.recordingItemTitle}
        >
          {data.item.title}
        </Text>
        <View style={styles.recordingItemDetails}>
          <Ionicons
            name="time-outline"
            size={15}
            color={colors.text.secondary}
            style={{ marginRight: 4 }}
          />
          <Text style={styles.recordingItemDesc}>
            {formatDuration(data.item.duration)}
          </Text>
          <Ionicons
            name="calendar-outline"
            size={15}
            color={colors.text.secondary}
            style={{ marginRight: 4 }}
          />
          <Text style={styles.recordingItemDesc}>
            {data.item.createdAt ? formatDate(data.item.createdAt) : '未知日期'}
            ·{data.item.type === 'import' ? '导入' : 'APP'}
          </Text>
          {data.item.location && (
            <>
              <Ionicons
                name="location-outline"
                size={14}
                color={colors.text.secondary}
                style={{ marginRight: 4 }}
              />
              <Text
                style={styles.locationText}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {data.item.location}
              </Text>
            </>
          )}
        </View>
      </TouchableOpacity>
      {/* 转录状态 */}
      <View style={styles.recordingItemActionsContainer}>
        {/* 根据上传状态优先显示 */}
        {data.item.uploadStatus === 'uploading' ? (
          <View style={styles.transcriptionProgressContainer}>
            <Text style={styles.transcriptionProgressText}>上传中</Text>
          </View>
        ) : data.item.uploadStatus === 'failed' ||
          data.item.uploadStatus === 'pending' ? (
          <TouchableOpacity
            style={styles.transcriptionStatusContainer}
            onPress={() => retryUpload(data.item.id)}
            activeOpacity={0.7}
          >
            <Ionicons
              name="cloud-upload-outline"
              size={16}
              color="#FF9800"
              style={{ marginRight: 4 }}
            />
            <Text
              style={[styles.transcriptionStatusText, { color: '#FF9800' }]}
            >
              待上传
            </Text>
          </TouchableOpacity>
        ) : (
          renderTranscriptionStatus(data.item)
        )}
      </View>
    </View>
  );

  const renderHiddenItem = (data: { item: Recording }) => (
    <View style={styles.rowBack}>
      <TouchableOpacity
        style={[styles.backRightBtn, styles.backRightBtnLeft]}
        onPress={() => handleRenameRecording(data.item)}
      >
        <View style={styles.editBtn}>
          <Image
            source={require('../../../assets/images/icons/icon-edit.png')}
            style={styles.ctrlBtn}
          />
        </View>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.backRightBtn, styles.backRightBtnRight]}
        onPress={() => handleDeleteRecording(data.item)}
      >
        <View style={styles.deleteBtn}>
          <Image
            source={require('../../../assets/images/icons/icon-trash.png')}
            style={styles.ctrlBtn}
          />
        </View>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.recordingSection}>
        {/* 按钮区 */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[
              styles.recordingActionButton,
              {
                width: buttonDimensions.recordingWidth,
                height: buttonDimensions.height,
              },
            ]}
            onPress={handleStartRecording}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={[
                '#3053ED', // #3053ED 0%
                'rgba(82,121,255,0.8)', // rgba(82,121,255,0.8) 50%
                '#3EDDB6', // #3EDDB6 100%
              ]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.gradientButton}
            >
              {isRecording ? (
                <Ionicons name="stop-sharp" size={24} color="#fff" />
              ) : (
                <Image
                  source={require('../../../assets/images/icons/microphone.png')}
                  style={styles.microphoneIcon}
                />
              )}
              <Text style={styles.recordingActionButtonText}>
                {isRecording ? '停止录音' : '开始录音'}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
          <Button
            title="导入音频"
            style={[
              styles.importAudioButton,
              {
                width: buttonDimensions.importWidth,
                height: buttonDimensions.height,
              },
            ]}
            onPress={handleImportAudio}
            icon={
              <Image
                source={require('../../../assets/images/icons/import.png')}
                style={styles.importAudioButtonIcon}
              />
            }
          />
        </View>
      </View>

      <View style={styles.recordingContainer}>
        {/* Tab栏 */}
        <View style={styles.tabBarContainer}>
          <Text style={styles.tabBarText}>录音</Text>
          <View style={styles.tarBarIcon}>
            <TouchableOpacity onPress={() => setIsSearchVisible(true)}>
              <Image
                source={require('../../../assets/images/icons/icon-search.png')}
                style={styles.extraBtn}
              />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => navigation.navigate('FileBatchManage')}
            >
              <Image
                source={require('../../../assets/images/icons/icon-filter.png')}
                style={styles.extraBtn}
              />
            </TouchableOpacity>
          </View>
        </View>
        {/* 录音列表 */}
        <View style={[styles.listContainer, { paddingBottom: bottomPadding }]}>
          {recordings.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <Ionicons
                name="mic-outline"
                size={48}
                color={colors.text.secondary}
              />
              <Text style={styles.emptyStateTitle}>暂无录音文件</Text>
              <Text style={styles.emptyStateSubtitle}>
                点击上方按钮开始录音或导入音频文件
              </Text>
            </View>
          ) : (
            <SwipeListView
              ref={swipeListRef}
              data={recordings}
              renderItem={renderRecordingItem}
              renderHiddenItem={renderHiddenItem}
              rightOpenValue={-120}
              previewRowKey={'0'}
              previewOpenValue={-40}
              previewOpenDelay={3000}
              style={styles.recordingListContainer}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              onRefresh={onRefresh}
              refreshing={refreshing}
              disableRightSwipe
              removeClippedSubviews={true}
              maxToRenderPerBatch={10}
              windowSize={10}
              initialNumToRender={5}
              getItemLayout={undefined}
              closeOnRowPress={true}
            />
          )}
        </View>
      </View>

      <RecordingSearchModal
        visible={isSearchVisible}
        onClose={() => setIsSearchVisible(false)}
        recordings={recordings}
        onRecordingPress={handleRecordingPress}
      />

      {/* 音频播放器弹窗 */}
      <Modal
        visible={isPlayerModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => {
          unload();
          setIsPlayerModalVisible(false);
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.playerModal}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{selectedRecording?.title}</Text>
              <TouchableOpacity
                onPress={() => {
                  unload();
                  setIsPlayerModalVisible(false);
                }}
              >
                <Ionicons name="close" size={24} color={colors.text.primary} />
              </TouchableOpacity>
            </View>

            <View style={styles.playerContent}>
              {/* 播放进度 */}
              <View style={styles.progressContainer}>
                <Text style={styles.timeText}>
                  {formatDuration(
                    Math.floor(playbackStatus.positionMillis / 1000)
                  )}
                </Text>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      {
                        width:
                          playbackStatus.durationMillis > 0
                            ? `${
                                (playbackStatus.positionMillis /
                                  playbackStatus.durationMillis) *
                                100
                              }%`
                            : '0%',
                      },
                    ]}
                  />
                </View>
                <Text style={styles.timeText}>
                  {formatDuration(
                    Math.floor(playbackStatus.durationMillis / 1000)
                  )}
                </Text>
              </View>

              {/* 播放控制 */}
              <View style={styles.playerControls}>
                <TouchableOpacity
                  onPress={() => rewind()}
                  style={styles.controlButton}
                >
                  <Ionicons
                    name="play-back"
                    size={24}
                    color={colors.text.primary}
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={playPause}
                  style={[styles.controlButton, styles.playButton]}
                >
                  <Ionicons
                    name={playbackStatus.isPlaying ? 'pause' : 'play'}
                    size={32}
                    color={colors.background.primary}
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => forward()}
                  style={styles.controlButton}
                >
                  <Ionicons
                    name="play-forward"
                    size={24}
                    color={colors.text.primary}
                  />
                </TouchableOpacity>
              </View>

              {/* 播放速度 */}
              <View style={styles.speedControl}>
                <Text style={styles.speedLabel}>播放速度</Text>
                <View style={styles.speedButtons}>
                  {[0.5, 1.0, 1.5, 2.0].map((speed) => (
                    <TouchableOpacity
                      key={speed}
                      style={[
                        styles.speedButton,
                        playbackSpeed === speed && styles.speedButtonActive,
                      ]}
                      onPress={() => changeSpeed(speed)}
                    >
                      <Text
                        style={[
                          styles.speedButtonText,
                          playbackSpeed === speed &&
                            styles.speedButtonTextActive,
                        ]}
                      >
                        {speed}x
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* 重命名弹窗 */}
      <Dialog
        visible={isRenameModalVisible}
        onConfirm={handleSaveRename}
        onClose={() => setIsRenameModalVisible(false)}
        title="重命名"
      >
        <TextInput
          style={styles.renameInput}
          value={newName}
          onChangeText={setNewName}
          placeholder="请输入新的录音名称"
          autoFocus
        />
      </Dialog>

      {/* 转写和AI分析弹窗 */}
      <TranscriptionModal
        visible={isTranscriptionModalVisible}
        recording={selectedRecordingForTranscription}
        onClose={() => {
          setIsTranscriptionModalVisible(false);
          setSelectedRecordingForTranscription(null);
        }}
      />

      {/* 录音弹窗 - 传递真实音频数据 */}
      <RecordingModal
        visible={isRecordingModalVisible}
        onClose={handleCloseRecordingModal}
        onMinimize={handleMinimizeRecording}
        onPause={handlePauseRecording}
        onStop={handleStopRecording}
        isRecording={isRecording}
        isPaused={isPaused}
        duration={recordingDuration}
        location={(() => {
          const cachedLocation = getCachedLocation();
          if (cachedLocation) {
            const formatted = formatLocationForDisplay(cachedLocation);
            return formatted || undefined;
          }
          return undefined;
        })()}
        transcriptionConfig={
          isTranscriptionConfigValid() ? XUNFEI_CONFIG : undefined
        }
        audioData={currentAudioData} // 传递真实音频数据
        // 🔧 传递转写状态，避免RecordingModal创建多个连接
        isTranscriptionConnected={transcriptionConnected}
        transcriptionText={transcriptionText}
        partialText={partialText}
        noteData={tempNoteData}
        onNote={() => setIsNoteModalVisible(true)}
      />

      {/* 笔记对话框 */}
      <NoteModal
        noteData={tempNoteData}
        visible={isNoteModalVisible}
        onClose={() => setIsNoteModalVisible(false)}
        onSave={(note) => {
          if (note.trim()) {
            setTempNoteData((pre) =>
              pre.concat({
                markTime: formatDuration(recordingDuration),
                note: note,
              })
            );
          }
        }}
        initialText="" // 记一下输入框默认为空，不自动填入转写内容
      />

      {/* 录音胶囊现在是全局的，在App.tsx中渲染 */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.bg,
    paddingTop: spacing.md,
  },

  recordingSection: {
    paddingHorizontal: spacing.md, // 减少外层padding，因为actionButtons内部已有padding
    paddingBottom: spacing.lg,
    backgroundColor: colors.background.bg,
  },

  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 16, // 确保两侧有边距
  },

  recordingActionButton: {
    borderRadius: 20,
    overflow: 'hidden',
    paddingVertical: 0,
    paddingHorizontal: 0,
    // box-shadow: 0px 1px 4px 0px rgba(100,103,122,0.06)
    shadowColor: 'rgba(100, 103, 122, 0.06)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 4, // Android 阴影
    // 宽度和高度由组件动态设置
  },

  gradientButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    borderRadius: 20,
    paddingHorizontal: 16, // 添加足够的内边距，参考Button组件
    // CSS: background: linear-gradient(...), #FFFFFF;
    // 白色作为后备背景色
    backgroundColor: '#FFFFFF',
  },

  recordingActionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: typography.fontWeight.semibold,
    marginLeft: 8, // 保持与图标的间距
    textAlign: 'center',
  },

  microphoneIcon: {
    width: 24,
    height: 24,
    tintColor: '#fff', // 设置图标颜色为白色
  },

  importAudioButton: {
    backgroundColor: '#FFFFFF', // background: #FFFFFF
    borderRadius: 20, // border-radius: 20px
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 0,
    paddingHorizontal: 12, // 添加内边距，防止文字贴边
    // box-shadow: 0px 1px 4px 0px rgba(100,103,122,0.06)
    shadowColor: 'rgba(100, 103, 122, 0.06)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 4, // Android 阴影
    // 宽度和高度由组件动态设置
  },

  importAudioButtonIcon: {
    width: 20,
    height: 20,
    marginRight: 6, // 减少间距以适应较小的按钮宽度
    tintColor: '#00C853', // 设置图标颜色为绿色
  },

  importAudioButtonText: {
    color: '#333',
    fontSize: 14, // 减小字体以适应较小的按钮
    fontWeight: typography.fontWeight.semibold,
  },

  recordingContainer: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: 12,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    overflow: 'hidden',
    shadowColor: '#64677A',
    shadowOffset: { width: 0, height: -1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 3,
  },

  tabBarContainer: {
    flexDirection: 'row',
    marginLeft: 24,
    marginBottom: 8,
    justifyContent: 'space-between',
    alignItems: 'center',
    marginRight: 24,
  },

  tabBarText: {
    fontSize: 15,
    fontWeight: typography.fontWeight.bold,
    fontFamily: typography.fontFamily,
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
    paddingBottom: 12,
  },

  tarBarIcon: {
    gap: spacing.md,
    flexDirection: 'row',
  },

  listContainer: {
    paddingHorizontal: 0,
    flex: 1,
    // paddingBottom 现在通过动态计算设置，见组件内的 bottomPadding
  },

  emptyStateContainer: {
    alignItems: 'center',
    paddingVertical: 64,
  },

  emptyStateTitle: {
    fontSize: 18,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginTop: 16,
    marginBottom: 4,
  },

  emptyStateSubtitle: {
    fontSize: 14,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
  },

  recordingListContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20, // 为列表底部添加额外间距，确保最后一项不被Tab栏遮挡
  },

  recordingItemContainer: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingRight: 8,
  },

  rowBack: {
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  backRightBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 60,
  },
  backRightBtnLeft: {
    // backgroundColor: colors.primary,
    right: 55,
  },
  backRightBtnRight: {
    right: 0,
  },

  editBtn: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
    backgroundColor: '#fff',
  },

  deleteBtn: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
    backgroundColor: colors.error,
  },

  recordingItemIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },

  recordingItemInfoContainer: {
    flex: 1,
  },

  recordingItemTitle: {
    fontSize: 15,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: 2,
    maxWidth: 200,
  },

  recordingItemDetails: {
    alignItems: 'center',
    flexDirection: 'row',
    color: colors.text.secondary,
    overflow: 'hidden',
    marginTop: 4,
  },

  recordingItemDesc: {
    fontSize: 10,
    color: colors.text.secondary,
    marginRight: 8,
  },

  recordingItemActionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 10,
  },

  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },

  statusButtonIcon: {
    marginRight: 4,
  },

  statusButtonText: {
    color: '#00C853',
    fontSize: 13,
  },

  // 转录处理中状态样式
  processingIndicator: {
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },

  processingText: {
    fontSize: 12,
    color: '#1976D2',
    fontWeight: typography.fontWeight.medium,
  },

  // 位置信息样式
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },

  locationText: {
    fontSize: 12,
    color: colors.text.secondary,
    flex: 1,
  },

  // 转录状态容器样式（灰色圆角背景，和已完成状态一样）
  transcriptionStatusContainer: {
    width: 83,
    height: 32,
    backgroundColor: '#F2F3F5',
    borderRadius: 999,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
  },

  // 转录箭头图标样式（PNG切图，不需要设置颜色）
  transcriptionArrowIcon: {
    width: 17,
    height: 17,
    marginRight: 4,
    // 不设置 tintColor，保持PNG原始颜色（蓝色背景+白色箭头）
  },

  // 转录状态文字样式（按照Android TextView样式）
  transcriptionStatusText: {
    // width: 47, // android:layout_width="47dp"
    // height: 22, // android:layout_height="22dp"
    color: colors.text.primary, // android:textColor="#ff2a2b33"
    fontSize: 13,
    textAlign: 'center',
  },

  // 🎯 转录进度容器样式
  transcriptionProgressContainer: {
    width: 90, // 与转文字按钮保持相同宽度
    height: 32,
    backgroundColor: '#E3F2FD', // 蓝色背景表示进行中
    borderRadius: 999,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
  },

  // 🎯 转录进度文字样式
  transcriptionProgressText: {
    color: '#1976D2', // 蓝色文字
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },

  editButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F5F5F5',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 4,
  },

  deleteButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#FF5252',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 4,
  },

  actionButtonIcon: {
    // This can be an empty object if no specific style is needed,
    // or you can add common styles for all action icons.
  },

  // 功能特色区域
  featuresSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    backgroundColor: '#FAFAFA',
    marginBottom: 100, // 给底部导航留出空间
  },

  featuresList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
  },

  featureItem: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },

  featureIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },

  featureIconText: {
    fontSize: 16,
    fontWeight: '600',
  },

  featureText: {
    fontSize: 14,
    color: colors.text.primary,
    fontWeight: '500',
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
  },

  playerModal: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingBottom: 40,
    maxHeight: '80%',
  },

  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 20,
  },

  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginRight: spacing.md,
  },

  playerContent: {
    paddingHorizontal: 20,
  },

  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 30,
  },

  timeText: {
    fontSize: 12,
    color: colors.text.secondary,
    minWidth: 45,
  },

  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    marginHorizontal: 12,
    overflow: 'hidden',
  },

  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },

  playerControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },

  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 12,
  },

  playButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.primary,
    marginHorizontal: 20,
  },

  speedControl: {
    marginTop: 20,
  },

  speedLabel: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 12,
    textAlign: 'center',
  },

  speedButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
  },

  speedButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 16,
    backgroundColor: '#F5F5F5',
  },

  speedButtonActive: {
    backgroundColor: colors.primary,
  },

  speedButtonText: {
    fontSize: 14,
    color: colors.text.primary,
  },

  speedButtonTextActive: {
    color: colors.background.primary,
    fontWeight: '500',
  },

  // Rename modal styles
  renameModal: {
    backgroundColor: colors.background.primary,
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 20,
  },

  renameInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: colors.text.primary,
    marginVertical: 16,
  },

  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: spacing.md,
    gap: spacing.md,
  },

  modalButton: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: spacing.lg,
    flex: 1,
  },

  modalButtonCancel: {
    backgroundColor: '#F5F5F5',
  },

  modalButtonConfirm: {
    backgroundColor: colors.primary,
  },

  modalButtonText: {
    fontSize: 16,
    color: colors.text.primary,
  },

  modalButtonTextConfirm: {
    color: colors.background.primary,
    fontWeight: '500',
  },

  extraBtn: {
    width: 16,
    height: 16,
  },

  ctrlBtn: {
    width: 20,
    height: 20,
  },
});

export default RecordingScreen;
