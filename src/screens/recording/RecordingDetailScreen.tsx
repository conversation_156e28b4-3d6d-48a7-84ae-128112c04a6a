import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Share,
  Image,
  PanResponder,
  Animated,
  ScrollView,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import {
  useRoute,
  useNavigation,
  useFocusEffect,
} from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RecordingStackParamList } from '../../types/navigation';
import { useRecordingStore } from '../../stores';
import { colors } from '../../styles';
import { formatDuration, formatDate, formatFileSize } from '../../utils';
import {
  Recording,
  TranscriptionSegment as ApiTranscriptionSegment,
  NoteData,
} from '../../types/recording';
import AIFeaturesModal from '../../components/recording/AIFeaturesModal';
import { useMediaPlayerContext } from '../../contexts/MediaPlayerContext';
import MediaPlayer, {
  MediaPlayerRef,
} from '../../components/recording/MediaPlayer';
import { Button } from '@/components/common';
import { SafeAreaView } from 'react-native-safe-area-context';
import { lcdpService } from '@/services/api';
import ChangeSpeakerNameModal from '@/components/recording/ChangeSpeakerNameModal';
import DisplayNoteModal from '@/components/recording/DisplayNoteModal';
import { MediaPlayerProvider } from '../../contexts/MediaPlayerContext';

type RecordingDetailScreenNavigationProp = StackNavigationProp<
  RecordingStackParamList,
  'RecordingDetail'
>;

interface RouteParams {
  recordingId: string;
  showAIFeatures?: boolean;
}

interface TranscriptionSegment {
  id: string;
  speaker: string;
  time: string;
  content: string;
  speakerName: string;
  noteData?: NoteData[]; // 添加标记数据
}

const RecordingDetailScreen: React.FC = () => {
  const { setCurrentTime } = useMediaPlayerContext();
  // Track the current active chapter time range
  const [activeChapterRange, setActiveChapterRange] = useState<{
    start: number;
    end: number | null;
  } | null>(null);
  const route = useRoute();
  const navigation = useNavigation<RecordingDetailScreenNavigationProp>();
  const { recordingId, showAIFeatures: initialShowAIFeatures } =
    route.params as RouteParams;

  const { recordings, updateRecording } = useRecordingStore();

  const [recording, setRecording] = useState<Recording | null>(null);
  const [showAIFeatures, setShowAIFeatures] = useState(
    initialShowAIFeatures || false
  );
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<number[]>([]);
  const [currentResultIndex, setCurrentResultIndex] = useState(0);
  const [throttledSearchQuery, setThrottledSearchQuery] = useState('');
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [showChangeSpeakerNameModal, setShowChangeSpeakerNameModal] =
    useState(false);
  const [changeSegment, setChangeSegment] = useState<
    TranscriptionSegment | any
  >({});
  const [showNoteModal, setShowNoteModal] = useState(false);
  const [selectedNotes, setSelectedNotes] = useState<NoteData[]>([]);

  const panY = useRef(new Animated.Value(0)).current;
  const scrollViewRef = useRef<ScrollView>(null);
  // Create a ref array to directly access each segment (similar to querySelector)
  const segmentRefs = useRef<{ [key: string]: React.RefObject<View> }>({});
  // Store current scroll position
  const scrollPosition = useRef(0);
  const mediaPlayerRef = useRef<MediaPlayerRef>(null);
  const gestureThreshold = 60; // 下拉阈值

  // Function to handle segment time navigation
  const segmentTime = (time: string, nextChapterTime?: string) => {
    // Convert time format to seconds
    const timeInSeconds = parseTimeToSeconds(time);

    // Set the active chapter range
    const endTimeSeconds = nextChapterTime
      ? parseTimeToSeconds(nextChapterTime)
      : null;
    setActiveChapterRange({
      start: timeInSeconds,
      end: endTimeSeconds,
    });

    // Update the player position
    if (mediaPlayerRef.current) {
      mediaPlayerRef.current.seekTo(timeInSeconds);
    }

    // Also update the context time if available
    if (setCurrentTime) {
      setCurrentTime(timeInSeconds);
    }

    // Find the segment that matches or is closest to the selected time
    if (transcriptionSegments.length > 0 && scrollViewRef.current) {
      // Find the segment with the closest timestamp
      const targetTimeSeconds = parseTimeToSeconds(time);
      let closestSegmentIndex = 0;
      let minTimeDifference = Math.abs(
        parseTimeToSeconds(transcriptionSegments[0].time) - targetTimeSeconds
      );

      transcriptionSegments.forEach((segment, index) => {
        const segmentTimeSeconds = parseTimeToSeconds(segment.time);
        const timeDifference = Math.abs(segmentTimeSeconds - targetTimeSeconds);

        if (timeDifference < minTimeDifference) {
          minTimeDifference = timeDifference;
          closestSegmentIndex = index;
        }
      });

      // In React Native, we can't directly access DOM elements like in web
      // Instead, we'll use a simpler approach with estimated positions
      setTimeout(() => {
        // Find the offset of the segment in the scrollView
        if (scrollViewRef.current) {
          // This is a simple approach - just estimate the position based on segment index
          // and approximate height of each segment
          const estimatedPosition = closestSegmentIndex * 120; // Assuming each segment is about 120px high

          scrollViewRef.current.scrollTo({
            y: estimatedPosition,
            animated: true,
          });
        }
      }, 300); // Small delay to ensure the modal is closed and scroll can happen
    }
  };

  // 预定义的颜色数组，用于说话人区分
  const speakerColors = [
    '#FF6B6B', // 红色
    '#4ECDC4', // 青色
    '#45B7D1', // 蓝色
    '#96CEB4', // 绿色
    '#FECA57', // 黄色
    '#FF9FF3', // 粉色
    '#54A0FF', // 天蓝色
    '#5F27CD', // 紫色
    '#00D2D3', // 蓝绿色
    '#FF9F43', // 橙色
    '#E17055', // 珊瑚色
    '#A29BFE', // 薰衣草色
  ];

  // 根据说话人名称生成固定颜色
  const getSpeakerColor = (speaker: string): string => {
    // 使用简单的字符串哈希函数
    let hash = 0;
    for (let i = 0; i < speaker.length; i++) {
      hash = speaker.charCodeAt(i) + ((hash << 5) - hash);
    }
    // 确保索引为正数并在颜色数组范围内
    const index = Math.abs(hash) % speakerColors.length;
    return speakerColors[index];
  };

  // 时间解析辅助函数：将时间字符串转换为秒数
  const parseTimeToSeconds = (timeStr: string): number => {
    // 支持格式：HH:MM:SS 或 MM:SS 或 SS
    const parts = timeStr.split(':').map(Number);
    if (parts.length === 3) {
      // HH:MM:SS
      return parts[0] * 3600 + parts[1] * 60 + parts[2];
    } else if (parts.length === 2) {
      // MM:SS
      return parts[0] * 60 + parts[1];
    } else if (parts.length === 1) {
      // SS
      return parts[0];
    }
    return 0;
  };

  // 处理标记点击事件
  const handleNoteIconPress = (notes: NoteData[]) => {
    setSelectedNotes(notes);
    setShowNoteModal(true);
  };

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => false,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // 只有在ScrollView滚动到顶部且是下拉手势时才响应
        return gestureState.dy > 0;
      },
      onPanResponderMove: (_, gestureState) => {
        if (gestureState.dy > 0) {
          panY.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dy > gestureThreshold) {
          // 下拉超过阈值，打开AI功能弹窗
          setShowAIFeatures(true);
        }

        // 重置动画
        Animated.spring(panY, {
          toValue: 0,
          useNativeDriver: true,
          bounciness: 10,
        }).start();
      },
      onPanResponderTerminate: () => {
        Animated.spring(panY, {
          toValue: 0,
          useNativeDriver: true,
          bounciness: 10,
        }).start();
      },
    })
  ).current;

  useEffect(() => {
    const foundRecording = recordings.find((r) => r.id === recordingId);
    if (foundRecording) {
      setRecording(foundRecording);
    } else {
      navigation.goBack();
    }
  }, [recordingId, recordings, navigation]);

  // 页面失焦时停止音频播放
  useFocusEffect(
    React.useCallback(() => {
      return () => {
        // 页面失焦时停止播放
        if (mediaPlayerRef.current) {
          mediaPlayerRef.current.stop();
        }
      };
    }, [])
  );

  const handleShare = async () => {
    if (!recording) return;

    try {
      await Share.share({
        url: 'https://www.hjlingxi.com/LCDP/#/userLogin',
        title: recording.title,
        message: `分享录音: ${recording.title}`,
      });
    } catch (error) {
      console.error('分享失败:', error);
    }
  };

  const handleSearchPress = () => {
    setIsSearchVisible(true);
  };

  const handleCloseSearch = () => {
    setIsSearchVisible(false);
    setSearchQuery('');
    setSearchResults([]);
    setCurrentResultIndex(0);
    setThrottledSearchQuery('');
  };

  // Function to create a ref for a segment if it doesn't exist
  const getSegmentRef = (index: number): React.RefObject<View> => {
    const key = `segment-${index}`;
    if (!segmentRefs.current[key]) {
      segmentRefs.current[key] = React.createRef<View>();
    }
    return segmentRefs.current[key];
  };

  // Function to scroll to a specific segment by directly using its ref
  // This is similar to document.querySelector and then scrollIntoView in web
  const scrollToSegment = (index: number) => {
    if (
      scrollViewRef.current &&
      index >= 0 &&
      index < transcriptionSegments.length
    ) {
      const segmentRef = getSegmentRef(index);

      if (segmentRef.current) {
        // Use UIManager to measure the element position (similar to getBoundingClientRect)
        segmentRef.current.measureInWindow((x, y) => {
          // Use the stored scroll position
          const currentScrollY = scrollPosition.current;

          // Calculate offset to position the element at the top of the screen with some padding
          const offset = y - 150; // Add padding from top

          // Calculate final position by adding the current scroll position
          const finalPosition = currentScrollY + offset;

          console.log(
            `Segment ${index} found at y=${y}, scrolling to ${finalPosition}`
          );

          // Scroll to the element position
          scrollViewRef.current!.scrollTo({
            y: finalPosition,
            animated: true,
          });
        });
      } else {
        console.log(
          `Segment ref for index ${index} not found, using fallback method`
        );
        // Fallback to a direct position calculation
        const position = index * 120 - 200; // Estimate position and offset for visibility
        scrollViewRef.current.scrollTo({
          y: Math.max(0, position),
          animated: true,
        });
      }
    }
  };

  // Navigate to next search result
  const goToNextResult = () => {
    if (searchResults.length === 0) return;

    const nextIndex = (currentResultIndex + 1) % searchResults.length;
    console.log(
      `Navigating to next result: ${nextIndex} of ${searchResults.length}`
    );

    // Update state and then scroll with a slight delay to ensure state is updated
    setCurrentResultIndex(nextIndex);

    // Force a small delay to ensure the state update has processed
    setTimeout(() => {
      scrollToSegment(searchResults[nextIndex]);
    }, 100);
  };

  // Navigate to previous search result
  const goToPrevResult = () => {
    if (searchResults.length === 0) return;

    const prevIndex =
      (currentResultIndex - 1 + searchResults.length) % searchResults.length;
    console.log(
      `Navigating to previous result: ${prevIndex} of ${searchResults.length}`
    );

    // Update state and then scroll with a slight delay to ensure state is updated
    setCurrentResultIndex(prevIndex);

    // Force a small delay to ensure the state update has processed
    setTimeout(() => {
      scrollToSegment(searchResults[prevIndex]);
    }, 100);
  };

  // Function to highlight search text in content - memoized for better performance
  const highlightSearchText = React.useCallback(
    (text: string, query: string, isCurrentResult: boolean) => {
      if (!query || query.length < 1)
        return <Text style={styles.segmentContent}>{text}</Text>;

      // Try to use RegExp but catch any errors (for special characters)
      let parts;
      try {
        // Escape special regex characters to prevent errors
        const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        parts = text.split(new RegExp(`(${escapedQuery})`, 'gi'));
      } catch (e) {
        // Fallback to simple rendering if RegExp fails
        return <Text style={styles.segmentContent}>{text}</Text>;
      }

      return (
        <Text style={styles.segmentContent}>
          {parts.map((part, index) => {
            if (part.toLowerCase() === query.toLowerCase()) {
              return (
                <Text
                  key={index}
                  style={[
                    styles.highlightedText,
                    isCurrentResult && styles.currentHighlightedText,
                  ]}
                >
                  {part}
                </Text>
              );
            }
            return <Text key={index}>{part}</Text>;
          })}
        </Text>
      );
    },
    [] // No dependencies needed for this function
  );

  const handleTranscribe = async () => {
    setIsTranscribing(false);
    // 设置状态为处理中，触发轮询机制
    if (recording?.id) {
      updateRecording(recording.id, {
        transcriptionStatus: 'processing',
      });

      await lcdpService.transcribeAudioToText({
        minSpeakers: '1',
        maxSpeakers: '10',
        app_file_id: recording.id,
        duration: recording.duration,
      });
    }
  };

  const openChangeSpeakerNameModal = (segment: TranscriptionSegment) => {
    setShowChangeSpeakerNameModal(true);
    setChangeSegment(segment);
  };

  const handleChangeSpeakerName = async (
    newSpeakerName: string,
    applyToAll: boolean
  ) => {
    if (!newSpeakerName.trim() || !recording?.id) return;

    let newData = recording.transcriptionData;

    if (applyToAll) {
      newData = recording.transcriptionData?.map((c) => ({
        ...c,
        speakerName:
          c.speakerName === changeSegment.speakerName
            ? newSpeakerName
            : c.speakerName,
      }));
    } else {
      newData = recording.transcriptionData?.map((c, i) => ({
        ...c,
        speakerName:
          i === Number(changeSegment.id) - 1 ? newSpeakerName : c.speakerName,
      }));
    }
    await lcdpService.updateSpeakerName({
      recording_records:
        newData?.map((c) => ({
          beginTime: c.beginTime,
          endTime: c.endTime,
          speaker: c.speakerName || c.speaker,
          text: c.text,
        })) || [],
      appFileId: recording.id,
    });
    updateRecording(recording.id, {
      transcriptionData: newData,
    });
  };

  // 🎙️ 转换真实转录数据 - 使用API返回的数据，并整合noteData
  const transcriptionSegments: TranscriptionSegment[] = React.useMemo(() => {
    // 如果recording不存在或没有转录数据或转录未完成，返回空数组
    if (
      !recording ||
      !recording.transcriptionData ||
      recording.transcriptionStatus !== 'completed' ||
      recording.transcriptionData.length === 0
    ) {
      return [];
    }

    // 找到最接近的segment来分配noteData
    const findClosestSegment = (
      noteTime: string,
      segments: ApiTranscriptionSegment[]
    ): number => {
      const noteSeconds = parseTimeToSeconds(noteTime);
      let closestIndex = 0;
      let minDistance = Math.abs(
        parseTimeToSeconds(segments[0].beginTime) - noteSeconds
      );

      segments.forEach((segment, index) => {
        const beginSeconds = parseTimeToSeconds(segment.beginTime);
        const endSeconds = parseTimeToSeconds(segment.endTime);

        // 如果在范围内，直接返回
        if (noteSeconds >= beginSeconds && noteSeconds <= endSeconds) {
          closestIndex = index;
          minDistance = 0;
          return;
        }

        // 计算到开始时间和结束时间的最小距离
        const distanceToBegin = Math.abs(beginSeconds - noteSeconds);
        const distanceToEnd = Math.abs(endSeconds - noteSeconds);
        const minSegmentDistance = Math.min(distanceToBegin, distanceToEnd);

        if (minSegmentDistance < minDistance) {
          minDistance = minSegmentDistance;
          closestIndex = index;
        }
      });

      return closestIndex;
    };

    // 为每个segment创建noteData数组
    const segmentNotesMap: { [key: number]: NoteData[] } = {};

    if (recording.noteData && recording.noteData.length > 0) {
      recording.noteData.forEach((note) => {
        const closestSegmentIndex = findClosestSegment(
          note.markTime,
          recording.transcriptionData!
        );

        if (!segmentNotesMap[closestSegmentIndex]) {
          segmentNotesMap[closestSegmentIndex] = [];
        }
        segmentNotesMap[closestSegmentIndex].push(note);
      });
    }

    // 将API格式转换为UI格式，并整合noteData
    return recording.transcriptionData.map(
      (segment: ApiTranscriptionSegment, index: number) => {
        const segmentNotes = segmentNotesMap[index] || [];

        return {
          id: (index + 1).toString(),
          speakerName:
            segment.speakerName || `说话人${parseInt(segment.speaker) + 1}`, // speaker "0" -> "说话人1"
          time: segment.beginTime, // 使用格式化的开始时间
          content: segment.text, // 使用识别的文本
          speaker: segment.speaker,
          noteData: segmentNotes.length > 0 ? segmentNotes : undefined, // 只有当有标记时才添加
        };
      }
    );
  }, [
    recording?.transcriptionData,
    recording?.transcriptionStatus,
    recording?.noteData,
  ]);

  // Improved throttle function for search with debounce
  useEffect(() => {
    // Use a longer delay to ensure typing is complete
    const timerId = setTimeout(() => {
      setThrottledSearchQuery(searchQuery);
    }, 500); // 500ms delay for better debounce

    return () => {
      clearTimeout(timerId);
    };
  }, [searchQuery]);

  // Optimized search logic
  useEffect(() => {
    if (!throttledSearchQuery || throttledSearchQuery.length < 1) {
      setSearchResults([]);
      setCurrentResultIndex(0);
      return;
    }

    // Perform search in a non-blocking way
    const performSearch = () => {
      const query = throttledSearchQuery.toLowerCase();
      const results: number[] = [];

      // Search in batches to prevent UI freezing
      for (let index = 0; index < transcriptionSegments.length; index++) {
        if (
          transcriptionSegments[index].content.toLowerCase().includes(query)
        ) {
          results.push(index);
        }
      }

      console.log(`Search found ${results.length} results for "${query}"`);

      // Update state with results
      setSearchResults(results);
      if (results.length > 0) {
        setCurrentResultIndex(0);

        // Let the state update first, then scroll
        setTimeout(() => {
          // Direct scroll to the first result using the ref-based approach
          scrollToSegment(results[0]);
        }, 100);
      } else {
        setCurrentResultIndex(-1);
      }
    };

    // Use requestAnimationFrame to optimize rendering
    requestAnimationFrame(performSearch);
  }, [throttledSearchQuery, transcriptionSegments]);

  // 只有在recording存在时才打印日志
  useEffect(() => {
    if (recording) {
      console.log('📄 转录数据显示:', {
        录音ID: recording.id,
        转录状态: recording.transcriptionStatus,
        原始段数: recording.transcriptionData?.length || 0,
        显示段数: transcriptionSegments.length,
      });
    }
  }, [recording, transcriptionSegments.length]);

  if (!recording) {
    return (
      <View style={styles.loadingContainer}>
        <Text>加载中...</Text>
      </View>
    );
  }

  // 下拉提示的动画样式
  const hintStyle = {
    transform: [
      {
        translateY: panY.interpolate({
          inputRange: [0, 40],
          outputRange: [0, 15],
          extrapolate: 'clamp',
        }),
      },
    ],
    opacity: panY.interpolate({
      inputRange: [0, 20, 40],
      outputRange: [0, 0.5, 1],
      extrapolate: 'clamp',
    }),
  };

  // 主内容的动画样式
  const contentStyle = {
    transform: [
      {
        translateY: panY.interpolate({
          inputRange: [0, 60],
          outputRange: [0, 30],
          extrapolate: 'clamp',
        }),
      },
    ],
  };

  // 渲染转录状态
  const renderTranscriptionStatus = (recording: Recording) => {
    switch (recording.transcriptionStatus) {
      case 'processing': {
        const progress = recording.transcriptionProgress || '0%';
        return (
          <View style={styles.transcriptingButton}>
            <Text style={styles.transcriptingButtonText}>
              转写中 {progress}
            </Text>
          </View>
        );
      }
      case 'failed':
        return (
          <Button
            style={styles.transcriptionFailedButton}
            onPress={handleTranscribe}
          >
            <Ionicons
              name="warning"
              size={16}
              color={colors.background.primary}
            />
            <Text style={styles.transcriptionButtonText}>转录失败</Text>
          </Button>
        );
      case 'pending':
      default:
        return (
          <Button style={styles.transcriptionButton} onPress={handleTranscribe}>
            <Text style={styles.transcriptionButtonText}>点击转文字</Text>
            <Ionicons
              name="arrow-forward-sharp"
              size={16}
              color={colors.background.primary}
            />
          </Button>
        );
    }
  };

  return (
    <MediaPlayerProvider>
      <SafeAreaView style={styles.container}>
        <View style={[styles.flex1]} {...panResponder.panHandlers}>
          {/* 顶部导航栏 */}
          <View style={styles.header}>
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Ionicons
                name="arrow-back"
                size={24}
                color={colors.text.primary}
              />
            </TouchableOpacity>
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              style={styles.headerTitle}
            >
              {recording.title}
            </Text>
            <View style={styles.headerActions}>
              <TouchableOpacity
                onPress={handleSearchPress}
                style={styles.headerAction}
              >
                <Ionicons
                  name="search-outline"
                  size={28}
                  color={colors.text.primary}
                />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleShare}
                style={styles.headerAction}
              >
                <Ionicons
                  name="share-outline"
                  size={28}
                  color={colors.text.primary}
                />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => setShowAIFeatures(true)}
                style={styles.headerAction}
              >
                <Image
                  source={require('../../../assets/images/icons/zico.png')}
                  style={styles.zicoIcon}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
          </View>
          <Animated.View style={[contentStyle, styles.flex1]}>
            <Animated.View style={[styles.gestureHintContainer, hintStyle]}>
              <View style={styles.gestureHint}>
                <Image
                  source={require('../../../assets/images/icons/ai.png')}
                  style={styles.aiIcon}
                  resizeMode="contain"
                />
                <Image
                  source={require('../../../assets/images/icons/assistant.png')}
                  style={styles.gestureHintText}
                  resizeMode="contain"
                />
              </View>
            </Animated.View>

            {/* 录音标题 */}
            <View style={styles.titleSection}>
              <View style={styles.recordingMeta}>
                {/* 第一行：时长、日期和文件大小 */}
                <View style={styles.metaRow}>
                  <View style={styles.metaItem}>
                    <Ionicons
                      name="time-outline"
                      size={16}
                      color={colors.text.secondary}
                    />
                    <Text style={styles.metaText}>
                      {formatDuration(recording.duration)}
                    </Text>
                  </View>
                  <View style={[styles.metaItem, { marginLeft: 16 }]}>
                    <Ionicons
                      name="calendar-outline"
                      size={16}
                      color={colors.text.secondary}
                    />
                    <Text style={styles.metaText}>
                      {recording.createdAt
                        ? formatDate(recording.createdAt)
                        : '未知日期'}
                    </Text>
                  </View>
                  <View style={[styles.metaItem, { marginLeft: 16 }]}>
                    <Ionicons
                      name="archive-outline"
                      size={16}
                      color={colors.text.secondary}
                    />
                    <Text style={styles.metaText}>
                      {formatFileSize(recording.size)}
                    </Text>
                  </View>
                </View>
                {/* 第二行：位置信息（如果有的话） */}
                {recording.location && (
                  <View style={[styles.metaItem, { marginTop: 8 }]}>
                    <Ionicons
                      name="location-outline"
                      size={14}
                      color={colors.text.secondary}
                      style={{ marginRight: 4 }}
                    />
                    <Text style={styles.metaText}>{recording.location}</Text>
                  </View>
                )}
              </View>
            </View>

            {/* 录音内容 - 转录文本 */}
            <View style={styles.contentSection}>
              <ScrollView
                ref={scrollViewRef}
                style={styles.transcriptionContainer}
                scrollEventThrottle={16}
                showsVerticalScrollIndicator={false}
                scrollEnabled={!isTranscribing}
                onScroll={(event) => {
                  // Store current scroll position
                  scrollPosition.current = event.nativeEvent.contentOffset.y;
                }}
              >
                {transcriptionSegments.length > 0 ? (
                  // 🎙️ 显示转录结果
                  transcriptionSegments.map((segment) => (
                    <View
                      key={segment.id}
                      style={[
                        styles.segmentContainer,
                        // Apply highlight style if this segment is in the active chapter range
                        activeChapterRange &&
                        parseTimeToSeconds(segment.time) >=
                          activeChapterRange.start &&
                        (!activeChapterRange.end ||
                          parseTimeToSeconds(segment.time) <
                            activeChapterRange.end)
                          ? styles.activeSegment
                          : undefined,
                        // Apply highlight for the current search result
                        isSearchVisible &&
                        searchResults.length > 0 &&
                        currentResultIndex >= 0 &&
                        searchResults[currentResultIndex] ===
                          transcriptionSegments.indexOf(segment)
                          ? styles.currentSearchResult
                          : undefined,
                      ]}
                      ref={getSegmentRef(
                        transcriptionSegments.indexOf(segment)
                      )}
                      onLayout={() => {
                        const index = transcriptionSegments.indexOf(segment);

                        // If this is a search result and it's the current one, scroll to it after layout
                        if (
                          isSearchVisible &&
                          searchResults.length > 0 &&
                          searchResults[currentResultIndex] === index
                        ) {
                          // Use requestAnimationFrame to ensure all layout is complete
                          requestAnimationFrame(() => {
                            scrollToSegment(index);
                          });
                        }
                      }}
                    >
                      <View style={styles.segmentHeader}>
                        <TouchableOpacity
                          style={styles.speakerContainer}
                          onPress={() => openChangeSpeakerNameModal(segment)}
                        >
                          <Text
                            style={[
                              styles.speakerName,
                              { color: getSpeakerColor(segment.speakerName) },
                            ]}
                          >
                            {segment.speakerName}
                          </Text>
                        </TouchableOpacity>
                        <Text style={styles.segmentTime}>{segment.time}</Text>
                        <View style={styles.segmentHeaderRight}>
                          {segment.noteData && segment.noteData.length > 0 && (
                            <TouchableOpacity
                              onPress={() =>
                                handleNoteIconPress(segment.noteData!)
                              }
                            >
                              <Image
                                source={require('../../../assets/images/recording/edit-icon.png')}
                                style={styles.noteIconContainer}
                                resizeMode="contain"
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      </View>
                      {isSearchVisible && searchQuery ? (
                        highlightSearchText(
                          segment.content,
                          searchQuery,
                          searchResults[currentResultIndex] ===
                            transcriptionSegments.indexOf(segment)
                        )
                      ) : (
                        <Text style={styles.segmentContent}>
                          {segment.content}
                        </Text>
                      )}
                    </View>
                  ))
                ) : recording?.transcriptionStatus === 'completed' ? (
                  // 🔇 转录完成但没有内容的空状态
                  <View style={styles.emptyTranscriptionContainer}>
                    <Ionicons
                      name="volume-mute-outline"
                      size={48}
                      color={colors.text.secondary}
                      style={styles.emptyTranscriptionIcon}
                    />
                    <Text style={styles.emptyTranscriptionTitle}>
                      未识别到语音内容
                    </Text>
                    <Text style={styles.emptyTranscriptionSubtitle}>
                      可能是静音文件或音质较差，建议重新录制
                    </Text>
                  </View>
                ) : recording?.transcription ? (
                  <View>
                    <Text>{recording?.transcription}</Text>
                  </View>
                ) : null}
              </ScrollView>
              {/* 转录蒙版 */}
              {recording?.transcriptionStatus !== 'completed' && (
                <View style={styles.transcriptionMask}>
                  {/* 白色背景内容区域 */}
                  <View style={styles.transcriptionMaskContent}>
                    {/* 渐变毛玻璃效果 - 从上方向下渐变 */}
                    <View style={styles.transcriptionBlurContainer}>
                      <View style={styles.transcriptionBlurOverlay}>
                        <LinearGradient
                          colors={['transparent', '#fff']}
                          start={{ x: 0, y: 0 }}
                          end={{ x: 0, y: 1 }}
                          style={styles.blurGradientMask}
                        />
                      </View>
                    </View>
                    <Text style={styles.transcriptionMaskTitle}>
                      当前支持预览部分转文字结果
                    </Text>
                    {renderTranscriptionStatus(recording)}
                  </View>
                </View>
              )}
            </View>
          </Animated.View>

          {/* 下拉提示 - 放在titleSection上方 */}
        </View>

        <AIFeaturesModal
          visible={showAIFeatures}
          onClose={() => setShowAIFeatures(false)}
          recordingId={recordingId}
          onTimePress={segmentTime}
        />

        {/* Search Results Navigation */}
        {isSearchVisible && (
          <View style={styles.searchContainer}>
            <View style={styles.searchInputContainer}>
              <TouchableOpacity
                onPress={handleCloseSearch}
                style={styles.searchBackButton}
              >
                <Ionicons
                  name="arrow-back"
                  size={24}
                  color={colors.text.primary}
                />
              </TouchableOpacity>
              <TextInput
                style={styles.searchInput}
                placeholder="搜索转写内容..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoFocus
                placeholderTextColor={colors.text.secondary}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity
                  onPress={() => setSearchQuery('')}
                  style={styles.clearSearchButton}
                >
                  <Ionicons
                    name="close-circle"
                    size={20}
                    color={colors.text.secondary}
                  />
                </TouchableOpacity>
              )}
            </View>

            {searchResults.length > 0 && (
              <View style={styles.searchResultsNav}>
                <TouchableOpacity
                  onPress={goToPrevResult}
                  style={styles.navButton}
                >
                  <Ionicons
                    name="chevron-back"
                    size={24}
                    color={colors.primary}
                  />
                </TouchableOpacity>
                <Text style={styles.resultCountText}>
                  {currentResultIndex + 1} / {searchResults.length}
                </Text>
                <TouchableOpacity
                  onPress={goToNextResult}
                  style={styles.navButton}
                >
                  <Ionicons
                    name="chevron-forward"
                    size={24}
                    color={colors.primary}
                  />
                </TouchableOpacity>
              </View>
            )}
          </View>
        )}

        {/* 底部播放器 */}
        <MediaPlayer
          ref={mediaPlayerRef}
          audioUri={recording.filePath || ''}
          duration={recording.duration}
        />

        <ChangeSpeakerNameModal
          visible={showChangeSpeakerNameModal}
          onClose={() => setShowChangeSpeakerNameModal(false)}
          onConfirm={handleChangeSpeakerName}
          currentSpeakerName={changeSegment.speakerName}
        />

        {/* 标记内容显示弹窗 */}
        <DisplayNoteModal
          visible={showNoteModal}
          onClose={() => setShowNoteModal(false)}
          notes={selectedNotes}
        />
      </SafeAreaView>
    </MediaPlayerProvider>
  );
};

const styles = StyleSheet.create({
  activeSegment: {
    backgroundColor: colors.warning + '20', // Light version of warning color for background
    borderRadius: 8,
    padding: 8,
    marginHorizontal: -8,
  },
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  flex1: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
  },
  gestureHintContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  gestureHint: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    top: -50,
    position: 'absolute',
  },
  gestureHintText: {
    height: 12,
    width: 110,
  },
  aiIcon: {
    height: 18,
    width: 18,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: colors.background.primary,
    position: 'relative',
    zIndex: 1000,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    width: 180,
    marginLeft: 8,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerAction: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  zicoIcon: {
    width: 28,
    height: 28,
  },
  titleSection: {
    paddingHorizontal: 20,
    backgroundColor: colors.background.primary,
  },
  recordingMeta: {
    flexDirection: 'column',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    paddingBottom: 12,
    paddingHorizontal: 12,
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaText: {
    fontSize: 14,
    color: colors.text.secondary,
    marginLeft: 4,
  },
  contentSection: {
    flex: 1,
    paddingTop: 16,
    position: 'relative',
  },
  transcriptionContainer: {
    flex: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  segmentContainer: {
    marginBottom: 20,
  },
  segmentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  speakerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  speakerIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  speakerName: {
    fontSize: 14,
    marginRight: 8,
  },
  segmentHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-end',
  },
  noteIconContainer: {
    marginRight: 8,
    width: 16,
    height: 16,
  },
  segmentTime: {
    fontSize: 12,
    color: colors.text.secondary,
  },
  segmentContent: {
    fontSize: 16,
    lineHeight: 24,
    color: colors.text.primary,
  },
  transcriptionMask: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    // backgroundColor: 'rgba(0, 0, 0, 0.7)',
    width: '100%',
    zIndex: 1000,
  },
  transcriptionBlurContainer: {
    position: 'absolute',
    top: -120,
    left: 0,
    right: 0,
    height: 120,
    zIndex: 2,
  },
  transcriptionBlurOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 120,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  blurGradientMask: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 120,
  },
  transcriptionMaskContent: {
    position: 'absolute',
    width: '100%',
    bottom: 0,
    backgroundColor: colors.background.primary,
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  transcriptionMaskTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.secondary,
    marginBottom: 20,
  },
  transcriptionMaskText: {
    fontSize: 14,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  transcriptionButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 30,
    gap: 8,
  },
  transcriptionButtonText: {
    color: colors.background.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  transcriptionFailedButton: {
    backgroundColor: colors.error,
    paddingHorizontal: 24,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 30,
    gap: 8,
  },
  transcriptingButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 30,
    gap: 8,
  },
  transcriptingButtonText: {
    color: colors.background.secondary,
    fontSize: 16,
    fontWeight: '600',
  },
  testTranscribeButton: {
    position: 'absolute',
    bottom: 100,
    right: 20,
    backgroundColor: colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  testTranscribeButtonText: {
    color: colors.background.primary,
    fontSize: 14,
    fontWeight: '600',
  },

  // 🔇 空转录状态样式
  emptyTranscriptionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  emptyTranscriptionIcon: {
    marginBottom: 16,
    opacity: 0.6,
  },
  emptyTranscriptionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 8,
  },
  emptyTranscriptionSubtitle: {
    fontSize: 14,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  // Search styles
  searchContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 70, // Above the media player
    backgroundColor: colors.background.primary,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 1000,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  searchBackButton: {
    padding: 4,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: colors.text.primary,
    paddingVertical: 4,
  },
  clearSearchButton: {
    padding: 4,
  },
  searchResultsNav: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
  },
  navButton: {
    padding: 8,
  },
  resultCountText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.primary,
    marginHorizontal: 16,
  },
  highlightedText: {
    backgroundColor: colors.warning + '70', // Semi-transparent warning color
    borderRadius: 2,
    padding: 1,
  },
  currentHighlightedText: {
    backgroundColor: colors.primary + '90', // More opaque primary color
    borderRadius: 2,
    color: colors.background.primary,
    padding: 1,
  },
  currentSearchResult: {
    backgroundColor: colors.warning + '20', // Light background for current result segment
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: colors.primary,
    marginLeft: -4,
    paddingLeft: 4,
  },
});

export default RecordingDetailScreen;
