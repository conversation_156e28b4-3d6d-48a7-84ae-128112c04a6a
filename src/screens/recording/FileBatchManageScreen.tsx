import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRecordingStore } from '../../stores';
import { colors, typography, spacing } from '../../styles';
import { Button } from '../../components/common';
import { Recording } from '../../types/recording';
import {
  formatDuration,
  // formatDurationWithMilliseconds,
  formatDate,
  formatFileSize,
} from '../../utils';
import { DialogUtil } from '@/utils/dialogUtil';
const BatchManageScreen = () => {
  const { recordings, batchDeleteRecordings } = useRecordingStore();
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  const toggleSelect = (id: string) => {
    setSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((sid) => sid !== id) : [...prev, id]
    );
  };

  const handleDelete = () => {
    if (selectedIds.length === 0) return;
    DialogUtil.alert(
      '删除录音',
      `确定要删除选中的${selectedIds.length}个录音吗？`,
      async () => {
        await batchDeleteRecordings(selectedIds);
        setSelectedIds([]);
      }
    );
  };

  const renderItem = ({ item }: { item: Recording }) => {
    const selected = selectedIds.includes(item.id);
    return (
      <TouchableOpacity
        style={[styles.itemContainer, selected && styles.itemSelected]}
        onPress={() => toggleSelect(item.id)}
      >
        <Ionicons
          name={selected ? 'checkbox' : 'square-outline'}
          size={24}
          color={selected ? colors.primary : colors.text.secondary}
          style={{ marginRight: 12 }}
        />
        <View style={{ flex: 1 }}>
          <Text style={styles.title}>{item.title}</Text>
          <View style={styles.recordingItemDetails}>
            <Ionicons
              name="time-outline"
              size={15}
              color={colors.text.secondary}
              style={{ marginRight: 4 }}
            />
            <Text style={styles.recordingItemDesc}>
              {formatDuration(item.duration)}
            </Text>
            <Ionicons
              name="calendar-outline"
              size={15}
              color={colors.text.secondary}
              style={{ marginRight: 4 }}
            />
            <Text style={styles.recordingItemDesc}>
              {item.createdAt ? formatDate(item.createdAt) : '未知日期'}·
              {item.type === 'import' ? '导入' : 'APP'}
            </Text>
            <Text style={styles.recordingItemDesc}>
              {formatFileSize(item.size)}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={recordings}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        extraData={selectedIds}
        contentContainerStyle={{ flexGrow: 1 }}
        ListEmptyComponent={<Text style={styles.empty}>暂无录音</Text>}
      />
      <View style={styles.footer}>
        <Button
          title="删除"
          onPress={handleDelete}
          disabled={selectedIds.length === 0}
          style={styles.deleteBtn}
          textStyle={styles.deleteBtnText}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: colors.separator,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.separator,
    backgroundColor: '#fff',
  },
  itemSelected: {
    backgroundColor: colors.primary + '10',
  },
  title: {
    fontSize: typography.fontSize.base,
    color: colors.text.primary,
    fontWeight: typography.fontWeight.medium,
  },

  recordingItemDetails: {
    alignItems: 'center',
    flexDirection: 'row',
    color: colors.text.secondary,
  },

  recordingItemDesc: {
    fontSize: 12,
    color: colors.text.secondary,
    marginRight: 8,
  },
  subtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginTop: 2,
  },
  empty: {
    textAlign: 'center',
    color: colors.text.secondary,
    marginTop: 40,
  },
  footer: {
    padding: spacing.lg,
    backgroundColor: colors.background.secondary,
    borderTopWidth: 1,
    borderTopColor: colors.separator,
  },
  deleteBtn: {
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  deleteBtnText: {
    color: '#F23030',
  },
});

export default BatchManageScreen;
