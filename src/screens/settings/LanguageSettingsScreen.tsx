import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@/styles';
import { useAppStore } from '@/stores';
import Toast from 'react-native-toast-message';

interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
}

export const LanguageSettingsScreen: React.FC = () => {
  const { language, setLanguage } = useAppStore();
  const [selectedLanguage, setSelectedLanguage] = useState<string>(language);

  const languages: Language[] = [
    {
      code: 'zh',
      name: 'Chinese (Simplified)',
      nativeName: '简体中文',
      flag: '🇨🇳',
    },
    {
      code: 'zh-TW',
      name: 'Chinese (Traditional)',
      nativeName: '繁體中文',
      flag: '🇹🇼',
    },
    {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸',
    },
    {
      code: 'ja',
      name: 'Japanese',
      nativeName: '日本語',
      flag: '🇯🇵',
    },
    {
      code: 'ko',
      name: 'Korean',
      nativeName: '한국어',
      flag: '🇰🇷',
    },
    {
      code: 'es',
      name: 'Spanish',
      nativeName: 'Español',
      flag: '🇪🇸',
    },
    {
      code: 'fr',
      name: 'French',
      nativeName: 'Français',
      flag: '🇫🇷',
    },
    {
      code: 'de',
      name: 'German',
      nativeName: 'Deutsch',
      flag: '🇩🇪',
    },
    {
      code: 'ru',
      name: 'Russian',
      nativeName: 'Русский',
      flag: '🇷🇺',
    },
    {
      code: 'ar',
      name: 'Arabic',
      nativeName: 'العربية',
      flag: '🇸🇦',
    },
  ];

  const handleLanguageSelect = (langCode: string) => {
    setSelectedLanguage(langCode);
    // 只支持中文和英文
    if (langCode === 'zh' || langCode === 'en') {
      setLanguage(langCode);
    }

    Toast.show({
      type: 'success',
      text1: '语言已更改',
      text2: `已切换到${languages.find((l) => l.code === langCode)?.nativeName}`,
      visibilityTime: 2000,
    });
  };

  const renderLanguageItem = (language: Language) => {
    const isSelected = selectedLanguage === language.code;

    return (
      <TouchableOpacity
        key={language.code}
        style={[styles.languageItem, isSelected && styles.languageItemSelected]}
        onPress={() => handleLanguageSelect(language.code)}
      >
        <View style={styles.languageInfo}>
          <Text style={styles.languageFlag}>{language.flag}</Text>
          <View style={styles.languageText}>
            <Text
              style={[
                styles.languageName,
                isSelected && styles.languageNameSelected,
              ]}
            >
              {language.nativeName}
            </Text>
            <Text style={styles.languageSubtitle}>{language.name}</Text>
          </View>
        </View>
        {isSelected && (
          <Ionicons name="checkmark-circle" size={24} color={colors.primary} />
        )}
      </TouchableOpacity>
    );
  };

  const popularLanguages = languages.slice(0, 5);
  const otherLanguages = languages.slice(5);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>语言设置</Text>
          <Text style={styles.subtitle}>选择应用显示语言</Text>
        </View>

        {/* 常用语言 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>常用语言</Text>
          <View style={styles.sectionContent}>
            {popularLanguages.map(renderLanguageItem)}
          </View>
        </View>

        {/* 其他语言 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>其他语言</Text>
          <View style={styles.sectionContent}>
            {otherLanguages.map(renderLanguageItem)}
          </View>
        </View>

        {/* 提示信息 */}
        <View style={styles.tips}>
          <View style={styles.tipItem}>
            <Ionicons
              name="information-circle"
              size={20}
              color={colors.text.secondary}
            />
            <Text style={styles.tipText}>更改语言后，应用界面将立即更新</Text>
          </View>
          <View style={styles.tipItem}>
            <Ionicons name="globe" size={20} color={colors.text.secondary} />
            <Text style={styles.tipText}>
              部分功能可能需要重启应用才能完全生效
            </Text>
          </View>
        </View>

        {/* 高级设置 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>高级设置</Text>
          <View style={styles.sectionContent}>
            <TouchableOpacity style={styles.advancedItem}>
              <View style={styles.advancedInfo}>
                <Text style={styles.advancedTitle}>跟随系统语言</Text>
                <Text style={styles.advancedDescription}>
                  自动使用系统设置的语言
                </Text>
              </View>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={colors.text.secondary}
              />
            </TouchableOpacity>
            <TouchableOpacity style={styles.advancedItem}>
              <View style={styles.advancedInfo}>
                <Text style={styles.advancedTitle}>地区设置</Text>
                <Text style={styles.advancedDescription}>
                  设置日期、时间和数字格式
                </Text>
              </View>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={colors.text.secondary}
              />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    marginBottom: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.secondary,
    marginBottom: spacing.sm,
    marginHorizontal: spacing.lg,
    textTransform: 'uppercase',
  },
  sectionContent: {
    backgroundColor: colors.background.secondary,
    marginHorizontal: spacing.lg,
    borderRadius: 12,
    overflow: 'hidden',
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  languageItemSelected: {
    backgroundColor: colors.primary + '10',
  },
  languageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  languageFlag: {
    fontSize: 28,
    marginRight: spacing.md,
  },
  languageText: {
    flex: 1,
  },
  languageName: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.xs / 2,
  },
  languageNameSelected: {
    color: colors.primary,
  },
  languageSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  tips: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  tipText: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
    marginLeft: spacing.sm,
    flex: 1,
  },
  advancedItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  advancedInfo: {
    flex: 1,
  },
  advancedTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.xs / 2,
  },
  advancedDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
});
