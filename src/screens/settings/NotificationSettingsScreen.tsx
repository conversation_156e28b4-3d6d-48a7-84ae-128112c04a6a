import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Switch } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors, spacing, typography } from '@/styles';
import Toast from 'react-native-toast-message';

interface NotificationSetting {
  id: string;
  title: string;
  description: string;
  enabled: boolean;
}

export const NotificationSettingsScreen: React.FC = () => {
  const [settings, setSettings] = useState<NotificationSetting[]>([
    {
      id: 'push',
      title: '推送通知',
      description: '接收应用推送通知',
      enabled: true,
    },
    {
      id: 'recording',
      title: '录音提醒',
      description: '录音开始和结束时通知',
      enabled: true,
    },
    {
      id: 'transcription',
      title: '转写完成',
      description: '语音转写完成时通知',
      enabled: true,
    },
    {
      id: 'ai_writing',
      title: 'AI写作',
      description: 'AI内容生成完成时通知',
      enabled: false,
    },
    {
      id: 'knowledge',
      title: '知识库更新',
      description: '文档上传或处理完成时通知',
      enabled: true,
    },
    {
      id: 'calendar',
      title: '日程提醒',
      description: '会议和事件提醒通知',
      enabled: true,
    },
    {
      id: 'system',
      title: '系统通知',
      description: '应用更新和系统消息',
      enabled: true,
    },
    {
      id: 'marketing',
      title: '营销推广',
      description: '优惠活动和推荐内容',
      enabled: false,
    },
  ]);

  const [quietHours, setQuietHours] = useState({
    enabled: false,
    startTime: '22:00',
    endTime: '08:00',
  });

  const handleToggle = (id: string, value: boolean) => {
    setSettings((prev) =>
      prev.map((setting) =>
        setting.id === id ? { ...setting, enabled: value } : setting
      )
    );

    Toast.show({
      type: 'success',
      text1: '设置已更新',
      text2: `${settings.find((s) => s.id === id)?.title}${value ? '已开启' : '已关闭'}`,
      visibilityTime: 2000,
    });
  };

  const renderNotificationSetting = (setting: NotificationSetting) => (
    <View key={setting.id} style={styles.settingItem}>
      <View style={styles.settingInfo}>
        <Text style={styles.settingTitle}>{setting.title}</Text>
        <Text style={styles.settingDescription}>{setting.description}</Text>
      </View>
      <Switch
        value={setting.enabled}
        onValueChange={(value) => handleToggle(setting.id, value)}
        trackColor={{
          false: colors.border,
          true: colors.primary + '50',
        }}
        thumbColor={
          setting.enabled ? colors.primary : colors.background.primary
        }
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>通知设置</Text>
          <Text style={styles.subtitle}>管理应用通知偏好</Text>
        </View>

        {/* 通知类型 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>通知类型</Text>
          <View style={styles.sectionContent}>
            {settings.map(renderNotificationSetting)}
          </View>
        </View>

        {/* 免打扰时段 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>免打扰时段</Text>
          <View style={styles.sectionContent}>
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>启用免打扰</Text>
                <Text style={styles.settingDescription}>
                  在指定时间段内不接收通知
                </Text>
              </View>
              <Switch
                value={quietHours.enabled}
                onValueChange={(value) =>
                  setQuietHours({ ...quietHours, enabled: value })
                }
                trackColor={{
                  false: colors.border,
                  true: colors.primary + '50',
                }}
                thumbColor={
                  quietHours.enabled
                    ? colors.primary
                    : colors.background.primary
                }
              />
            </View>

            {quietHours.enabled && (
              <View style={styles.timeSettings}>
                <View style={styles.timeItem}>
                  <Text style={styles.timeLabel}>开始时间</Text>
                  <Text style={styles.timeValue}>{quietHours.startTime}</Text>
                </View>
                <View style={styles.timeItem}>
                  <Text style={styles.timeLabel}>结束时间</Text>
                  <Text style={styles.timeValue}>{quietHours.endTime}</Text>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* 通知样式 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>通知样式</Text>
          <View style={styles.sectionContent}>
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>声音</Text>
                <Text style={styles.settingDescription}>播放通知提示音</Text>
              </View>
              <Switch
                value={true}
                onValueChange={() => {}}
                trackColor={{
                  false: colors.border,
                  true: colors.primary + '50',
                }}
                thumbColor={colors.primary}
              />
            </View>
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>振动</Text>
                <Text style={styles.settingDescription}>收到通知时振动</Text>
              </View>
              <Switch
                value={true}
                onValueChange={() => {}}
                trackColor={{
                  false: colors.border,
                  true: colors.primary + '50',
                }}
                thumbColor={colors.primary}
              />
            </View>
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>锁屏显示</Text>
                <Text style={styles.settingDescription}>
                  在锁屏界面显示通知内容
                </Text>
              </View>
              <Switch
                value={false}
                onValueChange={() => {}}
                trackColor={{
                  false: colors.border,
                  true: colors.primary + '50',
                }}
                thumbColor={colors.background.primary}
              />
            </View>
          </View>
        </View>

        {/* 提示信息 */}
        <View style={styles.tips}>
          <Text style={styles.tipsText}>
            提示：某些通知设置可能需要在系统设置中进行调整
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    marginBottom: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.secondary,
    marginBottom: spacing.sm,
    marginHorizontal: spacing.lg,
    textTransform: 'uppercase',
  },
  sectionContent: {
    backgroundColor: colors.background.secondary,
    marginHorizontal: spacing.lg,
    borderRadius: 12,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingItem_last: {
    borderBottomWidth: 0,
  },
  settingInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  settingTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.xs / 2,
  },
  settingDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  timeSettings: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.md,
  },
  timeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  timeLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  timeValue: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  tips: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  tipsText: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
    textAlign: 'center',
  },
});
