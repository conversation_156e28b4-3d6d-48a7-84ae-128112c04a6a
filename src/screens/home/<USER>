import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { colors, typography, spacing } from '../../styles';
import { TabParamList } from '../../types/navigation';

const { width } = Dimensions.get('window');
const GRID_ITEM_WIDTH = (width - spacing.lg * 3) / 2;

interface QuickActionItem {
  id: string;
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  onPress: () => void;
}

type HomeScreenNavigationProp = BottomTabNavigationProp<
  TabParamList,
  'HomeTab'
>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const [refreshing, setRefreshing] = useState(false);

  const quickActions: QuickActionItem[] = [
    {
      id: 'recording',
      title: '开始录音',
      icon: 'mic',
      color: colors.error,
      onPress: () => {
        navigation.navigate('RecordingTab');
      },
    },
    {
      id: 'writing',
      title: 'AI写作',
      icon: 'create',
      color: colors.primary,
      onPress: () => {
        navigation.navigate('WritingTab');
      },
    },
    {
      id: 'knowledge',
      title: '知识库',
      icon: 'library',
      color: colors.accent,
      onPress: () => {
        navigation.navigate('KnowledgeTab');
      },
    },
    {
      id: 'import',
      title: '导入文件',
      icon: 'cloud-upload',
      color: colors.success,
      onPress: () => {
        navigation.navigate('KnowledgeTab');
      },
    },
  ];

  const recentItems = [
    {
      id: '1',
      title: '会议录音_2025-07-15',
      type: 'recording',
      time: '2小时前',
    },
    {
      id: '2',
      title: '产品需求文档',
      type: 'document',
      time: '昨天',
    },
    {
      id: '3',
      title: '营销文案草稿',
      type: 'writing',
      time: '3天前',
    },
  ];

  const renderQuickAction = (item: QuickActionItem) => (
    <TouchableOpacity
      key={item.id}
      style={[styles.quickActionItem, { backgroundColor: item.color + '15' }]}
      onPress={item.onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.iconContainer, { backgroundColor: item.color }]}>
        <Ionicons name={item.icon} size={24} color="white" />
      </View>
      <Text style={styles.quickActionTitle}>{item.title}</Text>
    </TouchableOpacity>
  );

  const getItemIcon = (type: string) => {
    switch (type) {
      case 'recording':
        return 'mic';
      case 'document':
        return 'document-text';
      case 'writing':
        return 'create';
      default:
        return 'document';
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // TODO: 刷新数据
      await new Promise((resolve) => setTimeout(resolve, 1500));
    } finally {
      setRefreshing(false);
    }
  }, []);

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={colors.background.primary}
      />
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* 欢迎区域 */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>欢迎回来！</Text>
          <Text style={styles.welcomeSubtitle}>
            今天是{' '}
            {new Date().toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              weekday: 'long',
            })}
          </Text>
        </View>

        {/* 快捷操作 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>快捷操作</Text>
          <View style={styles.quickActionsGrid}>
            {quickActions.map(renderQuickAction)}
          </View>
        </View>

        {/* 最近使用 */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>最近使用</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>查看全部</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.recentList}>
            {recentItems.map((item) => (
              <TouchableOpacity key={item.id} style={styles.recentItem}>
                <View style={styles.recentItemLeft}>
                  <View style={styles.recentItemIcon}>
                    <Ionicons
                      name={getItemIcon(item.type)}
                      size={20}
                      color={colors.primary}
                    />
                  </View>
                  <View style={styles.recentItemInfo}>
                    <Text style={styles.recentItemTitle}>{item.title}</Text>
                    <Text style={styles.recentItemTime}>{item.time}</Text>
                  </View>
                </View>
                <Ionicons
                  name="chevron-forward"
                  size={16}
                  color={colors.text.secondary}
                />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 统计信息 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>使用统计</Text>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>12</Text>
              <Text style={styles.statLabel}>录音文件</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>8</Text>
              <Text style={styles.statLabel}>知识文档</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>5</Text>
              <Text style={styles.statLabel}>AI文章</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },

  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },

  welcomeSection: {
    padding: spacing.lg,
    backgroundColor: colors.primary + '10',
  },

  welcomeTitle: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },

  welcomeSubtitle: {
    fontSize: typography.fontSize.base,
    color: colors.text.secondary,
  },

  section: {
    padding: spacing.lg,
  },

  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },

  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },

  seeAllText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
  },

  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },

  quickActionItem: {
    width: GRID_ITEM_WIDTH,
    padding: spacing.md,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: spacing.md,
  },

  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },

  quickActionTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    textAlign: 'center',
  },

  recentList: {
    backgroundColor: colors.background.primary,
  },

  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.separator,
  },

  recentItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  recentItemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },

  recentItemInfo: {
    flex: 1,
  },

  recentItemTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: spacing.xs / 2,
  },

  recentItemTime: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },

  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    paddingVertical: spacing.lg,
  },

  statItem: {
    alignItems: 'center',
  },

  statNumber: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
    marginBottom: spacing.xs,
  },

  statLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
});

export default HomeScreen;
