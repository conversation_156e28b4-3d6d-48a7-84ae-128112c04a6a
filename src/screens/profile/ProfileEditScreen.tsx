import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@/styles';
import { useAuthStore } from '@/stores';
import Toast from 'react-native-toast-message';
import { DialogUtil } from '@/utils/dialogUtil';

interface UserProfile {
  avatar?: string;
  username: string;
  email: string;
  phone: string;
}

export const ProfileEditScreen: React.FC = () => {
  const { user, updateUser } = useAuthStore();
  const [profile, setProfile] = useState<UserProfile>({
    avatar: user?.avatar,
    username: user?.username || '',
    email: user?.email || '',
    phone: user?.phone || '',
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    if (!profile.username.trim()) {
      Toast.show({
        type: 'error',
        text1: '用户名不能为空',
      });
      return;
    }

    if (!profile.email.trim()) {
      Toast.show({
        type: 'error',
        text1: '邮箱不能为空',
      });
      return;
    }

    setIsLoading(true);
    try {
      updateUser(profile);
      Toast.show({
        type: 'success',
        text1: '个人资料更新成功',
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '更新失败',
        text2: error instanceof Error ? error.message : '请稍后重试',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAvatarPress = () => {
    DialogUtil.custom(
      '更换头像',
      <View style={{ alignItems: 'center' }}>
        <Text style={{ fontSize: 16, color: colors.text.primary, marginBottom: 20 }}>
          选择头像来源
        </Text>
        <TouchableOpacity
          style={{ padding: 10, marginBottom: 10 }}
          onPress={() => {
            DialogUtil.hide();
            console.log('选择相册');
          }}
        >
          <Text style={{ fontSize: 16, color: colors.primary }}>相册</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={{ padding: 10 }}
          onPress={() => {
            DialogUtil.hide();
            console.log('拍照');
          }}
        >
          <Text style={{ fontSize: 16, color: colors.primary }}>拍照</Text>
        </TouchableOpacity>
      </View>,
      undefined,
      {
        confirmText: '取消',
        cancelText: '',
      }
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 头像区域 */}
        <View style={styles.avatarSection}>
          <TouchableOpacity style={styles.avatarContainer} onPress={handleAvatarPress}>
            {profile.avatar ? (
              <Image source={{ uri: profile.avatar }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Ionicons name="person" size={40} color={colors.text.secondary} />
              </View>
            )}
            <View style={styles.avatarEditIcon}>
              <Ionicons name="camera" size={16} color={colors.background.primary} />
            </View>
          </TouchableOpacity>
          <Text style={styles.avatarHint}>点击更换头像</Text>
        </View>

        {/* 表单区域 */}
        <View style={styles.formSection}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>用户名</Text>
            <TextInput
              style={styles.input}
              value={profile.username}
              onChangeText={(text) => setProfile({ ...profile, username: text })}
              placeholder="请输入用户名"
              placeholderTextColor={colors.text.secondary}
              maxLength={20}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>邮箱</Text>
            <TextInput
              style={styles.input}
              value={profile.email}
              onChangeText={(text) => setProfile({ ...profile, email: text })}
              placeholder="请输入邮箱"
              placeholderTextColor={colors.text.secondary}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>手机号</Text>
            <TextInput
              style={[styles.input, styles.disabledInput]}
              value={profile.phone}
              placeholder="请输入手机号"
              placeholderTextColor={colors.text.secondary}
              keyboardType="phone-pad"
              editable={false}
            />
            <Text style={styles.hint}>手机号暂不支持修改</Text>
          </View>


        </View>
      </ScrollView>

      {/* 保存按钮 */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.saveButton, isLoading && styles.disabledButton]}
          onPress={handleSave}
          disabled={isLoading}
          activeOpacity={0.8}
        >
          <Text style={styles.saveButtonText}>
            {isLoading ? '保存中...' : '保存'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  avatarSection: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
    backgroundColor: colors.background.primary,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: spacing.sm,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.background.secondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarEditIcon: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: colors.background.primary,
  },
  avatarHint: {
    fontSize: 14,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily,
  },
  formSection: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
  },
  inputGroup: {
    marginBottom: spacing.xl,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing.sm,
    fontFamily: typography.fontFamily,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: 16,
    color: colors.text.primary,
    backgroundColor: colors.background.primary,
    fontFamily: typography.fontFamily,
  },
  disabledInput: {
    backgroundColor: colors.background.secondary,
    color: colors.text.secondary,
  },
  textArea: {
    height: 100,
    paddingTop: spacing.sm,
  },
  hint: {
    fontSize: 12,
    color: colors.text.secondary,
    marginTop: spacing.xs,
    fontFamily: typography.fontFamily,
  },
  charCount: {
    fontSize: 12,
    color: colors.text.secondary,
    textAlign: 'right',
    marginTop: spacing.xs,
    fontFamily: typography.fontFamily,
  },
  buttonContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    backgroundColor: colors.background.primary,
  },
  saveButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: spacing.md,
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.6,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.background.primary,
    fontFamily: typography.fontFamily,
  },
});

export default ProfileEditScreen;
