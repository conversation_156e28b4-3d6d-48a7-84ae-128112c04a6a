import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
  ScrollView,
  Modal,
  FlatList,
  Dimensions,
  NativeSyntheticEvent,
  NativeScrollEvent,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { colors } from '@/styles';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth } = Dimensions.get('window');

// 选择器数据
const wordCountOptions = [
  '默认',
  '100',
  '500',
  '1000',
  '2000',
  '3000',
  '自定义',
];
const styleOptions = ['默认', '正式', '严肃', '口语化', '简洁', '生动', '幽默'];
const advancedOptions = ['默认', '高质量', '快速生成', '创意优先'];

// 灵感轮播数据
const inspirationData = [
  {
    id: 1,
    platform: '小红书',
    content:
      '我是一名美妆爱好者，想在小红书分享一款粉底液，突出遮瑕力和持妆效果，风格生动活泼，字数 500 。',
    platformColor: '#F23030',
    gradientColors: [
      'rgba(255, 51, 51, 0.4)',
      'rgba(255, 92, 92, 0.12)',
      'rgba(255, 133, 133, 0.1)',
    ],
    gradientLocations: [0, 0.44, 1],
    gradientStart: { x: 0.985, y: 0.028 },
    gradientEnd: { x: 0.026, y: 1 },
    iconSource: require('../../../assets/images/writing/heart.png'),
  },
  {
    id: 2,
    platform: '公众号推文',
    content:
      '我是一名美妆爱好者，想在小红书分享一款粉底液，突出遮瑕力和持妆效果，风格生动活泼，字数 500 。',
    platformColor: '#02B342',
    gradientColors: [
      'rgba(51, 255, 54, 0.4)',
      'rgba(106, 255, 92, 0.12)',
      'rgba(149, 255, 133, 0.1)',
    ],
    gradientLocations: [0, 0.44, 1],
    gradientStart: { x: 0.985, y: 0.028 },
    gradientEnd: { x: 0.026, y: 1 },
    iconSource: require('../../../assets/images/writing/wechat.png'),
  },
  {
    id: 3,
    platform: '职场汇报',
    content:
      '我是一名美妆爱好者，想在小红书分享一款粉底液，突出遮瑕力和持妆效果，风格生动活泼，字数 500 。',
    platformColor: '#417FFB',
    gradientColors: [
      'rgba(51, 95, 255, 0.4)',
      'rgba(92, 125, 255, 0.12)',
      'rgba(133, 178, 255, 0.1)',
    ],
    gradientLocations: [0, 0.44, 1],
    gradientStart: { x: 0.985, y: 0.028 },
    gradientEnd: { x: 0.026, y: 1 },
    iconSource: require('../../../assets/images/writing/question.png'),
  },
  {
    id: 4,
    platform: '产品宣传文案',
    content:
      '我是一名美妆爱好者，想在小红书分享一款粉底液，突出遮瑕力和持妆效果，风格生动活泼，字数 500 。',
    platformColor: '#FAAF0C',
    gradientColors: [
      'rgba(255, 204, 51, 0.4)',
      'rgba(255, 214, 92, 0.12)',
      'rgba(255, 233, 133, 0.1)',
    ],
    gradientLocations: [0, 0.44, 1],
    gradientStart: { x: 0.985, y: 0.028 },
    gradientEnd: { x: 0.026, y: 1 },
    iconSource: require('../../../assets/images/writing/announcement.png'),
  },
  {
    id: 5,
    platform: '故事创作',
    content:
      '我是一名美妆爱好者，想在小红书分享一款粉底液，突出遮瑕力和持妆效果，风格生动活泼，字数 500 。',
    platformColor: '#7252FF',
    gradientColors: [
      'rgba(122, 51, 255, 0.4)',
      'rgba(146, 92, 255, 0.12)',
      'rgba(178, 133, 255, 0.1)',
    ],
    gradientLocations: [0, 0.44, 1],
    gradientStart: { x: 0.985, y: 0.028 },
    gradientEnd: { x: 0.026, y: 1 },
    iconSource: require('../../../assets/images/writing/book.png'),
  },
];

export const WritingCreationScreen: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const [wordCount, setWordCount] = useState('默认');
  const [style, setStyle] = useState('默认');
  const [advancedSettings, setAdvancedSettings] = useState('默认');

  // 选择器状态
  const [showWordCountModal, setShowWordCountModal] = useState(false);
  const [showStyleModal, setShowStyleModal] = useState(false);
  const [showAdvancedModal, setShowAdvancedModal] = useState(false);

  // 轮播状态
  const [currentInspirationIndex, setCurrentInspirationIndex] = useState(0);

  const handlePaste = () => {
    // TODO: 实现一键粘贴功能
  };

  const handleClear = () => {
    setInputText('');
  };

  const handleVoiceInput = () => {
    // TODO: 实现语音输入功能
  };

  const handleGenerate = () => {
    // TODO: 实现开始生成功能
  };

  // 选择器处理函数
  const handleWordCountSelect = (option: string) => {
    setWordCount(option);
    setShowWordCountModal(false);
  };

  const handleStyleSelect = (option: string) => {
    setStyle(option);
    setShowStyleModal(false);
  };

  const handleAdvancedSelect = (option: string) => {
    setAdvancedSettings(option);
    setShowAdvancedModal(false);
  };

  // 轮播滚动处理
  const handleInspirationScroll = (
    event: NativeSyntheticEvent<NativeScrollEvent>
  ) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const cardWidth = screenWidth - 32; // 减去左右padding
    const index = Math.round(contentOffsetX / cardWidth);
    setCurrentInspirationIndex(index);
  };

  // 渲染选择器组件
  const renderSelectorModal = (
    visible: boolean,
    options: string[],
    currentValue: string,
    onSelect: (value: string) => void,
    onClose: () => void,
    title: string
  ) => (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{title}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#2A2B33" />
            </TouchableOpacity>
          </View>
          <FlatList
            data={options}
            keyExtractor={(item) => item}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.optionItem,
                  item === currentValue && styles.selectedOption,
                ]}
                onPress={() => onSelect(item)}
              >
                <Text
                  style={[
                    styles.optionText,
                    item === currentValue && styles.selectedOptionText,
                  ]}
                >
                  {item}
                </Text>
                {item === currentValue && (
                  <Ionicons name="checkmark" size={20} color="#2E4BEB" />
                )}
              </TouchableOpacity>
            )}
          />
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.container} edges={['left', 'right', 'bottom']}>
      {/* 主要内容区域 */}
      <ScrollView
        style={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.mainContent}>
          {/* 输入区域 */}
          <LinearGradient
            colors={['#3053ED', 'rgba(82, 121, 255, 0.8)', '#3EDDB6']}
            locations={[0, 0.5, 1]}
            start={{ x: -0.12, y: -0.038 }}
            end={{ x: 1.024, y: 1.047 }}
            style={styles.inputContainer}
          >
            <View style={styles.inputWrapper}>
              <TextInput
                style={styles.textInput}
                placeholder="请描述您想要创作的内容要求"
                placeholderTextColor="#B0B2BF"
                value={inputText}
                onChangeText={setInputText}
                multiline
                textAlignVertical="top"
              />

              {/* 底部操作按钮 */}
              <View style={styles.inputActions}>
                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={handlePaste}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.actionButtonText}>一键粘贴</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={handleClear}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.actionButtonText}>清空文本</Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.voiceActions}>
                  <TouchableOpacity
                    style={styles.addButton}
                    activeOpacity={0.7}
                  >
                    <Ionicons name="add" size={24} color="#414352" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.voiceButton}
                    onPress={handleVoiceInput}
                    activeOpacity={0.7}
                  >
                    <LinearGradient
                      colors={['#3053ED', 'rgba(82, 121, 255, 0.8)', '#3EDDB6']}
                      locations={[0, 0.5, 1]}
                      start={{ x: -0.12, y: -0.038 }}
                      end={{ x: 1.024, y: 1.047 }}
                      style={styles.voiceButtonGradient}
                    >
                      <Image
                        source={require('../../../assets/images/icons/microphone.png')}
                        style={styles.microphoneIcon}
                      />
                    </LinearGradient>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </LinearGradient>

          {/* 设置选项区域 */}
          <View style={styles.settingsContainer}>
            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => setShowWordCountModal(true)}
            >
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>字数</Text>
                <Text style={styles.settingValue}>{wordCount}</Text>
              </View>
              <Ionicons name="chevron-forward" size={16} color="#9092A3" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => setShowStyleModal(true)}
            >
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>风格</Text>
                <Text style={styles.settingValue}>{style}</Text>
              </View>
              <Ionicons name="chevron-forward" size={16} color="#9092A3" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => setShowAdvancedModal(true)}
            >
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>高级设置</Text>
                <Text style={styles.settingValue}>{advancedSettings}</Text>
              </View>
              <Ionicons name="chevron-forward" size={16} color="#9092A3" />
            </TouchableOpacity>
          </View>

          {/* 开始生成按钮 */}
          <TouchableOpacity
            style={styles.generateButton}
            onPress={handleGenerate}
            activeOpacity={0.8}
          >
            <Text style={styles.generateButtonText}>开始生成</Text>
          </TouchableOpacity>
        </View>

        {/* 灵感区域 */}
        <View style={styles.inspirationSection}>
          <View style={styles.inspirationHeader}>
            <Text style={styles.inspirationTitle}>找点灵感</Text>
          </View>

          <ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={handleInspirationScroll}
            style={styles.inspirationCarousel}
          >
            {inspirationData.map((item, _index) => (
              <View key={item.id} style={styles.inspirationCardContainer}>
                <View style={styles.innerContainer}>
                  <LinearGradient
                    colors={item.gradientColors}
                    locations={item.gradientLocations}
                    start={item.gradientStart}
                    end={item.gradientEnd}
                    style={styles.inspirationCard}
                  >
                    <View style={styles.inspirationContent}>
                      <Text
                        style={[
                          styles.inspirationPlatform,
                          { color: item.platformColor },
                        ]}
                      >
                        {item.platform}
                      </Text>
                      <Text style={styles.inspirationText}>{item.content}</Text>
                    </View>
                    <View style={styles.inspirationIndicator}>
                      {inspirationData.map((_, dotIndex) => (
                        <View
                          key={dotIndex}
                          style={[
                            styles.dot,
                            dotIndex !== currentInspirationIndex &&
                              styles.dotInactive,
                          ]}
                        />
                      ))}
                    </View>
                  </LinearGradient>

                  <View style={styles.topicImage}>
                    <Image
                      source={item.iconSource}
                      style={styles.topicImageIcon}
                    />
                  </View>
                </View>
              </View>
            ))}
          </ScrollView>
        </View>
      </ScrollView>

      {/* 选择器模态框 */}
      {renderSelectorModal(
        showWordCountModal,
        wordCountOptions,
        wordCount,
        handleWordCountSelect,
        () => setShowWordCountModal(false),
        '选择字数'
      )}

      {renderSelectorModal(
        showStyleModal,
        styleOptions,
        style,
        handleStyleSelect,
        () => setShowStyleModal(false),
        '选择风格'
      )}

      {renderSelectorModal(
        showAdvancedModal,
        advancedOptions,
        advancedSettings,
        handleAdvancedSelect,
        () => setShowAdvancedModal(false),
        '高级设置'
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.bg,
  },

  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 6,
  },

  mainContent: {
    gap: 16,
  },

  inputContainer: {
    borderRadius: 16,
    padding: 2,
    shadowColor: 'rgba(100, 103, 122, 0.12)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 16,
    elevation: 8,
  },

  inputWrapper: {
    backgroundColor: '#FFFFFF',
    borderRadius: 14,
    padding: 12,
    gap: 10,
  },

  textInput: {
    fontSize: 14,
    color: '#2A2B33',
    fontFamily: 'PingFang SC',
    lineHeight: 22,
    minHeight: 150,
    textAlignVertical: 'top',
  },

  inputActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 4,
  },

  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },

  actionButton: {
    backgroundColor: '#F2F3F5',
    borderRadius: 999,
    paddingHorizontal: 16,
    paddingVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },

  actionButtonText: {
    fontSize: 14,
    color: '#414352',
    fontFamily: 'PingFang SC',
    lineHeight: 22,
  },

  voiceActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },

  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F2F3F5',
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0, // 隐藏，保持布局
  },

  voiceButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    overflow: 'hidden',
  },

  voiceButtonGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },

  microphoneIcon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
  },

  settingsContainer: {
    flexDirection: 'row',
    gap: 4,
  },

  settingItem: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#EBEBF0',
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 10,
  },

  settingContent: {
    gap: 2,
  },

  settingLabel: {
    fontSize: 14,
    color: '#2A2B33',
    fontFamily: 'PingFang SC',
    lineHeight: 22,
  },

  settingValue: {
    fontSize: 10,
    color: '#9092A3',
    fontFamily: 'PingFang SC',
    lineHeight: 14,
  },

  generateButton: {
    backgroundColor: '#2E4BEB',
    borderRadius: 16,
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },

  generateButtonText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#FFFFFF',
    fontFamily: 'PingFang SC',
    lineHeight: 24,
  },

  inspirationSection: {
    marginTop: 24,
    gap: 16,
  },

  inspirationHeader: {
    paddingHorizontal: 16,
  },

  inspirationTitle: {
    fontSize: 12,
    color: '#9092A3',
    fontFamily: 'PingFang SC',
    lineHeight: 20,
    marginBottom: -20,
  },

  inspirationCarousel: {
    flexGrow: 0,
  },

  carouselContainer: {
    paddingHorizontal: 16,
  },

  inspirationCardContainer: {
    width: screenWidth - 32,
    position: 'relative',
  },

  innerContainer: {
    paddingTop: 16,
  },

  inspirationCard: {
    borderRadius: 16,
    padding: 16,
    gap: 8,
    marginRight: 0,
  },

  inspirationContent: {
    gap: 8,
  },

  inspirationPlatform: {
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'PingFang SC',
    lineHeight: 22,
  },

  inspirationText: {
    fontSize: 12,
    color: '#64677A',
    fontFamily: 'PingFang SC',
    lineHeight: 20,
  },

  inspirationIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
  },

  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.64)',
  },

  dotInactive: {
    backgroundColor: 'rgba(0, 0, 0, 0.16)',
  },

  topicImage: {
    position: 'absolute',
    top: -12,
    right: 0,
    width: 64,
    height: 64,
    borderRadius: 8,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },

  topicImageIcon: {
    width: 48,
    height: 48,
    resizeMode: 'contain',
  },

  // 选择器模态框样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },

  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: 34,
    maxHeight: '60%',
  },

  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EBEBF0',
  },

  modalTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2A2B33',
    fontFamily: 'PingFang SC',
  },

  closeButton: {
    padding: 4,
  },

  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F3F5',
  },

  selectedOption: {
    backgroundColor: '#F4F8FF',
  },

  optionText: {
    fontSize: 16,
    color: '#2A2B33',
    fontFamily: 'PingFang SC',
  },

  selectedOptionText: {
    color: '#2E4BEB',
    fontWeight: '500',
  },
});

export default WritingCreationScreen;
