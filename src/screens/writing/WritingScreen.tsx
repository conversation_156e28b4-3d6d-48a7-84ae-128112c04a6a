import React, { useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Image,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { colors, typography, spacing } from '@/styles';
import { useWritingStore } from '@/stores';
import { LinearGradient } from 'expo-linear-gradient';
import { SwipeListView } from 'react-native-swipe-list-view';
import { WritingContent } from '@/types';
import { RootStackParamList } from '@/types/navigation';
import { formatDate } from '@/utils';
import { DialogUtil } from '@/utils/dialogUtil';
import Toast from 'react-native-toast-message';

type WritingScreenNavigationProp = StackNavigationProp<RootStackParamList>;

// 计算响应式按钮尺寸，保持原始比例
const getButtonDimensions = () => {
  const screenWidth = Dimensions.get('window').width;
  const containerPadding = 32; // 左右边距总和
  const buttonGap = 8; // 按钮间距
  const availableWidth = screenWidth - containerPadding - buttonGap;

  // 根据设计稿调整比例，保持响应式
  const originalCreateWidth = 183;
  const originalOptimizeWidth = 184;
  const originalTotalWidth = originalCreateWidth + originalOptimizeWidth;
  const originalHeight = 76;

  // 计算缩放比例，但不放大超过原始尺寸
  const scale = Math.min(availableWidth / originalTotalWidth, 1);

  return {
    createWidth: originalCreateWidth * scale,
    optimizeWidth: originalOptimizeWidth * scale,
    height: originalHeight * scale,
    scale,
  };
};

export const WritingScreen: React.FC = () => {
  const { contents, deleteContent } = useWritingStore();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation<WritingScreenNavigationProp>();

  // 获取响应式按钮尺寸
  const buttonDimensions = getButtonDimensions();

  // 精确计算底部间距：让最后一个写作项的下边框恰好被Tab栏遮挡
  const TAB_BAR_HEIGHT = 56;
  const BORDER_OVERLAP = 1;
  const bottomPadding = TAB_BAR_HEIGHT + insets.bottom - BORDER_OVERLAP;

  // 处理后的写作内容数据
  const processedContents = useMemo(() => {
    return contents.map((content) => ({
      ...content,
      // 生成示例数据用于显示
      displayTitle: content.title || '春之韵，心之醉',
      displayDate: content.createdAt
        ? formatDate(content.createdAt)
        : '07-21 14:00',
    }));
  }, [contents]);

  // 示例数据（如果没有实际数据）
  const defaultItems = [
    {
      id: '1',
      title: '春之韵，心之醉',
      createdAt: new Date(),
      content: '示例内容',
      prompt: '',
      type: 'article' as const,
      wordCount: 500,
      language: 'zh' as const,
      style: 'creative' as const,
      updatedAt: new Date(),
      userId: 'user1',
      displayTitle: '春之韵，心之醉',
      displayDate: '07-21 14:00',
    },
    {
      id: '2',
      title: '春之韵，心之醉',
      createdAt: new Date(),
      content: '示例内容2',
      prompt: '',
      type: 'optimization' as const,
      wordCount: 300,
      language: 'zh' as const,
      style: 'professional' as const,
      updatedAt: new Date(),
      userId: 'user1',
      displayTitle: '春之韵，心之醉',
      displayDate: '07-21 14:00',
    },
    {
      id: '3',
      title: '春之韵，心之醉',
      createdAt: new Date(),
      content: '示例内容3',
      prompt: '',
      type: 'template' as const,
      wordCount: 800,
      language: 'zh' as const,
      style: 'formal' as const,
      updatedAt: new Date(),
      userId: 'user1',
      displayTitle: '春之韵，心之醉',
      displayDate: '07-21 14:00',
    },
  ];

  // 使用实际数据或示例数据
  const displayItems =
    processedContents.length > 0 ? processedContents : defaultItems;

  const handleCreatePress = () => {
    navigation.navigate('WritingCreation');
  };

  const handleOptimizePress = () => {
    // TODO: 导航到优化页面
  };

  const handleContentPress = (
    _content: WritingContent & { displayTitle: string; displayDate: string }
  ) => {
    // TODO: 查看写作内容详情
  };

  const handleRenameContent = (
    _content: WritingContent & { displayTitle: string; displayDate: string }
  ) => {
    // TODO: 重命名写作内容
  };

  const handleDeleteContent = (
    content: WritingContent & { displayTitle: string; displayDate: string }
  ) => {
    DialogUtil.alert('删除内容', '确定要删除这个写作内容吗？', () => {
      deleteContent(content.id);
      Toast.show({
        type: 'success',
        text1: '删除成功',
      });
    });
  };

  // 渲染写作内容项
  const renderContentItem = (data: {
    item: WritingContent & { displayTitle: string; displayDate: string };
  }) => (
    <TouchableOpacity
      style={styles.contentItemContainer}
      onPress={() => handleContentPress(data.item)}
    >
      <Text
        numberOfLines={1}
        ellipsizeMode="tail"
        style={styles.contentItemTitle}
      >
        {data.item.displayTitle}
      </Text>
      <View style={styles.contentItemDetails}>
        <Ionicons name="calendar-outline" size={12} color="#B0B2BF" />
        <Text style={styles.contentItemDate}>{data.item.displayDate}</Text>
      </View>
    </TouchableOpacity>
  );

  // 渲染滑动后的隐藏操作按钮
  const renderHiddenItem = (data: {
    item: WritingContent & { displayTitle: string; displayDate: string };
  }) => (
    <View style={styles.rowBack}>
      <TouchableOpacity
        style={[styles.backRightBtn, styles.backRightBtnLeft]}
        onPress={() => handleRenameContent(data.item)}
      >
        <View style={styles.editBtn}>
          <Image
            source={require('../../../assets/images/icons/icon-edit.png')}
            style={styles.ctrlBtn}
          />
        </View>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.backRightBtn, styles.backRightBtnRight]}
        onPress={() => handleDeleteContent(data.item)}
      >
        <View style={styles.deleteBtn}>
          <Image
            source={require('../../../assets/images/icons/icon-trash.png')}
            style={styles.ctrlBtn}
          />
        </View>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.writingSection}>
        {/* 按钮区 */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[
              styles.createActionButton,
              {
                width: buttonDimensions.createWidth,
                height: buttonDimensions.height,
              },
            ]}
            onPress={handleCreatePress}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={['#3053ED', 'rgba(82, 121, 255, 0.8)', '#3EDDB6']}
              locations={[0, 0.5, 1]}
              start={{ x: -0.12, y: -0.038 }}
              end={{ x: 1.024, y: 1.047 }}
              style={styles.gradientButton}
            >
              <View style={styles.textContainer}>
                <Text style={styles.createActionButtonText}>开始创作</Text>
                <Text style={styles.createActionButtonSubtext}>
                  灵感不枯竭！
                </Text>
              </View>
              <Image
                source={require('../../assets/images/writing/writing-icon.png')}
                style={styles.writingIcon}
                resizeMode="contain"
              />
            </LinearGradient>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.optimizeActionButton,
              {
                width: buttonDimensions.optimizeWidth,
                height: buttonDimensions.height,
              },
            ]}
            onPress={handleOptimizePress}
            activeOpacity={0.8}
          >
            <View style={styles.optimizeTextContainer}>
              <Text style={styles.optimizeActionButtonText}>文案优化</Text>
              <Text style={styles.optimizeActionButtonSubtext}>
                即刻妙笔生花！
              </Text>
            </View>
            <Image
              source={require('../../assets/images/writing/tips-icon.png')}
              style={styles.tipsIcon}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.writingContainer}>
        {/* Tab栏 */}
        <View style={styles.tabBarContainer}>
          <Text style={styles.tabBarText}>全部</Text>
          <View style={styles.tabBarIcon}>
            <TouchableOpacity>
              <Image
                source={require('../../../assets/images/icons/icon-search.png')}
                style={styles.extraBtn}
              />
            </TouchableOpacity>
            <TouchableOpacity>
              <Image
                source={require('../../../assets/images/icons/icon-filter.png')}
                style={styles.extraBtn}
              />
            </TouchableOpacity>
          </View>
        </View>
        {/* 写作内容列表 */}
        <View style={[styles.listContainer, { paddingBottom: bottomPadding }]}>
          {displayItems.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <Ionicons
                name="create-outline"
                size={48}
                color={colors.text.secondary}
              />
              <Text style={styles.emptyStateTitle}>暂无写作内容</Text>
              <Text style={styles.emptyStateSubtitle}>
                点击上方按钮开始创作或优化文案
              </Text>
            </View>
          ) : (
            <SwipeListView
              data={displayItems}
              renderItem={renderContentItem}
              renderHiddenItem={renderHiddenItem}
              rightOpenValue={-120}
              previewRowKey={undefined}
              previewOpenValue={0}
              style={styles.contentListContainer}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              disableRightSwipe
            />
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.bg,
    paddingTop: spacing.md,
  },

  writingSection: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.lg,
    backgroundColor: colors.background.bg,
  },

  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 16,
  },

  createActionButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: 'rgba(100, 103, 122, 0.06)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 4,
  },

  gradientButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 16,
    flex: 1,
    height: '100%',
  },

  writingIcon: {
    width: 32,
    height: 32,
  },

  textContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
  },

  createActionButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
    fontFamily: 'PingFang SC',
    lineHeight: 24,
    textAlign: 'center',
  },

  createActionButtonSubtext: {
    color: 'rgba(255, 255, 255, 0.72)',
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'PingFang SC',
    lineHeight: 20,
    textAlign: 'left',
  },

  optimizeActionButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    shadowColor: 'rgba(100, 103, 122, 0.06)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 4,
  },

  tipsIcon: {
    width: 32,
    height: 32,
  },

  optimizeTextContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
  },

  optimizeActionButtonText: {
    color: '#2A2B33',
    fontSize: 15,
    fontWeight: '600',
    fontFamily: 'PingFang SC',
    lineHeight: 24,
    textAlign: 'center',
  },

  optimizeActionButtonSubtext: {
    color: '#64677A',
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'PingFang SC',
    lineHeight: 20,
    textAlign: 'left',
  },

  writingContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    overflow: 'hidden',
    shadowColor: '#64677A',
    shadowOffset: { width: 0, height: -1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 3,
  },

  tabBarContainer: {
    flexDirection: 'row',
    marginLeft: 24,
    paddingTop: 12,
    marginBottom: 8,
    justifyContent: 'space-between',
    alignItems: 'center',
    marginRight: 24,
  },

  tabBarText: {
    fontSize: 16,
    fontWeight: typography.fontWeight.semibold,
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
    paddingBottom: 12,
  },

  tabBarIcon: {
    gap: spacing.md,
    flexDirection: 'row',
  },

  listContainer: {
    paddingHorizontal: 0,
    flex: 1,
  },

  emptyStateContainer: {
    alignItems: 'center',
    paddingVertical: 64,
  },

  emptyStateTitle: {
    fontSize: 18,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginTop: 16,
    marginBottom: 4,
  },

  emptyStateSubtitle: {
    fontSize: 14,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
  },

  contentListContainer: {
    flex: 1,
  },

  contentItemContainer: {
    backgroundColor: '#fff',
    borderBottomWidth: 0.5,
    borderBottomColor: '#EBEBF0',
    paddingHorizontal: 24,
    paddingVertical: 12,
  },

  rowBack: {
    alignItems: 'center',
    backgroundColor: '#F2F3F5',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingRight: 0,
  },

  backRightBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 60,
  },

  backRightBtnLeft: {
    right: 60,
  },

  backRightBtnRight: {
    right: 0,
  },

  editBtn: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    backgroundColor: '#fff',
  },

  deleteBtn: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    backgroundColor: '#FF615C',
  },

  contentItemTitle: {
    fontSize: 15,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: 8,
  },

  contentItemDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },

  contentItemDate: {
    fontSize: 12,
    color: '#B0B2BF',
    fontWeight: '400',
  },

  ctrlBtn: {
    width: 20,
    height: 20,
  },

  extraBtn: {
    width: 16,
    height: 16,
  },
});

export default WritingScreen;
