import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { colors, spacing, typography } from '@/styles';
import { useKnowledgeStore } from '@/stores';
import { KnowledgeDocument } from '@/types';
import Toast from 'react-native-toast-message';
import { DialogUtil } from '@/utils/dialogUtil';

interface Comment {
  id: string;
  documentId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  content: string;
  position?: {
    page?: number;
    x?: number;
    y?: number;
    selection?: string;
  };
  parentId?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface CollaborationActivity {
  id: string;
  documentId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  type: 'edit' | 'comment' | 'share' | 'view';
  description: string;
  createdAt: Date;
}

export const DocumentCollaborationScreen: React.FC = () => {
  const navigation = useNavigation();
  const { documents } = useKnowledgeStore();
  
  const [selectedTab, setSelectedTab] = useState<'comments' | 'activity'>('comments');
  const [comments, setComments] = useState<Comment[]>([]);
  const [activities, setActivities] = useState<CollaborationActivity[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [replyToComment, setReplyToComment] = useState<string | null>(null);

  useEffect(() => {
    loadCollaborationData();
  }, []);

  const loadCollaborationData = async () => {
    try {
      setIsLoading(true);
      
      // 模拟加载评论数据
      const mockComments: Comment[] = [
        {
          id: '1',
          documentId: 'doc1',
          userId: 'user1',
          userName: '张三',
          content: '这个方案很有创意，建议在第三部分增加一些具体的实施细节。',
          position: {
            page: 1,
            selection: '项目实施方案'
          },
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        },
        {
          id: '2',
          documentId: 'doc1',
          userId: 'user2',
          userName: '李四',
          content: '同意张三的建议，另外时间安排上可能需要调整。',
          parentId: '1',
          createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
        },
        {
          id: '3',
          documentId: 'doc2',
          userId: 'user3',
          userName: '王五',
          content: '数据分析部分的图表很清晰，但建议添加趋势预测。',
          position: {
            page: 2,
            selection: '数据分析'
          },
          createdAt: new Date(Date.now() - 30 * 60 * 1000),
          updatedAt: new Date(Date.now() - 30 * 60 * 1000),
        },
      ];

      // 模拟加载活动数据
      const mockActivities: CollaborationActivity[] = [
        {
          id: '1',
          documentId: 'doc1',
          userId: 'user1',
          userName: '张三',
          type: 'edit',
          description: '编辑了项目方案第3页',
          createdAt: new Date(Date.now() - 10 * 60 * 1000),
        },
        {
          id: '2',
          documentId: 'doc1',
          userId: 'user2',
          userName: '李四',
          type: 'comment',
          description: '在项目方案中添加了评论',
          createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
        },
        {
          id: '3',
          documentId: 'doc2',
          userId: 'user3',
          userName: '王五',
          type: 'share',
          description: '分享了数据报告给团队',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        },
        {
          id: '4',
          documentId: 'doc1',
          userId: 'user4',
          userName: '赵六',
          type: 'view',
          description: '查看了项目方案',
          createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
        },
      ];

      setComments(mockComments);
      setActivities(mockActivities);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '加载失败',
        text2: '无法加载协作信息',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCollaborationData();
    setRefreshing(false);
  };

  const handleAddComment = async () => {
    if (!newComment.trim()) {
      Toast.show({
        type: 'error',
        text1: '评论内容不能为空',
      });
      return;
    }

    try {
      const comment: Comment = {
        id: Date.now().toString(),
        documentId: 'current-doc',
        userId: 'current-user',
        userName: '当前用户',
        content: newComment.trim(),
        parentId: replyToComment || undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      setComments([comment, ...comments]);
      setNewComment('');
      setReplyToComment(null);

      Toast.show({
        type: 'success',
        text1: '评论已添加',
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '添加评论失败',
      });
    }
  };

  const handleDeleteComment = (commentId: string) => {
    DialogUtil.confirmDelete(
      '确认删除',
      '确定要删除这条评论吗？',
      () => {
        setComments(comments.filter(c => c.id !== commentId));
        Toast.show({
          type: 'success',
          text1: '评论已删除',
        });
      }
    );
  };

  const getRelativeTime = (date: Date): string => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    
    return date.toLocaleDateString('zh-CN');
  };

  const getActivityIcon = (type: CollaborationActivity['type']) => {
    switch (type) {
      case 'edit':
        return 'create';
      case 'comment':
        return 'chatbubble';
      case 'share':
        return 'share';
      case 'view':
        return 'eye';
      default:
        return 'ellipse';
    }
  };

  const getActivityColor = (type: CollaborationActivity['type']) => {
    switch (type) {
      case 'edit':
        return colors.warning;
      case 'comment':
        return colors.info;
      case 'share':
        return colors.success;
      case 'view':
        return colors.text.secondary;
      default:
        return colors.text.secondary;
    }
  };

  const renderComment = (comment: Comment, isReply = false) => {
    const replies = comments.filter(c => c.parentId === comment.id);
    
    return (
      <View key={comment.id} style={[styles.commentItem, isReply && styles.replyComment]}>
        <View style={styles.commentHeader}>
          <View style={styles.commentUser}>
            <View style={styles.userAvatar}>
              <Text style={styles.userAvatarText}>
                {comment.userName.charAt(0)}
              </Text>
            </View>
            <View style={styles.commentMeta}>
              <Text style={styles.userName}>{comment.userName}</Text>
              <Text style={styles.commentTime}>{getRelativeTime(comment.createdAt)}</Text>
            </View>
          </View>
          
          <View style={styles.commentActions}>
            <TouchableOpacity
              style={styles.commentAction}
              onPress={() => setReplyToComment(comment.id)}
            >
              <Ionicons name="chatbubble-outline" size={16} color={colors.text.secondary} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.commentAction}
              onPress={() => handleDeleteComment(comment.id)}
            >
              <Ionicons name="trash-outline" size={16} color={colors.error} />
            </TouchableOpacity>
          </View>
        </View>
        
        {comment.position?.selection && (
          <View style={styles.commentContext}>
            <Text style={styles.commentSelection}>"{comment.position.selection}"</Text>
          </View>
        )}
        
        <Text style={styles.commentContent}>{comment.content}</Text>
        
        {replies.length > 0 && (
          <View style={styles.repliesContainer}>
            {replies.map(reply => renderComment(reply, true))}
          </View>
        )}
        
        {replyToComment === comment.id && (
          <View style={styles.replyInput}>
            <TextInput
              style={styles.textInput}
              value={newComment}
              onChangeText={setNewComment}
              placeholder="回复评论..."
              multiline
              autoFocus
            />
            <View style={styles.replyActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => {
                  setReplyToComment(null);
                  setNewComment('');
                }}
              >
                <Text style={styles.cancelButtonText}>取消</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.submitButton}
                onPress={handleAddComment}
                disabled={!newComment.trim()}
              >
                <Text style={[styles.submitButtonText, !newComment.trim() && styles.disabled]}>
                  回复
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    );
  };

  const renderActivity = (activity: CollaborationActivity) => (
    <View key={activity.id} style={styles.activityItem}>
      <View style={[styles.activityIcon, { backgroundColor: getActivityColor(activity.type) + '20' }]}>
        <Ionicons
          name={getActivityIcon(activity.type) as any}
          size={16}
          color={getActivityColor(activity.type)}
        />
      </View>
      
      <View style={styles.activityContent}>
        <Text style={styles.activityDescription}>
          <Text style={styles.activityUser}>{activity.userName}</Text>
          {' '}
          {activity.description}
        </Text>
        <Text style={styles.activityTime}>{getRelativeTime(activity.createdAt)}</Text>
      </View>
    </View>
  );

  const renderCommentsTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.commentInput}>
        <TextInput
          style={styles.textInput}
          value={newComment}
          onChangeText={setNewComment}
          placeholder="添加评论..."
          multiline
        />
        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleAddComment}
          disabled={!newComment.trim()}
        >
          <Ionicons
            name="send"
            size={20}
            color={newComment.trim() ? colors.primary : colors.text.secondary}
          />
        </TouchableOpacity>
      </View>
      
      <ScrollView
        style={styles.commentsList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {comments.filter(c => !c.parentId).map(comment => renderComment(comment))}
        {comments.filter(c => !c.parentId).length === 0 && (
          <Text style={styles.emptyText}>暂无评论</Text>
        )}
      </ScrollView>
    </View>
  );

  const renderActivityTab = () => (
    <ScrollView
      style={styles.tabContent}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.activitiesList}>
        {activities.map(renderActivity)}
        {activities.length === 0 && (
          <Text style={styles.emptyText}>暂无活动记录</Text>
        )}
      </View>
    </ScrollView>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>加载协作信息...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>协作</Text>
        <View style={{ width: 24 }} />
      </View>
      
      <View style={styles.tabBar}>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'comments' && styles.activeTab]}
          onPress={() => setSelectedTab('comments')}
        >
          <Text style={[styles.tabText, selectedTab === 'comments' && styles.activeTabText]}>
            评论 ({comments.filter(c => !c.parentId).length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'activity' && styles.activeTab]}
          onPress={() => setSelectedTab('activity')}
        >
          <Text style={[styles.tabText, selectedTab === 'activity' && styles.activeTabText]}>
            活动 ({activities.length})
          </Text>
        </TouchableOpacity>
      </View>
      
      {selectedTab === 'comments' ? renderCommentsTab() : renderActivityTab()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.sm,
  },
  loadingText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: colors.background.secondary,
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  tabContent: {
    flex: 1,
  },
  commentInput: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    gap: spacing.sm,
  },
  textInput: {
    flex: 1,
    backgroundColor: colors.background.secondary,
    borderRadius: 20,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    maxHeight: 80,
  },
  submitButton: {
    padding: spacing.sm,
  },
  commentsList: {
    flex: 1,
  },
  commentItem: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  replyComment: {
    marginLeft: spacing.xl,
    paddingLeft: spacing.lg,
    borderLeftWidth: 2,
    borderLeftColor: colors.border,
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  commentUser: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  userAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userAvatarText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.background.primary,
  },
  commentMeta: {
    gap: spacing.xs,
  },
  userName: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
  },
  commentTime: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  commentActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  commentAction: {
    padding: spacing.xs,
  },
  commentContext: {
    backgroundColor: colors.background.secondary,
    padding: spacing.sm,
    borderRadius: 6,
    marginBottom: spacing.sm,
  },
  commentSelection: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    fontStyle: 'italic',
  },
  commentContent: {
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    lineHeight: typography.fontSize.md * 1.5,
  },
  repliesContainer: {
    marginTop: spacing.md,
  },
  replyInput: {
    marginTop: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
  },
  replyActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: spacing.sm,
    marginTop: spacing.sm,
  },
  cancelButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  cancelButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  submitButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  disabled: {
    opacity: 0.5,
  },
  activitiesList: {
    padding: spacing.lg,
    gap: spacing.md,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.md,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  activityContent: {
    flex: 1,
  },
  activityDescription: {
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  activityUser: {
    fontWeight: typography.fontWeight.medium as any,
  },
  activityTime: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  emptyText: {
    textAlign: 'center',
    color: colors.text.secondary,
    fontSize: typography.fontSize.sm,
    paddingVertical: spacing.xl,
  },
});