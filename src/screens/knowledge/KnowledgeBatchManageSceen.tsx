import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useKnowledgeStore } from '../../stores';
import { colors, typography, spacing } from '../../styles';
import { Button } from '../../components/common';

import { DialogUtil } from '@/utils/dialogUtil';
import { LinearGradient } from 'expo-linear-gradient';
import { KnowledgeCategory, KnowledgeDocument } from '@/types';
import { EditCategoryModal } from './EditCategoryModal';
import { lcdpService } from '@/services/api';
import Toast from 'react-native-toast-message';
interface DocumentItem extends Partial<KnowledgeDocument> {
  id: string;
  fileName: string;
  // 在 UI 中简化显示，只区分文档和音频
  displayType: 'document' | 'audio';
}

const KnowledgeBatchManageSceen = () => {
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const { documents, batchDeleteDocument, setDocuments } = useKnowledgeStore();
  const [editCategoryModalVisible, setEditCategoryModalVisible] =
    useState(false);

  // 将存储中的文档转换为UI需要的格式
  const mapDocumentsToUI = (): DocumentItem[] => {
    return documents.map((doc) => ({
      ...doc,
      displayType: ['mp3', 'wav', 'm4a', 'aac', 'ogg', 'flac'].includes(
        doc.fileType
      )
        ? 'audio'
        : 'document',
    }));
  };

  const toggleSelect = (id: string) => {
    setSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((sid) => sid !== id) : [...prev, id]
    );
  };

  const handleDelete = () => {
    if (selectedIds.length === 0) return;
    DialogUtil.alert(
      '删除知识',
      `确定要删除选中的${selectedIds.length}个知识吗？`,
      async () => {
        await batchDeleteDocument(selectedIds);
        setSelectedIds([]);
      }
    );
  };

  // 处理保存分类
  const handleSaveCategory = async (category: KnowledgeCategory) => {
    if (selectedIds.length) {
      // 更新文档的标题和其他相关信息
      await lcdpService.changeSpaceScene({
        space_ids: selectedIds.map((c) => ({ id: c })),
        scene_id: category.id,
      });
      setSelectedIds([]);

      setDocuments(
        documents.map((c) => ({
          ...c,
          sceneId: selectedIds.includes(c.id) ? category.id : c.sceneId,
        }))
      );
      Toast.show({
        text1: '修改成功',
      });
    }
  };

  // 处理关闭编辑分类模态框
  const handleCloseEditCategoryModal = () => {
    setEditCategoryModalVisible(false);
  };

  // 渲染文档列表项
  const renderDocumentItem = (data: { item: DocumentItem }) => {
    const selected = selectedIds.includes(data.item.id);
    return (
      <TouchableOpacity
        style={styles.documentItem}
        onPress={() => toggleSelect(data.item.id)}
      >
        <Ionicons
          name={selected ? 'checkbox' : 'square-outline'}
          size={24}
          color={selected ? colors.primary : colors.text.secondary}
          style={{ marginRight: 12 }}
        />
        <View style={styles.documentContent}>
          <View style={styles.documentIcon}>
            {data.item.displayType === 'document' ? (
              <LinearGradient
                colors={['#4080FF', '#94BFFF']}
                style={styles.documentIconBg}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Ionicons name="document-text" size={12} color={colors.white} />
              </LinearGradient>
            ) : (
              <LinearGradient
                colors={['#7570E7', '#B9B5FF']}
                style={styles.documentIconBg}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Ionicons name="mic" size={12} color={colors.white} />
              </LinearGradient>
            )}
          </View>
          <Text
            style={styles.documentTitle}
            ellipsizeMode="tail"
            numberOfLines={1}
          >
            {data.item.fileName}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={mapDocumentsToUI()}
        renderItem={renderDocumentItem}
        keyExtractor={(item) => item.id}
        extraData={selectedIds}
        contentContainerStyle={{ flexGrow: 1 }}
        ListEmptyComponent={<Text style={styles.empty}>暂无录音</Text>}
      />
      <View style={styles.footer}>
        <Button
          title="编辑分类"
          onPress={() => setEditCategoryModalVisible(true)}
          disabled={selectedIds.length === 0}
          style={styles.changeBtn}
          textStyle={styles.changeBtnText}
        />
        <Button
          title="删除"
          onPress={handleDelete}
          disabled={selectedIds.length === 0}
          style={styles.deleteBtn}
          textStyle={styles.deleteBtnText}
        />
      </View>
      {/* 编辑分类模态框 */}
      <EditCategoryModal
        visible={editCategoryModalVisible}
        onClose={handleCloseEditCategoryModal}
        onSave={handleSaveCategory}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: colors.separator,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.separator,
    backgroundColor: '#fff',
  },
  itemSelected: {
    backgroundColor: colors.primary + '10',
  },
  title: {
    fontSize: typography.fontSize.base,
    color: colors.text.primary,
    fontWeight: typography.fontWeight.medium,
  },
  footer: {
    padding: spacing.lg,
    backgroundColor: colors.background.secondary,
    borderTopWidth: 1,
    borderTopColor: colors.separator,
    flexDirection: 'row',
    gap: 8,
  },
  deleteBtn: {
    borderRadius: 8,
    backgroundColor: '#fff',
    flex: 1,
  },
  deleteBtnText: {
    color: '#F23030',
  },
  changeBtn: {
    borderRadius: 8,
    backgroundColor: colors.primary,
    flex: 1,
  },
  changeBtnText: {
    color: colors.background.primary,
  },
  empty: {
    textAlign: 'center',
    color: colors.text.secondary,
    marginTop: 40,
  },
  documentItem: {
    backgroundColor: colors.white,
    borderBottomWidth: 0.5,
    borderBottomColor: '#EBEBF0',
    paddingHorizontal: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
  },
  documentContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    paddingVertical: 12,
    paddingRight: spacing.lg,
  },
  documentIcon: {
    // 图标容器样式
  },
  documentIconBg: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  documentTitle: {
    flex: 1,
    fontSize: 15,
    color: colors.text.secondary,
    lineHeight: 24,
    fontFamily: typography.fontFamily,
  },
});

export default KnowledgeBatchManageSceen;
