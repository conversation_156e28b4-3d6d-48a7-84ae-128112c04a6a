import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors, spacing, typography } from '@/styles';
import { Button, Input } from '@/components/common';
import { KnowledgeCategory } from '@/types';
import { Ionicons } from '@expo/vector-icons';
import { useKnowledgeStore } from '@/stores';
import Toast from 'react-native-toast-message';
import { DialogUtil } from '@/utils/dialogUtil';

interface EditCategoryModalProps {
  visible: boolean;
  sceneId?: string;
  onClose: () => void;
  onSave: (category: KnowledgeCategory) => void;
}

export const EditCategoryModal: React.FC<EditCategoryModalProps> = ({
  visible,
  sceneId,
  onClose,
  onSave,
}) => {
  const { categories, addCategoryBySceneName, deleteCategory } =
    useKnowledgeStore();
  const [selected, setSelected] = useState<KnowledgeCategory | null>(null);
  const [sceneName, setSceneName] = useState('');
  const [loading, setLoading] = useState(false);
  const handleSave = async () => {
    if (selected) {
      await onSave(selected);
    }
    onClose();
  };
  const handleAddCategory = async () => {
    if (sceneName.trim()) {
      setLoading(true);
      try {
        await addCategoryBySceneName(sceneName);
      } finally {
        setLoading(false);
        setSceneName('');
      }
    } else {
      Toast.show({
        text1: '请输入分类名称',
      });
    }
  };

  const handleDeleteCategory = (category: KnowledgeCategory) => {
    if (category.id) {
      DialogUtil.confirmDelete('确定删除吗？', '删除后数据不可恢复', () => {
        deleteCategory(category.id);
      });
    }
  };

  useEffect(() => {
    if (visible) {
      if (sceneId) {
        setSelected(categories.find((c) => c.id === sceneId) || null);
      }
    } else {
      setSelected(null);
    }
  }, [visible]);

  return (
    <Modal visible={visible} animationType="fade" transparent={true}>
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalWrapper}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>编辑分类</Text>
            <TouchableOpacity onPress={onClose} style={styles.cancelButton}>
              <Ionicons name="close" style={styles.modalCancel} />
            </TouchableOpacity>
          </View>
          <View style={styles.inputWrapper}>
            <Input
              containerStyle={styles.inputContainer}
              inputStyle={styles.input}
              value={sceneName}
              onChangeText={setSceneName}
              placeholder="请输入分类"
            />
            <Button
              title="添加"
              style={styles.addButton}
              textStyle={styles.addButtonText}
              onPress={handleAddCategory}
              loading={loading}
            />
          </View>

          <ScrollView
            style={styles.modalContent}
            showsVerticalScrollIndicator={false}
          >
            {categories.length ? (
              <View style={styles.categoryList}>
                {categories.map((c) => (
                  <View key={c.id} style={styles.categoryItemWrapper}>
                    <Button
                      style={[
                        styles.categoryItem,
                        c.id === selected?.id ? styles.selected : {},
                      ]}
                      onPress={() => setSelected(c)}
                    >
                      <Text style={styles.categoryItemText}>{c.sceneName}</Text>
                    </Button>
                    {!['文档', '音频'].includes(c.sceneName) && (
                      <Ionicons
                        name="remove-circle"
                        style={styles.removeAction}
                        onPress={() => handleDeleteCategory(c)}
                      />
                    )}
                  </View>
                ))}
              </View>
            ) : (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyTitle}>没有目录，请新增一个</Text>
              </View>
            )}
          </ScrollView>
          <View style={styles.saveContainer}>
            <Button
              title="确定"
              style={styles.modalSave}
              textStyle={styles.addButtonText}
              onPress={handleSave}
            />
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  inputWrapper: {
    padding: spacing.md,
    paddingBottom: 0,
    flexDirection: 'row',
  },
  inputContainer: {
    flex: 1,
    marginRight: spacing.sm,
  },
  input: {
    borderRadius: 30,
    width: '100%',
    padding: 12,
  },
  categoryList: {
    flexDirection: 'row',
    gap: spacing.md,
    flexWrap: 'wrap',
  },
  categoryItem: {
    backgroundColor: colors.background.primary,
    borderRadius: spacing.lg,
    borderColor: colors.border,
    marginBottom: spacing.xs,
    borderWidth: 1,
    overflow: 'hidden',
    minWidth: '30%',
    paddingVertical: spacing.md,
  },
  selected: {
    borderColor: colors.primary,
  },
  categoryItemText: {
    fontSize: typography.fontSize.base,
  },
  categoryItemWrapper: {
    position: 'relative',
  },
  removeAction: {
    fontSize: typography.fontSize['2xl'],
    color: 'red',
    position: 'absolute',
    right: -6,
    top: -6,
    zIndex: 1000,
    backgroundColor: '#fff',
  },
  addButton: {
    backgroundColor: colors.primary,
    marginBottom: spacing.md,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 30,
  },
  addButtonText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.white,
  },
  saveContainer: {},
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
    gap: spacing.sm,
  },
  emptyButtonText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.white,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    // alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  modalWrapper: {
    height: '70%',
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  cancelButton: {
    position: 'absolute',
    right: spacing.md,
  },
  modalCancel: {
    fontSize: typography.fontSize.xl,
    color: colors.text.secondary,
  },
  modalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  modalSave: {
    marginHorizontal: spacing.lg,
    backgroundColor: colors.primary,
    borderRadius: spacing.md,
    marginBottom: spacing.md,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: spacing.md,
    paddingTop: spacing.xs,
    marginBottom: spacing.md,
  },
  optionGroup: {
    marginBottom: spacing.xl,
  },
  optionLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  colorOptionSelected: {
    borderColor: colors.border,
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  iconOption: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  previewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    padding: spacing.md,
    borderRadius: 12,
    gap: spacing.md,
  },
  previewIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewInfo: {
    flex: 1,
  },
  previewName: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  previewDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
});
