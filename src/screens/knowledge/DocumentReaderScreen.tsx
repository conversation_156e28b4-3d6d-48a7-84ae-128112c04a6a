import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Share,
  Modal,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { colors, spacing, typography } from '@/styles';
import { useKnowledgeStore } from '@/stores';
import { KnowledgeDocument } from '@/types';
import Toast from 'react-native-toast-message';
import { DialogUtil } from '@/utils/dialogUtil';

const { width: screenWidth } = Dimensions.get('window');

interface ToolbarItem {
  icon: string;
  action: () => void;
  active?: boolean;
}

export const DocumentReaderScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { documentId } = route.params as { documentId: string };
  const { documents, updateDocument } = useKnowledgeStore();
  
  const [document, setDocument] = useState<KnowledgeDocument | null>(null);
  const [fontSize, setFontSize] = useState(16);
  const [readingMode, setReadingMode] = useState<'light' | 'dark' | 'sepia'>('light');
  const [showToolbar, setShowToolbar] = useState(true);
  const [showTableOfContents, setShowTableOfContents] = useState(false);
  const [currentSection, setCurrentSection] = useState(0);
  const [bookmarks, setBookmarks] = useState<number[]>([]);
  const [notes, setNotes] = useState<{[position: number]: string}>({});
  const [showNoteModal, setShowNoteModal] = useState(false);
  const [notePosition, setNotePosition] = useState(0);
  const [noteText, setNoteText] = useState('');

  useEffect(() => {
    const doc = documents.find(d => d.id === documentId);
    if (doc) {
      setDocument(doc);
      // 模拟解析文档章节
      generateTableOfContents(doc.content);
    } else {
      DialogUtil.error('文档不存在', () => navigation.goBack());
    }
  }, [documentId, documents]);

  const generateTableOfContents = (content: string) => {
    // 简单的目录生成逻辑
    const sections = content.split('\n').filter(line => 
      line.startsWith('#') || line.length > 50
    );
    // 这里可以根据实际需求实现更复杂的目录生成
  };

  const getReadingModeStyles = () => {
    switch (readingMode) {
      case 'dark':
        return {
          backgroundColor: '#1a1a1a',
          textColor: '#ffffff',
        };
      case 'sepia':
        return {
          backgroundColor: '#f4f3e8',
          textColor: '#5c4b37',
        };
      default:
        return {
          backgroundColor: colors.background.primary,
          textColor: colors.text.primary,
        };
    }
  };

  const handleFontSizeChange = (delta: number) => {
    const newSize = Math.max(12, Math.min(24, fontSize + delta));
    setFontSize(newSize);
  };

  const handleAddBookmark = () => {
    const position = currentSection;
    if (!bookmarks.includes(position)) {
      setBookmarks([...bookmarks, position]);
      Toast.show({
        type: 'success',
        text1: '书签已添加',
      });
    }
  };

  const handleRemoveBookmark = (position: number) => {
    setBookmarks(bookmarks.filter(b => b !== position));
    Toast.show({
      type: 'success',
      text1: '书签已移除',
    });
  };

  const handleAddNote = () => {
    setNotePosition(currentSection);
    setNoteText(notes[currentSection] || '');
    setShowNoteModal(true);
  };

  const handleSaveNote = () => {
    if (noteText.trim()) {
      setNotes({ ...notes, [notePosition]: noteText.trim() });
      Toast.show({
        type: 'success',
        text1: '笔记已保存',
      });
    } else {
      const newNotes = { ...notes };
      delete newNotes[notePosition];
      setNotes(newNotes);
    }
    setShowNoteModal(false);
    setNoteText('');
  };

  const handleShare = async () => {
    try {
      await Share.share({
        title: document?.title,
        message: `${document?.title}\n\n${document?.content?.substring(0, 200)}...`,
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '分享失败',
      });
    }
  };

  const handleToggleReadingMode = () => {
    const modes: Array<'light' | 'dark' | 'sepia'> = ['light', 'dark', 'sepia'];
    const currentIndex = modes.indexOf(readingMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setReadingMode(modes[nextIndex]);
  };

  const modeStyles = getReadingModeStyles();

  const toolbarItems: ToolbarItem[] = [
    {
      icon: 'remove',
      action: () => handleFontSizeChange(-2),
    },
    {
      icon: 'add',
      action: () => handleFontSizeChange(2),
    },
    {
      icon: 'color-palette',
      action: handleToggleReadingMode,
    },
    {
      icon: 'bookmark',
      action: handleAddBookmark,
      active: bookmarks.includes(currentSection),
    },
    {
      icon: 'create',
      action: handleAddNote,
      active: !!notes[currentSection],
    },
    {
      icon: 'list',
      action: () => setShowTableOfContents(true),
    },
    {
      icon: 'share',
      action: handleShare,
    },
  ];

  const renderToolbar = () => (
    <View style={[styles.toolbar, { backgroundColor: modeStyles.backgroundColor }]}>
      {toolbarItems.map((item, index) => (
        <TouchableOpacity
          key={index}
          style={[styles.toolbarButton, item.active && styles.toolbarButtonActive]}
          onPress={item.action}
        >
          <Ionicons
            name={item.icon as any}
            size={20}
            color={item.active ? colors.primary : modeStyles.textColor}
          />
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderTableOfContents = () => (
    <Modal
      visible={showTableOfContents}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.tocContainer}>
        <View style={styles.tocHeader}>
          <Text style={styles.tocTitle}>目录</Text>
          <TouchableOpacity onPress={() => setShowTableOfContents(false)}>
            <Ionicons name="close" size={24} color={colors.text.primary} />
          </TouchableOpacity>
        </View>
        
        <ScrollView style={styles.tocContent}>
          {/* 模拟目录项 */}
          {[
            '第一章 引言',
            '第二章 背景分析',
            '第三章 解决方案',
            '第四章 实施计划',
            '第五章 总结',
          ].map((title, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.tocItem, currentSection === index && styles.tocItemActive]}
              onPress={() => {
                setCurrentSection(index);
                setShowTableOfContents(false);
              }}
            >
              <Text style={[
                styles.tocItemText,
                currentSection === index && styles.tocItemTextActive
              ]}>
                {title}
              </Text>
              {bookmarks.includes(index) && (
                <Ionicons name="bookmark" size={16} color={colors.primary} />
              )}
              {notes[index] && (
                <Ionicons name="create" size={16} color={colors.warning} />
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );

  const renderNoteModal = () => (
    <Modal
      visible={showNoteModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.noteModalContainer}>
        <View style={styles.noteModalHeader}>
          <TouchableOpacity onPress={() => setShowNoteModal(false)}>
            <Text style={styles.noteModalCancel}>取消</Text>
          </TouchableOpacity>
          <Text style={styles.noteModalTitle}>笔记</Text>
          <TouchableOpacity onPress={handleSaveNote}>
            <Text style={styles.noteModalSave}>保存</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.noteModalContent}>
          <TextInput
            style={styles.noteInput}
            value={noteText}
            onChangeText={setNoteText}
            placeholder="在此添加笔记..."
            multiline
            autoFocus
          />
        </View>
      </SafeAreaView>
    </Modal>
  );

  if (!document) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="document-text" size={64} color={colors.text.secondary} />
          <Text style={styles.errorText}>文档加载失败</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: modeStyles.backgroundColor }]}>
      {/* 顶部导航 */}
      <View style={[styles.header, { backgroundColor: modeStyles.backgroundColor }]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={modeStyles.textColor} />
        </TouchableOpacity>
        
        <View style={styles.headerCenter}>
          <Text style={[styles.headerTitle, { color: modeStyles.textColor }]} numberOfLines={1}>
            {document.title}
          </Text>
          <Text style={[styles.headerSubtitle, { color: modeStyles.textColor }]}>
            {new Date(document.updatedAt).toLocaleDateString('zh-CN')}
          </Text>
        </View>
        
        <TouchableOpacity 
          onPress={() => navigation.navigate('DocumentEditor' as any, { documentId })}
        >
          <Ionicons name="create" size={24} color={modeStyles.textColor} />
        </TouchableOpacity>
      </View>

      {/* 阅读区域 */}
      <ScrollView 
        style={styles.content}
        onScroll={() => setShowToolbar(true)}
        scrollEventThrottle={100}
      >
        <View style={styles.documentContent}>
          <Text style={[
            styles.documentTitle,
            { 
              color: modeStyles.textColor,
              fontSize: fontSize + 8
            }
          ]}>
            {document.title}
          </Text>
          
          <View style={styles.documentMeta}>
            <Text style={[styles.metaText, { color: modeStyles.textColor }]}>
              {document.type.toUpperCase()} • {new Date(document.updatedAt).toLocaleDateString('zh-CN')}
            </Text>
            {document.tags.length > 0 && (
              <View style={styles.tags}>
                {document.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
          
          <Text style={[
            styles.documentText,
            { 
              color: modeStyles.textColor,
              fontSize: fontSize,
              lineHeight: fontSize * 1.6
            }
          ]}>
            {document.content}
          </Text>

          {/* 显示笔记 */}
          {notes[currentSection] && (
            <View style={styles.noteSection}>
              <Text style={[styles.noteSectionTitle, { color: modeStyles.textColor }]}>
                笔记
              </Text>
              <View style={styles.noteBox}>
                <Text style={styles.noteBoxText}>{notes[currentSection]}</Text>
              </View>
            </View>
          )}
        </View>
      </ScrollView>

      {/* 工具栏 */}
      {showToolbar && renderToolbar()}

      {/* 模态框 */}
      {renderTableOfContents()}
      {renderNoteModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: spacing.md,
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.sm,
    opacity: 0.7,
    marginTop: spacing.xs,
  },
  content: {
    flex: 1,
  },
  documentContent: {
    padding: spacing.lg,
  },
  documentTitle: {
    fontWeight: typography.fontWeight.bold as any,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  documentMeta: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  metaText: {
    fontSize: typography.fontSize.sm,
    opacity: 0.7,
    marginBottom: spacing.sm,
  },
  tags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.xs,
  },
  tag: {
    backgroundColor: colors.primary + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  tagText: {
    fontSize: typography.fontSize.xs,
    color: colors.primary,
  },
  documentText: {
    textAlign: 'justify',
  },
  noteSection: {
    marginTop: spacing.xl,
    padding: spacing.md,
    backgroundColor: colors.warning + '10',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.warning,
  },
  noteSectionTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    marginBottom: spacing.sm,
  },
  noteBox: {
    backgroundColor: colors.background.secondary,
    padding: spacing.md,
    borderRadius: 6,
  },
  noteBoxText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.primary,
  },
  toolbar: {
    position: 'absolute',
    bottom: spacing.lg,
    left: spacing.lg,
    right: spacing.lg,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: spacing.md,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  toolbarButton: {
    padding: spacing.sm,
    borderRadius: 20,
  },
  toolbarButtonActive: {
    backgroundColor: colors.primary + '20',
  },
  tocContainer: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  tocHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tocTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  tocContent: {
    flex: 1,
    padding: spacing.lg,
  },
  tocItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    borderRadius: 8,
    marginBottom: spacing.xs,
    gap: spacing.sm,
  },
  tocItemActive: {
    backgroundColor: colors.primary + '10',
  },
  tocItemText: {
    flex: 1,
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
  },
  tocItemTextActive: {
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  noteModalContainer: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  noteModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  noteModalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  noteModalCancel: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  noteModalSave: {
    fontSize: typography.fontSize.md,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  noteModalContent: {
    flex: 1,
    padding: spacing.lg,
  },
  noteInput: {
    flex: 1,
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    padding: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    textAlignVertical: 'top',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.lg,
  },
  errorText: {
    fontSize: typography.fontSize.lg,
    color: colors.text.secondary,
  },
});