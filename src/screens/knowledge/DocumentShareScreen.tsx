import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  Share,
  Switch,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { colors, spacing, typography } from '@/styles';
import { useKnowledgeStore } from '@/stores';
import { KnowledgeDocument } from '@/types';
import Toast from 'react-native-toast-message';

interface SharePermission {
  id: string;
  email: string;
  name: string;
  role: 'viewer' | 'editor' | 'admin';
  avatar?: string;
  addedAt: Date;
}

interface ShareSettings {
  isPublic: boolean;
  allowDownload: boolean;
  allowComment: boolean;
  expiresAt?: Date;
  password?: string;
}

export const DocumentShareScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { documentId } = route.params as { documentId: string };
  const { documents, updateDocument } = useKnowledgeStore();
  
  const [document, setDocument] = useState<KnowledgeDocument | null>(null);
  const [sharePermissions, setSharePermissions] = useState<SharePermission[]>([]);
  const [shareSettings, setShareSettings] = useState<ShareSettings>({
    isPublic: false,
    allowDownload: true,
    allowComment: true,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserRole, setNewUserRole] = useState<'viewer' | 'editor'>('viewer');

  useEffect(() => {
    const doc = documents.find(d => d.id === documentId);
    if (doc) {
      setDocument(doc);
      loadShareData();
    } else {
      Alert.alert('错误', '文档不存在', [
        { text: '确定', onPress: () => navigation.goBack() }
      ]);
    }
  }, [documentId, documents]);

  const loadShareData = async () => {
    try {
      setIsLoading(true);
      // 模拟加载分享数据
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟数据
      const mockPermissions: SharePermission[] = [
        {
          id: '1',
          email: '<EMAIL>',
          name: '张三',
          role: 'editor',
          addedAt: new Date(),
        },
        {
          id: '2',
          email: '<EMAIL>',
          name: '李四',
          role: 'viewer',
          addedAt: new Date(),
        },
      ];
      
      setSharePermissions(mockPermissions);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '加载失败',
        text2: '无法加载分享信息',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddUser = async () => {
    if (!newUserEmail.trim()) {
      Toast.show({
        type: 'error',
        text1: '邮箱不能为空',
      });
      return;
    }

    if (!newUserEmail.includes('@')) {
      Toast.show({
        type: 'error',
        text1: '请输入有效的邮箱地址',
      });
      return;
    }

    try {
      setIsLoading(true);
      
      // 检查用户是否已经存在
      const existingUser = sharePermissions.find(p => p.email === newUserEmail);
      if (existingUser) {
        Toast.show({
          type: 'error',
          text1: '用户已存在',
          text2: '该用户已具有访问权限',
        });
        return;
      }

      const newPermission: SharePermission = {
        id: Date.now().toString(),
        email: newUserEmail,
        name: newUserEmail.split('@')[0],
        role: newUserRole,
        addedAt: new Date(),
      };

      setSharePermissions([...sharePermissions, newPermission]);
      setNewUserEmail('');
      setShowAddUserModal(false);
      
      Toast.show({
        type: 'success',
        text1: '添加成功',
        text2: `已邀请 ${newUserEmail}`,
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '添加失败',
        text2: '无法添加用户',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveUser = (permissionId: string) => {
    const permission = sharePermissions.find(p => p.id === permissionId);
    if (!permission) return;

    Alert.alert(
      '确认删除',
      `确定要移除 ${permission.name} 的访问权限吗？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => {
            setSharePermissions(sharePermissions.filter(p => p.id !== permissionId));
            Toast.show({
              type: 'success',
              text1: '已移除用户',
            });
          },
        },
      ]
    );
  };

  const handleChangeUserRole = (permissionId: string, newRole: 'viewer' | 'editor') => {
    setSharePermissions(sharePermissions.map(p => 
      p.id === permissionId ? { ...p, role: newRole } : p
    ));
    Toast.show({
      type: 'success',
      text1: '权限已更新',
    });
  };

  const handleShareDocument = async () => {
    try {
      const shareUrl = `https://app.example.com/document/${documentId}`;
      await Share.share({
        title: document?.title,
        message: `查看文档：${document?.title}\n${shareUrl}`,
        url: shareUrl,
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '分享失败',
      });
    }
  };

  const handleCopyLink = () => {
    const shareUrl = `https://app.example.com/document/${documentId}`;
    // 在实际应用中使用 Clipboard.setString(shareUrl)
    Toast.show({
      type: 'success',
      text1: '链接已复制',
      text2: shareUrl,
    });
  };

  const renderPermissionItem = (permission: SharePermission) => (
    <View key={permission.id} style={styles.permissionItem}>
      <View style={styles.userInfo}>
        <View style={styles.avatar}>
          <Text style={styles.avatarText}>
            {permission.name.charAt(0).toUpperCase()}
          </Text>
        </View>
        <View style={styles.userDetails}>
          <Text style={styles.userName}>{permission.name}</Text>
          <Text style={styles.userEmail}>{permission.email}</Text>
        </View>
      </View>
      
      <View style={styles.permissionActions}>
        <TouchableOpacity
          style={[
            styles.roleButton,
            permission.role === 'viewer' && styles.roleButtonActive
          ]}
          onPress={() => handleChangeUserRole(permission.id, 'viewer')}
        >
          <Text style={[
            styles.roleButtonText,
            permission.role === 'viewer' && styles.roleButtonTextActive
          ]}>
            查看
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.roleButton,
            permission.role === 'editor' && styles.roleButtonActive
          ]}
          onPress={() => handleChangeUserRole(permission.id, 'editor')}
        >
          <Text style={[
            styles.roleButtonText,
            permission.role === 'editor' && styles.roleButtonTextActive
          ]}>
            编辑
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => handleRemoveUser(permission.id)}
        >
          <Ionicons name="close" size={16} color={colors.error} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderAddUserModal = () => (
    <Modal
      visible={showAddUserModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={() => setShowAddUserModal(false)}>
            <Text style={styles.modalCancel}>取消</Text>
          </TouchableOpacity>
          <Text style={styles.modalTitle}>添加用户</Text>
          <TouchableOpacity onPress={handleAddUser} disabled={isLoading}>
            <Text style={[styles.modalDone, isLoading && styles.disabled]}>
              {isLoading ? '添加中...' : '完成'}
            </Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.modalContent}>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>邮箱地址</Text>
            <TextInput
              style={styles.textInput}
              value={newUserEmail}
              onChangeText={setNewUserEmail}
              placeholder="输入用户邮箱"
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>权限</Text>
            <View style={styles.roleSelector}>
              <TouchableOpacity
                style={[
                  styles.roleSelectorButton,
                  newUserRole === 'viewer' && styles.roleSelectorButtonActive
                ]}
                onPress={() => setNewUserRole('viewer')}
              >
                <Text style={[
                  styles.roleSelectorText,
                  newUserRole === 'viewer' && styles.roleSelectorTextActive
                ]}>
                  查看者
                </Text>
                <Text style={styles.roleDescription}>只能查看文档</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.roleSelectorButton,
                  newUserRole === 'editor' && styles.roleSelectorButtonActive
                ]}
                onPress={() => setNewUserRole('editor')}
              >
                <Text style={[
                  styles.roleSelectorText,
                  newUserRole === 'editor' && styles.roleSelectorTextActive
                ]}>
                  编辑者
                </Text>
                <Text style={styles.roleDescription}>可以查看和编辑文档</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );

  if (!document) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>加载文档信息...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>分享文档</Text>
        <TouchableOpacity onPress={() => setShowShareModal(true)}>
          <Ionicons name="settings" size={24} color={colors.text.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* 文档信息 */}
        <View style={styles.documentInfo}>
          <Ionicons
            name={document.type === 'pdf' ? 'document-text' : 'document'}
            size={32}
            color={colors.primary}
          />
          <View style={styles.documentDetails}>
            <Text style={styles.documentTitle}>{document.title}</Text>
            <Text style={styles.documentMeta}>
              {new Date(document.updatedAt).toLocaleDateString('zh-CN')} • {document.type.toUpperCase()}
            </Text>
          </View>
        </View>

        {/* 快速分享 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>快速分享</Text>
          <View style={styles.quickShareActions}>
            <TouchableOpacity style={styles.quickShareButton} onPress={handleShareDocument}>
              <Ionicons name="share" size={24} color={colors.primary} />
              <Text style={styles.quickShareText}>分享链接</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.quickShareButton} onPress={handleCopyLink}>
              <Ionicons name="copy" size={24} color={colors.primary} />
              <Text style={styles.quickShareText}>复制链接</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.quickShareButton}>
              <Ionicons name="qr-code" size={24} color={colors.primary} />
              <Text style={styles.quickShareText}>二维码</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 协作用户 */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>协作用户</Text>
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => setShowAddUserModal(true)}
            >
              <Ionicons name="add" size={20} color={colors.primary} />
              <Text style={styles.addButtonText}>添加</Text>
            </TouchableOpacity>
          </View>
          
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={styles.loadingText}>加载用户列表...</Text>
            </View>
          ) : (
            <View style={styles.permissionsList}>
              {sharePermissions.map(renderPermissionItem)}
              {sharePermissions.length === 0 && (
                <Text style={styles.emptyText}>暂无协作用户</Text>
              )}
            </View>
          )}
        </View>

        {/* 分享设置 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>分享设置</Text>
          <View style={styles.settingsGroup}>
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>公开访问</Text>
                <Text style={styles.settingDescription}>
                  任何人都可以通过链接访问
                </Text>
              </View>
              <Switch
                value={shareSettings.isPublic}
                onValueChange={(value) => 
                  setShareSettings({...shareSettings, isPublic: value})
                }
                trackColor={{ false: colors.border, true: colors.primary + '30' }}
                thumbColor={shareSettings.isPublic ? colors.primary : colors.text.secondary}
              />
            </View>
            
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>允许下载</Text>
                <Text style={styles.settingDescription}>
                  用户可以下载文档副本
                </Text>
              </View>
              <Switch
                value={shareSettings.allowDownload}
                onValueChange={(value) => 
                  setShareSettings({...shareSettings, allowDownload: value})
                }
                trackColor={{ false: colors.border, true: colors.primary + '30' }}
                thumbColor={shareSettings.allowDownload ? colors.primary : colors.text.secondary}
              />
            </View>
            
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>允许评论</Text>
                <Text style={styles.settingDescription}>
                  用户可以在文档中添加评论
                </Text>
              </View>
              <Switch
                value={shareSettings.allowComment}
                onValueChange={(value) => 
                  setShareSettings({...shareSettings, allowComment: value})
                }
                trackColor={{ false: colors.border, true: colors.primary + '30' }}
                thumbColor={shareSettings.allowComment ? colors.primary : colors.text.secondary}
              />
            </View>
          </View>
        </View>
      </ScrollView>

      {renderAddUserModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.sm,
  },
  loadingText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  documentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
    backgroundColor: colors.background.secondary,
    margin: spacing.lg,
    borderRadius: 12,
    gap: spacing.md,
  },
  documentDetails: {
    flex: 1,
  },
  documentTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  documentMeta: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  section: {
    margin: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  addButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
  },
  quickShareActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  quickShareButton: {
    alignItems: 'center',
    gap: spacing.sm,
  },
  quickShareText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  permissionsList: {
    gap: spacing.sm,
  },
  permissionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing.md,
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: spacing.md,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.background.primary,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
  },
  userEmail: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  permissionActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  roleButton: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 6,
    backgroundColor: colors.background.tertiary,
  },
  roleButtonActive: {
    backgroundColor: colors.primary,
  },
  roleButtonText: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  roleButtonTextActive: {
    color: colors.background.primary,
  },
  removeButton: {
    padding: spacing.xs,
  },
  settingsGroup: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  settingDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  emptyText: {
    textAlign: 'center',
    color: colors.text.secondary,
    fontSize: typography.fontSize.sm,
    paddingVertical: spacing.xl,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  modalCancel: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  modalDone: {
    fontSize: typography.fontSize.md,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  disabled: {
    opacity: 0.5,
  },
  modalContent: {
    flex: 1,
    padding: spacing.lg,
  },
  inputGroup: {
    marginBottom: spacing.lg,
  },
  inputLabel: {
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  textInput: {
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    padding: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
  },
  roleSelector: {
    gap: spacing.sm,
  },
  roleSelectorButton: {
    padding: spacing.md,
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  roleSelectorButtonActive: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  },
  roleSelectorText: {
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    fontWeight: typography.fontWeight.medium as any,
    marginBottom: spacing.xs,
  },
  roleSelectorTextActive: {
    color: colors.primary,
  },
  roleDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
});