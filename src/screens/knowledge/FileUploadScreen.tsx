import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import { colors, spacing, typography } from '@/styles';
import { Button, Input } from '@/components/common';
import { useKnowledgeStore } from '@/stores';
import { KnowledgeDocument } from '@/types';
import Toast from 'react-native-toast-message';
import { useNavigation } from '@react-navigation/native';

interface UploadProgress {
  id: string;
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
}

export const FileUploadScreen: React.FC = () => {
  const navigation = useNavigation();
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
  const [uploading, setUploading] = useState(false);
  const [documentTitle, setDocumentTitle] = useState('');
  const [documentDescription, setDocumentDescription] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [documentTags, setDocumentTags] = useState('');
  
  const { addDocument, categories } = useKnowledgeStore();

  const handleDocumentUpload = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-powerpoint',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        ],
        multiple: true,
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets.length > 0) {
        await processFiles(result.assets);
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '上传失败',
        text2: '无法选择文档文件',
      });
    }
  };

  const handleAudioUpload = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['audio/*'],
        multiple: true,
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets.length > 0) {
        await processFiles(result.assets);
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '上传失败',
        text2: '无法选择音频文件',
      });
    }
  };

  const handleImageUpload = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        quality: 0.8,
      });

      if (!result.canceled && result.assets.length > 0) {
        const assets = result.assets.map(asset => ({
          name: asset.fileName || `image_${Date.now()}.jpg`,
          uri: asset.uri,
          size: asset.fileSize || 0,
          mimeType: asset.type || 'image/jpeg',
        }));
        await processFiles(assets);
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '上传失败',
        text2: '无法选择图片文件',
      });
    }
  };

  const processFiles = async (assets: any[]) => {
    setUploading(true);
    const newProgressItems: UploadProgress[] = assets.map(asset => ({
      id: Date.now().toString() + Math.random().toString(),
      fileName: asset.name,
      progress: 0,
      status: 'uploading' as const,
    }));
    
    setUploadProgress(prev => [...prev, ...newProgressItems]);

    try {
      for (let i = 0; i < assets.length; i++) {
        const asset = assets[i];
        const progressId = newProgressItems[i].id;

        // 模拟上传进度
        for (let progress = 0; progress <= 100; progress += 20) {
          await new Promise(resolve => setTimeout(resolve, 200));
          setUploadProgress(prev => 
            prev.map(item => 
              item.id === progressId 
                ? { ...item, progress }
                : item
            )
          );
        }

        const newDocument: KnowledgeDocument = {
          id: Date.now().toString() + i,
          title: documentTitle || asset.name,
          content: '',
          description: documentDescription,
          type: getDocumentType(asset.mimeType || ''),
          size: asset.size || 0,
          filePath: asset.uri,
          categoryId: selectedCategory || undefined,
          tags: documentTags.split(',').map(tag => tag.trim()).filter(Boolean),
          createdAt: new Date(),
          updatedAt: new Date(),
          userId: 'user1',
        };

        addDocument(newDocument);

        setUploadProgress(prev => 
          prev.map(item => 
            item.id === progressId 
              ? { ...item, status: 'completed' }
              : item
          )
        );
      }

      Toast.show({
        type: 'success',
        text1: '上传成功',
        text2: `已成功上传 ${assets.length} 个文件`,
      });

      // 清空表单
      setDocumentTitle('');
      setDocumentDescription('');
      setDocumentTags('');
      setSelectedCategory('');

    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '上传失败',
        text2: '文件上传过程中出现错误',
      });
    } finally {
      setUploading(false);
      setTimeout(() => {
        setUploadProgress([]);
      }, 2000);
    }
  };

  const getDocumentType = (mimeType: string): KnowledgeDocument['type'] => {
    if (mimeType.includes('pdf')) return 'pdf';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'word';
    if (mimeType.includes('text')) return 'text';
    if (mimeType.includes('audio')) return 'audio';
    if (mimeType.includes('image')) return 'image';
    if (mimeType.includes('excel') || mimeType.includes('sheet')) return 'excel';
    if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'ppt';
    return 'other';
  };

  const renderUploadButton = (
    title: string,
    subtitle: string,
    icon: string,
    onPress: () => void,
    color: string = colors.primary
  ) => (
    <TouchableOpacity 
      style={[styles.uploadButton, { borderColor: color + '40' }]} 
      onPress={onPress}
      disabled={uploading}
    >
      <View style={[styles.uploadButtonIcon, { backgroundColor: color + '20' }]}>
        <Ionicons name={icon as any} size={32} color={color} />
      </View>
      <Text style={[styles.uploadButtonTitle, { color }]}>{title}</Text>
      <Text style={styles.uploadButtonSubtitle}>{subtitle}</Text>
    </TouchableOpacity>
  );

  const renderUploadProgress = (item: UploadProgress) => (
    <View key={item.id} style={styles.progressItem}>
      <View style={styles.progressHeader}>
        <Ionicons 
          name={item.status === 'completed' ? 'checkmark-circle' : 'cloud-upload'} 
          size={20} 
          color={item.status === 'completed' ? colors.success : colors.primary} 
        />
        <Text style={styles.progressFileName} numberOfLines={1}>
          {item.fileName}
        </Text>
        <Text style={styles.progressPercent}>{item.progress}%</Text>
      </View>
      <View style={styles.progressBar}>
        <View 
          style={[
            styles.progressFill, 
            { 
              width: `${item.progress}%`,
              backgroundColor: item.status === 'completed' ? colors.success : colors.primary
            }
          ]} 
        />
      </View>
    </View>
  );

  const renderCategorySelector = () => (
    <View style={styles.formGroup}>
      <Text style={styles.formLabel}>选择分类</Text>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.categoryScroll}
      >
        <TouchableOpacity
          style={[
            styles.categoryChip,
            !selectedCategory && styles.categoryChipSelected
          ]}
          onPress={() => setSelectedCategory('')}
        >
          <Text style={[
            styles.categoryChipText,
            !selectedCategory && styles.categoryChipTextSelected
          ]}>
            不分类
          </Text>
        </TouchableOpacity>
        {categories.map(category => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryChip,
              selectedCategory === category.id && styles.categoryChipSelected
            ]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <Text style={[
              styles.categoryChipText,
              selectedCategory === category.id && styles.categoryChipTextSelected
            ]}>
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.title}>文件上传</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {/* 上传按钮区域 */}
        <View style={styles.uploadSection}>
          <Text style={styles.sectionTitle}>选择文件类型</Text>
          <View style={styles.uploadGrid}>
            {renderUploadButton(
              '文档',
              'PDF, Word, Excel, PPT',
              'document-text',
              handleDocumentUpload
            )}
            {renderUploadButton(
              '音频',
              'MP3, WAV, M4A',
              'musical-notes',
              handleAudioUpload,
              colors.warning
            )}
            {renderUploadButton(
              '图片',
              'JPG, PNG, GIF',
              'image',
              handleImageUpload,
              colors.success
            )}
          </View>
        </View>

        {/* 文件信息表单 */}
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>文件信息</Text>
          
          <Input
            label="文件标题"
            value={documentTitle}
            onChangeText={setDocumentTitle}
            placeholder="为文件起一个标题（可选）"
          />

          <Input
            label="文件描述"
            value={documentDescription}
            onChangeText={setDocumentDescription}
            placeholder="简短描述文件内容（可选）"
            multiline
            numberOfLines={3}
          />

          {renderCategorySelector()}

          <Input
            label="标签"
            value={documentTags}
            onChangeText={setDocumentTags}
            placeholder="输入标签，用逗号分隔（可选）"
          />
        </View>

        {/* 上传进度 */}
        {uploadProgress.length > 0 && (
          <View style={styles.progressSection}>
            <Text style={styles.sectionTitle}>上传进度</Text>
            {uploadProgress.map(renderUploadProgress)}
          </View>
        )}

        {/* 使用提示 */}
        <View style={styles.tipSection}>
          <View style={styles.tipHeader}>
            <Ionicons name="information-circle" size={20} color={colors.info} />
            <Text style={styles.tipTitle}>使用提示</Text>
          </View>
          <Text style={styles.tipText}>
            • 支持批量选择和上传多个文件{'\n'}
            • 文件大小限制：50MB{'\n'}
            • 上传后可以在知识库中管理和搜索{'\n'}
            • 音频文件可以自动转写为文字
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
    marginLeft: -spacing.sm,
  },
  title: {
    flex: 1,
    textAlign: 'center',
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  uploadSection: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  uploadGrid: {
    gap: spacing.md,
  },
  uploadButton: {
    backgroundColor: colors.background.secondary,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: spacing.xl,
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  uploadButtonIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  uploadButtonTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    marginBottom: spacing.xs,
  },
  uploadButtonSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  formSection: {
    padding: spacing.lg,
    paddingTop: 0,
  },
  formGroup: {
    marginBottom: spacing.lg,
  },
  formLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  categoryScroll: {
    marginTop: spacing.sm,
  },
  categoryChip: {
    backgroundColor: colors.background.secondary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    marginRight: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  categoryChipSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryChipText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  categoryChipTextSelected: {
    color: colors.white,
  },
  progressSection: {
    padding: spacing.lg,
    paddingTop: 0,
  },
  progressItem: {
    backgroundColor: colors.background.secondary,
    padding: spacing.md,
    borderRadius: 12,
    marginBottom: spacing.sm,
  },
  progressHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
    gap: spacing.sm,
  },
  progressFileName: {
    flex: 1,
    fontSize: typography.fontSize.sm,
    color: colors.text.primary,
  },
  progressPercent: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  progressBar: {
    height: 4,
    backgroundColor: colors.background.tertiary,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  tipSection: {
    margin: spacing.lg,
    backgroundColor: colors.info + '10',
    padding: spacing.md,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: colors.info,
  },
  tipHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
    gap: spacing.sm,
  },
  tipTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
  },
  tipText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    lineHeight: 20,
  },
});