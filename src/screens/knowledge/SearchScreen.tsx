import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@/styles';
import { useKnowledgeStore } from '@/stores';
import { KnowledgeDocument } from '@/types';
import { useNavigation } from '@react-navigation/native';

interface SearchFilters {
  category: string;
  type: string[];
  dateRange: string;
  sizeRange: string;
}

interface SearchResult {
  document: KnowledgeDocument;
  relevanceScore: number;
  matchedFields: string[];
  highlightedTitle: string;
  highlightedContent: string;
}

export const SearchScreen: React.FC = () => {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [filters, setFilters] = useState<SearchFilters>({
    category: '',
    type: [],
    dateRange: '',
    sizeRange: '',
  });

  const { documents, categories } = useKnowledgeStore();

  const documentTypes = [
    { key: 'pdf', label: 'PDF', icon: 'document-text' },
    { key: 'word', label: 'Word', icon: 'document' },
    { key: 'audio', label: '音频', icon: 'musical-notes' },
    { key: 'image', label: '图片', icon: 'image' },
    { key: 'text', label: '文本', icon: 'document-outline' },
    { key: 'excel', label: 'Excel', icon: 'grid' },
    { key: 'ppt', label: 'PPT', icon: 'easel' },
  ];

  const dateRanges = [
    { key: 'today', label: '今天' },
    { key: 'week', label: '本周' },
    { key: 'month', label: '本月' },
    { key: 'quarter', label: '本季度' },
    { key: 'year', label: '今年' },
  ];

  const sizeRanges = [
    { key: 'small', label: '小于 1MB' },
    { key: 'medium', label: '1MB - 10MB' },
    { key: 'large', label: '10MB - 100MB' },
    { key: 'xlarge', label: '大于 100MB' },
  ];

  useEffect(() => {
    if (searchQuery.trim()) {
      performSearch();
    } else {
      setSearchResults([]);
    }
  }, [searchQuery, filters, performSearch]);

  const performSearch = async () => {
    setIsSearching(true);
    
    try {
      // 模拟搜索延时
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const results = searchDocuments(searchQuery, filters);
      setSearchResults(results);
      
      // 添加到搜索历史
      if (searchQuery.trim() && !searchHistory.includes(searchQuery.trim())) {
        setSearchHistory(prev => [searchQuery.trim(), ...prev.slice(0, 9)]);
      }
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const searchDocuments = (query: string, filters: SearchFilters): SearchResult[] => {
    const lowerQuery = query.toLowerCase();
    
    return documents
      .filter(doc => {
        // 基本搜索匹配
        const titleMatch = doc.title.toLowerCase().includes(lowerQuery);
        const contentMatch = doc.content.toLowerCase().includes(lowerQuery);
        const tagsMatch = doc.tags.some(tag => tag.toLowerCase().includes(lowerQuery));
        const descriptionMatch = doc.description?.toLowerCase().includes(lowerQuery);
        
        const hasMatch = titleMatch || contentMatch || tagsMatch || descriptionMatch;
        if (!hasMatch) return false;
        
        // 应用筛选器
        if (filters.category && doc.categoryId !== filters.category) return false;
        if (filters.type.length > 0 && !filters.type.includes(doc.type)) return false;
        
        // 日期筛选
        if (filters.dateRange) {
          const docDate = new Date(doc.createdAt);
          const now = new Date();
          const isInRange = checkDateRange(docDate, now, filters.dateRange);
          if (!isInRange) return false;
        }
        
        // 大小筛选
        if (filters.sizeRange) {
          const isInSizeRange = checkSizeRange(doc.size, filters.sizeRange);
          if (!isInSizeRange) return false;
        }
        
        return true;
      })
      .map(doc => {
        const matchedFields: string[] = [];
        let relevanceScore = 0;
        
        // 计算相关性得分
        if (doc.title.toLowerCase().includes(lowerQuery)) {
          matchedFields.push('title');
          relevanceScore += 3;
        }
        if (doc.content.toLowerCase().includes(lowerQuery)) {
          matchedFields.push('content');
          relevanceScore += 2;
        }
        if (doc.tags.some(tag => tag.toLowerCase().includes(lowerQuery))) {
          matchedFields.push('tags');
          relevanceScore += 2;
        }
        if (doc.description?.toLowerCase().includes(lowerQuery)) {
          matchedFields.push('description');
          relevanceScore += 1;
        }
        
        return {
          document: doc,
          relevanceScore,
          matchedFields,
          highlightedTitle: highlightText(doc.title, lowerQuery),
          highlightedContent: highlightText(
            doc.content.length > 200 ? doc.content.substring(0, 200) + '...' : doc.content,
            lowerQuery
          ),
        };
      })
      .sort((a, b) => b.relevanceScore - a.relevanceScore);
  };

  const checkDateRange = (docDate: Date, now: Date, range: string): boolean => {
    const diffTime = now.getTime() - docDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    switch (range) {
      case 'today':
        return diffDays <= 1;
      case 'week':
        return diffDays <= 7;
      case 'month':
        return diffDays <= 30;
      case 'quarter':
        return diffDays <= 90;
      case 'year':
        return diffDays <= 365;
      default:
        return true;
    }
  };

  const checkSizeRange = (size: number, range: string): boolean => {
    const sizeInMB = size / (1024 * 1024);
    
    switch (range) {
      case 'small':
        return sizeInMB < 1;
      case 'medium':
        return sizeInMB >= 1 && sizeInMB <= 10;
      case 'large':
        return sizeInMB > 10 && sizeInMB <= 100;
      case 'xlarge':
        return sizeInMB > 100;
      default:
        return true;
    }
  };

  const highlightText = (text: string, query: string): string => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '**$1**');
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const toggleTypeFilter = (type: string) => {
    setFilters(prev => ({
      ...prev,
      type: prev.type.includes(type)
        ? prev.type.filter(t => t !== type)
        : [...prev.type, type]
    }));
  };

  const clearFilters = () => {
    setFilters({
      category: '',
      type: [],
      dateRange: '',
      sizeRange: '',
    });
  };

  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (filters.category) count++;
    if (filters.type.length > 0) count++;
    if (filters.dateRange) count++;
    if (filters.sizeRange) count++;
    return count;
  }, [filters]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getDocumentIcon = (type: KnowledgeDocument['type']) => {
    const typeConfig = documentTypes.find(t => t.key === type);
    return typeConfig?.icon || 'document';
  };

  const renderSearchResult = ({ item }: { item: SearchResult }) => {
    const { document, matchedFields } = item;
    
    return (
      <TouchableOpacity style={styles.resultItem}>
        <View style={styles.resultHeader}>
          <View style={[styles.resultIcon, { backgroundColor: colors.primary + '20' }]}>
            <Ionicons
              name={getDocumentIcon(document.type) as any}
              size={20}
              color={colors.primary}
            />
          </View>
          <View style={styles.resultInfo}>
            <Text style={styles.resultTitle} numberOfLines={2}>
              {document.title}
            </Text>
            <Text style={styles.resultMeta}>
              {formatFileSize(document.size)} • {new Date(document.createdAt).toLocaleDateString('zh-CN')}
              {document.categoryId && (
                <Text style={styles.categoryName}>
                  {' • ' + categories.find(c => c.id === document.categoryId)?.name}
                </Text>
              )}
            </Text>
          </View>
          <View style={styles.relevanceScore}>
            <Text style={styles.scoreText}>{Math.round(item.relevanceScore * 10)}%</Text>
          </View>
        </View>
        
        {document.description && (
          <Text style={styles.resultDescription} numberOfLines={2}>
            {document.description}
          </Text>
        )}
        
        <View style={styles.matchedFields}>
          {matchedFields.map(field => (
            <View key={field} style={styles.matchField}>
              <Text style={styles.matchFieldText}>
                {field === 'title' ? '标题' : 
                 field === 'content' ? '内容' :
                 field === 'tags' ? '标签' : '描述'}
              </Text>
            </View>
          ))}
        </View>
        
        {document.tags.length > 0 && (
          <View style={styles.tagContainer}>
            {document.tags.slice(0, 3).map((tag, index) => (
              <Text key={index} style={styles.tag}>
                {tag}
              </Text>
            ))}
            {document.tags.length > 3 && (
              <Text style={styles.tagMore}>+{document.tags.length - 3}</Text>
            )}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderFilterSection = () => (
    <View style={styles.filterContainer}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* 分类筛选 */}
        <View style={styles.filterGroup}>
          <Text style={styles.filterGroupTitle}>分类</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[
                styles.filterChip,
                !filters.category && styles.filterChipSelected
              ]}
              onPress={() => handleFilterChange('category', '')}
            >
              <Text style={[
                styles.filterChipText,
                !filters.category && styles.filterChipTextSelected
              ]}>
                全部
              </Text>
            </TouchableOpacity>
            {categories.map(category => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.filterChip,
                  filters.category === category.id && styles.filterChipSelected
                ]}
                onPress={() => handleFilterChange('category', category.id)}
              >
                <Text style={[
                  styles.filterChipText,
                  filters.category === category.id && styles.filterChipTextSelected
                ]}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
        
        {/* 文件类型筛选 */}
        <View style={styles.filterGroup}>
          <Text style={styles.filterGroupTitle}>文件类型</Text>
          <View style={styles.filterGrid}>
            {documentTypes.map(type => (
              <TouchableOpacity
                key={type.key}
                style={[
                  styles.typeFilterChip,
                  filters.type.includes(type.key) && styles.typeFilterChipSelected
                ]}
                onPress={() => toggleTypeFilter(type.key)}
              >
                <Ionicons
                  name={type.icon as any}
                  size={16}
                  color={filters.type.includes(type.key) ? colors.white : colors.text.secondary}
                />
                <Text style={[
                  styles.typeFilterText,
                  filters.type.includes(type.key) && styles.typeFilterTextSelected
                ]}>
                  {type.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* 日期筛选 */}
        <View style={styles.filterGroup}>
          <Text style={styles.filterGroupTitle}>创建时间</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[
                styles.filterChip,
                !filters.dateRange && styles.filterChipSelected
              ]}
              onPress={() => handleFilterChange('dateRange', '')}
            >
              <Text style={[
                styles.filterChipText,
                !filters.dateRange && styles.filterChipTextSelected
              ]}>
                全部
              </Text>
            </TouchableOpacity>
            {dateRanges.map(range => (
              <TouchableOpacity
                key={range.key}
                style={[
                  styles.filterChip,
                  filters.dateRange === range.key && styles.filterChipSelected
                ]}
                onPress={() => handleFilterChange('dateRange', range.key)}
              >
                <Text style={[
                  styles.filterChipText,
                  filters.dateRange === range.key && styles.filterChipTextSelected
                ]}>
                  {range.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
        
        {/* 文件大小筛选 */}
        <View style={styles.filterGroup}>
          <Text style={styles.filterGroupTitle}>文件大小</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[
                styles.filterChip,
                !filters.sizeRange && styles.filterChipSelected
              ]}
              onPress={() => handleFilterChange('sizeRange', '')}
            >
              <Text style={[
                styles.filterChipText,
                !filters.sizeRange && styles.filterChipTextSelected
              ]}>
                全部
              </Text>
            </TouchableOpacity>
            {sizeRanges.map(range => (
              <TouchableOpacity
                key={range.key}
                style={[
                  styles.filterChip,
                  filters.sizeRange === range.key && styles.filterChipSelected
                ]}
                onPress={() => handleFilterChange('sizeRange', range.key)}
              >
                <Text style={[
                  styles.filterChipText,
                  filters.sizeRange === range.key && styles.filterChipTextSelected
                ]}>
                  {range.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
        
        {activeFiltersCount > 0 && (
          <TouchableOpacity style={styles.clearFiltersButton} onPress={clearFilters}>
            <Ionicons name="refresh" size={16} color={colors.error} />
            <Text style={styles.clearFiltersText}>清除筛选</Text>
          </TouchableOpacity>
        )}
      </ScrollView>
    </View>
  );

  const renderSearchHistory = () => (
    <View style={styles.historyContainer}>
      <Text style={styles.historyTitle}>搜索历史</Text>
      {searchHistory.map((item, index) => (
        <TouchableOpacity
          key={index}
          style={styles.historyItem}
          onPress={() => setSearchQuery(item)}
        >
          <Ionicons name="time" size={16} color={colors.text.secondary} />
          <Text style={styles.historyText}>{item}</Text>
          <TouchableOpacity
            onPress={() => setSearchHistory(prev => prev.filter((_, i) => i !== index))}
          >
            <Ionicons name="close" size={16} color={colors.text.secondary} />
          </TouchableOpacity>
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="chevron-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.searchBox}>
          <Ionicons name="search" size={20} color={colors.text.secondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="搜索文档、音频、标签..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={colors.text.secondary}
            autoFocus
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={colors.text.secondary} />
            </TouchableOpacity>
          )}
        </View>
        <TouchableOpacity
          style={[styles.filterButton, activeFiltersCount > 0 && styles.filterButtonActive]}
          onPress={() => setShowFilters(!showFilters)}
        >
          <Ionicons name="funnel" size={20} color={activeFiltersCount > 0 ? colors.white : colors.text.primary} />
          {activeFiltersCount > 0 && (
            <View style={styles.filterBadge}>
              <Text style={styles.filterBadgeText}>{activeFiltersCount}</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      {showFilters && renderFilterSection()}

      {isSearching ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>搜索中...</Text>
        </View>
      ) : searchQuery.trim() ? (
        <View style={styles.content}>
          <View style={styles.resultStats}>
            <Text style={styles.resultCount}>
              找到 {searchResults.length} 个结果
            </Text>
          </View>
          <FlatList
            data={searchResults}
            renderItem={renderSearchResult}
            keyExtractor={item => item.document.id}
            contentContainerStyle={styles.resultsList}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="search" size={64} color={colors.text.secondary} />
                <Text style={styles.emptyText}>没有找到相关文档</Text>
                <Text style={styles.emptySubtext}>尝试调整搜索词或筛选条件</Text>
              </View>
            }
          />
        </View>
      ) : (
        <ScrollView style={styles.content}>
          {searchHistory.length > 0 && renderSearchHistory()}
          
          <View style={styles.suggestionsContainer}>
            <Text style={styles.suggestionsTitle}>搜索建议</Text>
            <Text style={styles.suggestionsTip}>
              • 使用关键词搜索文档标题和内容{'\n'}
              • 支持标签搜索，如&quot;会议&quot;、&quot;项目&quot;{'\n'}
              • 可以组合使用筛选条件{'\n'}
              • 支持文件类型和时间范围筛选
            </Text>
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    gap: spacing.sm,
  },
  searchBox: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    gap: spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    paddingVertical: spacing.xs,
  },
  filterButton: {
    padding: spacing.sm,
    borderRadius: 8,
    position: 'relative',
  },
  filterButtonActive: {
    backgroundColor: colors.primary,
  },
  filterBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: colors.error,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterBadgeText: {
    fontSize: 10,
    color: colors.white,
    fontWeight: 'bold',
  },
  filterContainer: {
    backgroundColor: colors.background.secondary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    maxHeight: 300,
  },
  filterGroup: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  filterGroupTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  filterChip: {
    backgroundColor: colors.background.primary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    marginRight: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  filterChipSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterChipText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  filterChipTextSelected: {
    color: colors.white,
  },
  filterGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  typeFilterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 16,
    gap: spacing.xs,
    borderWidth: 1,
    borderColor: colors.border,
  },
  typeFilterChipSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  typeFilterText: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  typeFilterTextSelected: {
    color: colors.white,
  },
  clearFiltersButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    gap: spacing.xs,
  },
  clearFiltersText: {
    fontSize: typography.fontSize.sm,
    color: colors.error,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  resultStats: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  resultCount: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  resultsList: {
    padding: spacing.lg,
  },
  resultItem: {
    backgroundColor: colors.background.secondary,
    padding: spacing.md,
    borderRadius: 12,
    marginBottom: spacing.md,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  resultIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.sm,
  },
  resultInfo: {
    flex: 1,
  },
  resultTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  resultMeta: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  categoryName: {
    color: colors.primary,
  },
  relevanceScore: {
    backgroundColor: colors.primary + '20',
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 8,
  },
  scoreText: {
    fontSize: typography.fontSize.xs,
    color: colors.primary,
    fontWeight: 'bold',
  },
  resultDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: spacing.sm,
    lineHeight: 18,
  },
  matchedFields: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.xs,
    marginBottom: spacing.sm,
  },
  matchField: {
    backgroundColor: colors.info + '20',
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 8,
  },
  matchFieldText: {
    fontSize: typography.fontSize.xs,
    color: colors.info,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.xs,
  },
  tag: {
    fontSize: typography.fontSize.xs,
    color: colors.primary,
    backgroundColor: colors.primary + '20',
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 8,
  },
  tagMore: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.secondary,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptySubtext: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  historyContainer: {
    padding: spacing.lg,
  },
  historyTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    gap: spacing.sm,
  },
  historyText: {
    flex: 1,
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  suggestionsContainer: {
    padding: spacing.lg,
  },
  suggestionsTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  suggestionsTip: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    lineHeight: 20,
  },
});