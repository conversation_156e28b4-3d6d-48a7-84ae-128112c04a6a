import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@/styles';
import { LinearGradient } from 'expo-linear-gradient';
import { SwipeListView } from 'react-native-swipe-list-view';
import { DialogUtil } from '@/utils/dialogUtil';
import { useKnowledgeStore } from '@/stores/knowledgeStore';
import { KnowledgeDocument, KnowledgeCategory } from '@/types';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import KnowledgeSearchModal from '@/components/knowledge/KnowledgeSearchModal';
import { EditCategoryModal } from './EditCategoryModal';
import { lcdpService } from '@/services/api';
import Toast from 'react-native-toast-message';
import CommonAIChatModal, {
  AIChatConfig,
} from '@/components/common/ai-chat/CommonAIChatModal';

// 适配 KnowledgeDocument 类型到 UI 需要的 DocumentItem
interface DocumentItem extends Partial<KnowledgeDocument> {
  id: string;
  fileName: string;
  // 在 UI 中简化显示，只区分文档和音频
  displayType: 'document' | 'audio';
}

// 知识空间的AI聊天配置
const knowledgeConfig: AIChatConfig = {
  contextType: 'knowledge',
  welcomeConfig: {
    title: '知识问答',
    description: '我能学习和理解人类语言，提供业务知识解答服务',
    presetQuestions: [
      '帮我查询一下最近一次关于月度绩效的相关内容',
      '帮我总结一下会议的核心要点',
    ],
  },
  inputConfig: {
    placeholder: '点击输入与知识相关的问题',
  },
  contextParams: {
    paramName: 'knowledgeId',
    selector: 'KnowledgeSelector',
  },
};

export const KnowledgeScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('');
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  const [editCategoryModalVisible, setEditCategoryModalVisible] =
    useState(false);
  const [selectedDocument, setSelectedDocument] = useState<DocumentItem | null>(
    null
  );
  const [showAIChat, setShowAIChat] = useState(false);
  const navigation = useNavigation() as any;
  const swipeListRef = React.useRef<SwipeListView<DocumentItem>>(null);

  // 获取安全区域信息，用于精确计算底部间距
  const insets = useSafeAreaInsets();

  // 精确计算底部间距：让最后一个文档项的下边框恰好被Tab栏遮挡
  // Tab栏高度为56px（来自TabNavigator.tsx中的tabBarStyle.height）
  const TAB_BAR_HEIGHT = 56;
  const BORDER_OVERLAP = 0.5; // 让列表项下边框(0.5px)被Tab栏遮挡，避免双边框
  const bottomPadding = TAB_BAR_HEIGHT + insets.bottom - BORDER_OVERLAP;

  // 使用知识库存储
  const {
    documents: storeDocuments,
    isLoading,
    setLoading,
    deleteDocument,
    updateDocument,
    addDocument,
    initCategory,
    categories,
  } = useKnowledgeStore();

  useEffect(() => {
    initCategory();
  }, [initCategory]);

  // 将存储中的文档转换为UI需要的格式
  const mapDocumentsToUI = (): DocumentItem[] => {
    return storeDocuments.map((doc) => ({
      ...doc,
      displayType: ['mp3', 'wav', 'm4a', 'aac', 'ogg', 'flac'].includes(
        doc.fileType
      )
        ? 'audio'
        : 'document',
    }));
  };

  // 根据当前选择的标签过滤文档
  const getFilteredDocuments = () => {
    const uiDocuments = mapDocumentsToUI();

    if (activeTab === '') {
      return uiDocuments;
    } else {
      return uiDocuments.filter((doc) => doc.sceneId === activeTab);
    }
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={styles.title}>知识空间</Text>
      <View style={styles.headerRight}>
        <View style={styles.storageContainer}>
          <View style={styles.storageBackground}>
            <LinearGradient
              colors={['#3784F8', '#2E4BEB']}
              style={styles.storageBar}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            />
          </View>
          <Text style={styles.storageText}>24%已用</Text>
        </View>
        <TouchableOpacity
          style={styles.aiChatButton}
          onPress={() => setShowAIChat(true)}
        >
          <Image
            source={require('../../../assets/logo.png')}
            style={styles.aiChatIcon}
          />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderUploadCards = () => (
    <View style={styles.uploadSection}>
      <TouchableOpacity
        style={styles.uploadCard}
        onPress={() => handleUploadDocument('document')}
      >
        <Image
          source={require('../../../assets/images/icons/upload_document.png')}
          style={styles.uploadIcon}
        />
        <Text style={styles.uploadText}>上传文档</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.uploadCard}
        onPress={() => handleUploadDocument('audio')}
      >
        <Image
          source={require('../../../assets/images/icons/upload_audio.png')}
          style={styles.uploadIcon}
        />
        <Text style={styles.uploadText}>上传音频</Text>
      </TouchableOpacity>
    </View>
  );

  // 处理打开搜索模态框
  const handleOpenSearchModal = () => {
    setSearchModalVisible(true);
  };

  // 处理批量管理跳转
  const handleNavigateToBatchManage = () => {
    navigation.navigate('KnowledgeBatchManageSceen');
  };

  // 处理关闭搜索模态框
  const handleCloseSearchModal = () => {
    setSearchModalVisible(false);
  };

  // 处理文档被选中
  const handleDocumentSelect = (_document: KnowledgeDocument) => {
    // 这里可以处理文档被选中的逻辑，例如导航到文档详情页
    // console.log('文档被选中:', document.fileName);
  };

  // 处理文档删除
  const handleDeleteDocument = (document: DocumentItem) => {
    DialogUtil.confirmDelete('删除文档', '确定要删除这个文档吗？', () => {
      deleteDocument(document.id);
      // 关闭所有打开的swipe行
      if (swipeListRef.current) {
        swipeListRef.current.closeAllOpenRows();
      }
    });
  };

  // 处理文档编辑
  const handleEditDocument = (document: DocumentItem) => {
    DialogUtil.prompt(
      '重命名',
      '请输入新的文档标题',
      document.fileName,
      (newTitle: string) => {
        if (newTitle && newTitle.trim() !== '') {
          updateDocument(document.id, { fileName: newTitle.trim() });
          // 关闭所有打开的swipe行
          if (swipeListRef.current) {
            swipeListRef.current.closeAllOpenRows();
          }
        }
      }
    );
  };

  // 处理关闭编辑分类模态框
  const handleCloseEditCategoryModal = () => {
    setEditCategoryModalVisible(false);
    setSelectedDocument(null);

    // 关闭所有打开的swipe行
    if (swipeListRef.current) {
      swipeListRef.current.closeAllOpenRows();
    }
  };

  // 处理保存分类
  const handleSaveCategory = async (category: KnowledgeCategory) => {
    if (selectedDocument && category.id) {
      // 更新文档的标题和其他相关信息
      await lcdpService.changeSpaceScene({
        space_ids: [{ id: selectedDocument.id }],
        scene_id: category.id,
      });
      updateDocument(selectedDocument.id, {
        sceneId: category.id,
      });
      Toast.show({
        text1: '修改成功',
      });

      // 关闭所有打开的swipe行
      if (swipeListRef.current) {
        swipeListRef.current.closeAllOpenRows();
      }
    }
  };

  // 处理文档分类
  const handleCategorizeDocument = (document?: DocumentItem) => {
    // 设置选中的文档并打开编辑分类模态框
    if (document) {
      setSelectedDocument(document);
    }
    setEditCategoryModalVisible(true);
  };

  // 获取文件类型
  const getDocumentType = (
    fileType: string | undefined
  ): 'text' | 'pdf' | 'word' | 'audio' | 'other' => {
    if (!fileType) return 'other';

    if (fileType.includes('audio')) return 'audio';
    if (fileType.includes('pdf')) return 'pdf';
    if (
      fileType.includes('word') ||
      fileType.includes('msword') ||
      fileType.includes('officedocument.wordprocessingml')
    )
      return 'word';
    if (fileType.includes('text') || fileType.includes('plain')) return 'text';

    return 'other';
  };

  // 处理文档上传
  const handleUploadDocument = async (type: 'document' | 'audio') => {
    try {
      setLoading(true);

      let result;
      if (type === 'document') {
        // 打开文档选择器
        result = await DocumentPicker.getDocumentAsync({
          type: [
            'application/pdf',
            'text/plain',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          ],
          copyToCacheDirectory: true,
        });
      } else {
        // 打开音频选择器
        result = await DocumentPicker.getDocumentAsync({
          type: ['audio/*'],
          copyToCacheDirectory: true,
        });
      }

      if (result.canceled) {
        setLoading(false);
        return;
      }

      const file = result.assets[0];

      // 获取文件信息
      const fileInfo = await FileSystem.getInfoAsync(file.uri);

      // 获取文件大小
      let fileSize = 0;
      if (fileInfo.exists) {
        // FileInfo 类型存在且有效时才读取 size
        fileSize = 'size' in fileInfo ? fileInfo.size : 0;
      }

      // 处理文件名，去掉扩展名
      let fileName = file.name || `新${type === 'document' ? '文档' : '音频'}`;
      // 如果文件名包含扩展名，则去掉
      if (fileName.includes('.')) {
        fileName = fileName.substring(0, fileName.lastIndexOf('.'));
      }

      // 从文件路径获取文件扩展名
      const fileExtension = file.name?.split('.').pop()?.toLowerCase() || '';

      // 根据扩展名确定MIME类型
      const mimeType =
        file.mimeType ||
        (fileExtension === 'pdf'
          ? 'application/pdf'
          : fileExtension === 'txt'
            ? 'text/plain'
            : fileExtension === 'doc'
              ? 'application/msword'
              : fileExtension === 'docx'
                ? 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                : fileExtension === 'mp3'
                  ? 'audio/mpeg'
                  : fileExtension === 'wav'
                    ? 'audio/wav'
                    : fileExtension === 'm4a'
                      ? 'audio/mp4'
                      : 'application/octet-stream');

      // 创建文件对象用于上传
      const fileObject = {
        uri: file.uri,
        type: mimeType,
        name: file.name || `document.${fileExtension}`,
      } as any;

      // 使用lcdpService上传文件获取文件ID
      const fileResponse = await lcdpService.uploadSingleFile(fileObject);

      if (fileResponse.resultCode !== '0') {
        Toast.show({
          type: 'error',
          text1: '文件上传失败',
          text2: fileResponse.resultMsg || '请检查网络连接',
        });
        setLoading(false);
        return;
      }

      const fileId = fileResponse.resultObject.fileId;

      let sceneId = activeTab;

      if (!sceneId) {
        const type = getDocumentType(file.mimeType);
        if (type === 'audio') {
          sceneId = categories.find((c) => c.sceneName === '音频')?.id || '';
        } else {
          sceneId = categories.find((c) => c.sceneName === '文档')?.id || '';
        }
      }

      // 创建新文档对象，包含服务器文件ID
      const newDoc: KnowledgeDocument = {
        id: `new-${Date.now()}`,
        fileName,
        fileType: fileExtension,
        fileSize: fileSize,
        filePath: file.uri,
        fileId: fileId, // 保存服务器返回的文件ID
        sceneId: sceneId,
      };

      // 添加到知识库
      await addDocument(newDoc);

      // 显示成功消息
      Toast.show({
        type: 'success',
        text1: '上传成功',
        text2: `已成功上传${type === 'document' ? '文档' : '音频'}: ${newDoc.fileName}`,
      });
    } catch (error) {
      // console.error('文件上传错误:', error);
      DialogUtil.error('请重试或选择其他文件');
    } finally {
      setLoading(false);
    }
  };

  // 渲染文档列表项
  const renderDocumentItem = (data: { item: DocumentItem }) => (
    <View style={styles.documentItem}>
      <View style={styles.documentContent}>
        <View style={styles.documentIcon}>
          {data.item.displayType === 'document' ? (
            <LinearGradient
              colors={['#4080FF', '#94BFFF']}
              style={styles.documentIconBg}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons name="document-text" size={12} color={colors.white} />
            </LinearGradient>
          ) : (
            <LinearGradient
              colors={['#7570E7', '#B9B5FF']}
              style={styles.documentIconBg}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons name="mic" size={12} color={colors.white} />
            </LinearGradient>
          )}
        </View>
        <Text
          style={styles.documentTitle}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {data.item.fileName}
        </Text>
      </View>
    </View>
  );

  // 渲染隐藏的操作按钮
  const renderHiddenItem = (data: { item: DocumentItem }) => (
    <View style={styles.rowBack}>
      <TouchableOpacity
        style={[styles.backRightBtn, styles.backRightBtnLeft]}
        onPress={() => handleEditDocument(data.item)}
      >
        <View style={styles.editBtn}>
          <Image
            source={require('../../../assets/images/icons/icon-edit.png')}
            style={styles.ctrlBtn}
          />
        </View>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.backRightBtn, styles.backRightBtnCenter]}
        onPress={() => handleCategorizeDocument(data.item)}
      >
        <View style={styles.categoryBtn}>
          <Image
            source={require('../../../assets/images/icons/icon-category.png')}
            style={styles.ctrlBtn}
          />
        </View>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.backRightBtn, styles.backRightBtnRight]}
        onPress={() => handleDeleteDocument(data.item)}
      >
        <View style={styles.deleteBtn}>
          <Image
            source={require('../../../assets/images/icons/icon-trash.png')}
            style={styles.ctrlBtn}
          />
        </View>
      </TouchableOpacity>
    </View>
  );

  const renderContentSection = () => (
    <View style={styles.contentSection}>
      <View style={styles.tabsContainer}>
        <ScrollView
          style={styles.tabs}
          horizontal
          showsHorizontalScrollIndicator={false}
        >
          <TouchableOpacity
            style={[styles.tab, activeTab === '' && styles.activeTab]}
            onPress={() => setActiveTab('')}
          >
            <Text
              style={[styles.tabText, activeTab === '' && styles.activeTabText]}
            >
              全部
            </Text>
            {activeTab === '' && <View style={styles.tabIndicator} />}
          </TouchableOpacity>

          {categories.map((c) => (
            <TouchableOpacity
              key={c.id}
              style={[styles.tab, activeTab === c.id && styles.activeTab]}
              onPress={() => setActiveTab(c.id)}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === c.id && styles.activeTabText,
                ]}
              >
                {c.sceneName}
              </Text>
              {activeTab === c.id && <View style={styles.tabIndicator} />}
            </TouchableOpacity>
          ))}
        </ScrollView>

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleCategorizeDocument()}
          >
            <Image
              source={require('../../../assets/images/icons/icon-category-grey.png')}
              style={styles.ctrlBtn}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleOpenSearchModal}
          >
            <Image
              source={require('../../../assets/images/icons/icon-search.png')}
              style={styles.ctrlBtn}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleNavigateToBatchManage}
          >
            <Image
              source={require('../../../assets/images/icons/icon-filter.png')}
              style={styles.ctrlBtn}
            />
          </TouchableOpacity>
        </View>
      </View>

      <SwipeListView
        ref={swipeListRef}
        data={getFilteredDocuments()}
        renderItem={renderDocumentItem}
        renderHiddenItem={renderHiddenItem}
        rightOpenValue={-144} // 三个按钮的宽度：48*3
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        disableRightSwipe
        style={styles.documentList}
        contentContainerStyle={{ paddingBottom: bottomPadding }}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      ) : (
        <>
          <View style={styles.headerContent}>
            {renderHeader()}
            {renderUploadCards()}
          </View>
          {renderContentSection()}

          {/* 搜索模态框 */}
          <KnowledgeSearchModal
            visible={searchModalVisible}
            onClose={handleCloseSearchModal}
            documents={storeDocuments}
            onDocumentPress={handleDocumentSelect}
          />

          {/* 编辑分类模态框 */}
          <EditCategoryModal
            visible={editCategoryModalVisible}
            onClose={handleCloseEditCategoryModal}
            onSave={handleSaveCategory}
            sceneId={selectedDocument?.sceneId || ''}
          />

          {/* AI对话弹窗 */}
          <CommonAIChatModal
            visible={showAIChat}
            onClose={() => setShowAIChat(false)}
            config={knowledgeConfig}
          />
        </>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F4F8FF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F4F8FF',
  },
  loadingText: {
    marginTop: spacing.md,
    fontSize: 14,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily,
  },
  headerContent: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
  },

  // Header 样式
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2A2B33',
    fontFamily: typography.fontFamily,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  storageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  storageBackground: {
    width: 127.5,
    height: 14,
    backgroundColor: colors.background.secondary,
    borderRadius: 18,
    overflow: 'hidden',
  },
  storageBar: {
    width: '24%',
    height: '100%',
    borderRadius: 18,
  },
  storageText: {
    fontSize: 10,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily,
  },
  aiChatButton: {
    padding: 2,
    marginRight: spacing.sm,
  },
  aiChatIcon: {
    width: 28,
    height: 28,
    borderRadius: 8,
  },

  // Upload Cards 样式
  uploadSection: {
    flexDirection: 'row',
    gap: spacing.sm,
    marginBottom: spacing.lg,
  },
  uploadCard: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    shadowColor: '#64677A',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  uploadIcon: {
    width: 32,
    height: 32,
  },
  uploadText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#2A2B33',
    fontFamily: typography.fontFamily,
  },

  // Content Section 样式
  contentSection: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    shadowColor: '#64677A',
    shadowOffset: { width: 0, height: -1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 3,
  },
  tabsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    paddingRight: spacing.lg,
  },
  tabs: {
    flexDirection: 'row',
    marginRight: spacing.sm,
  },
  tab: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  activeTab: {
    // 活跃状态样式由文字颜色和指示器处理
  },
  tabText: {
    fontSize: 15,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily,
  },
  activeTabText: {
    color: '#2A2B33',
    fontWeight: typography.fontWeight.bold,
  },
  tabIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 14.5,
    width: '100%',
    height: 2,
    backgroundColor: '#2E4BEB',
    borderRadius: 2,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 4,
  },

  // Document List 样式
  documentList: {
    flex: 1,
  },
  documentItem: {
    backgroundColor: colors.white,
    borderBottomWidth: 0.5,
    borderBottomColor: '#EBEBF0',
    paddingLeft: spacing.lg,
  },
  documentContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    paddingVertical: 12,
    paddingRight: spacing.lg,
  },
  documentIcon: {
    // 图标容器样式
  },
  documentIconBg: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  documentTitle: {
    flex: 1,
    fontSize: 15,
    color: colors.text.secondary,
    lineHeight: 24,
    fontFamily: typography.fontFamily,
  },

  // SwipeListView 滑动按钮样式
  rowBack: {
    alignItems: 'center',
    backgroundColor: '#F2F3F5',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingRight: 0,
  },
  backRightBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 48,
  },
  backRightBtnLeft: {
    right: 96, // 第一个按钮，距离右边96px
  },
  backRightBtnCenter: {
    right: 48, // 第二个按钮，距离右边48px
  },
  backRightBtnRight: {
    right: 0, // 第三个按钮，贴右边
  },

  editBtn: {
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    backgroundColor: colors.white,
  },
  categoryBtn: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    backgroundColor: colors.white,
  },
  deleteBtn: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    backgroundColor: '#FF615C',
  },
  ctrlBtn: {
    width: 16,
    height: 16,
  },
});
