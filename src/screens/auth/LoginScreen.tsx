import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  StatusBar,
  Platform,
  Image,
  KeyboardAvoidingView,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import { spacing } from '@/styles';
import { useAuthStore } from '@/stores';
import { RootStackParamList } from '@/types/navigation';
import Toast from 'react-native-toast-message';

type LoginScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Login'
>;
type LoginScreenRouteProp = StackScreenProps<
  RootStackParamList,
  'Login'
>['route'];

export const LoginScreen: React.FC = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const route = useRoute<LoginScreenRouteProp>();

  // 如果从注册页面跳转过来，自动填充手机号
  const [phone, setPhone] = useState(route.params?.phone || '');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { login } = useAuthStore();

  // 验证手机号格式
  const validatePhone = (phoneNumber: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phoneNumber);
  };

  const handleLogin = async () => {
    if (!phone || !password) {
      Toast.show({
        type: 'error',
        text1: '请填写完整信息',
        text2: '手机号和密码不能为空',
      });
      return;
    }

    if (!validatePhone(phone)) {
      Toast.show({
        type: 'error',
        text1: '手机号格式错误',
        text2: '请输入正确的手机号',
      });
      return;
    }

    setIsLoading(true);
    try {
      await login({ email: phone, password });
      Toast.show({
        type: 'success',
        text1: '登录成功',
        text2: '欢迎回来！',
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '登录失败',
        text2: '请检查手机号和密码是否正确',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = () => {
    navigation.navigate('Register');
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* 状态栏 */}
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* 顶部导航 */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#000000" />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContentContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {/* 顶部Logo和标题 */}
          <View style={styles.logoSection}>
            <Image
              source={require('../../../assets/logo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
            <Text style={styles.appTitle}>办公助手</Text>
          </View>

          {/* 登录表单 */}
          <View style={styles.formWrapper}>
            <View style={styles.formCard}>
              <Text style={styles.welcomeTitle}>欢迎回来</Text>
              <Text style={styles.welcomeSubtitle}>请输入您的登录信息</Text>

              {/* 手机号输入框 */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>手机号</Text>
                <TextInput
                  style={styles.textInput}
                  value={phone}
                  onChangeText={setPhone}
                  placeholder="请输入手机号"
                  placeholderTextColor="#B0B2BF"
                  keyboardType="phone-pad"
                  maxLength={11}
                />
              </View>

              {/* 密码输入框 */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>密码</Text>
                <View style={styles.passwordContainer}>
                  <TextInput
                    style={[styles.textInput, styles.passwordInput]}
                    value={password}
                    onChangeText={setPassword}
                    placeholder="请输入密码"
                    placeholderTextColor="#B0B2BF"
                    secureTextEntry={!showPassword}
                  />
                  <TouchableOpacity
                    style={styles.eyeButton}
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Ionicons
                      name={showPassword ? 'eye' : 'eye-off'}
                      size={20}
                      color="#B0B2BF"
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* 一键登录按钮（实际执行账号密码登录）*/}
              <TouchableOpacity
                style={styles.quickLoginButton}
                onPress={handleLogin}
                disabled={isLoading}
              >
                <Text style={styles.quickLoginText}>
                  {isLoading ? '登录中...' : '登录'}
                </Text>
              </TouchableOpacity>

              {/* 用户协议 */}
              <View style={styles.agreementContainer}>
                <Ionicons name="checkmark-circle" size={16} color="#2E4BEB" />
                <Text style={styles.agreementText}>
                  我已阅读并同意《服务条款》及《隐私政策》
                </Text>
              </View>
            </View>

            {/* 底部注册链接 */}
            <View style={styles.bottomSection}>
              <Text style={styles.bottomText}>还没有账号？</Text>
              <TouchableOpacity onPress={handleRegister}>
                <Text style={styles.registerLink}>立即注册</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  backButton: {
    padding: spacing.xs,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContentContainer: {
    flexGrow: 1,
    paddingHorizontal: spacing.lg,
  },
  logoSection: {
    alignItems: 'center',
    marginTop: spacing.xl,
    marginBottom: spacing.xl,
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: spacing.lg,
  },
  appTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'center',
  },
  formWrapper: {
    flex: 1,
    justifyContent: 'center',
    minHeight: 400,
  },
  formCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    padding: spacing.xl,
    marginHorizontal: spacing.md,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  welcomeSubtitle: {
    fontSize: 14,
    color: '#8F9098',
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  inputLabel: {
    fontSize: 14,
    color: '#000000',
    marginBottom: spacing.sm,
    fontWeight: '500',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E8E8E8',
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: 16,
    color: '#000000',
    backgroundColor: '#FFFFFF',
    minHeight: 48,
  },
  passwordContainer: {
    position: 'relative',
  },
  passwordInput: {
    paddingRight: 50,
  },
  eyeButton: {
    position: 'absolute',
    right: spacing.md,
    top: spacing.md,
    padding: spacing.xs,
  },
  quickLoginButton: {
    backgroundColor: '#2E4BEB',
    borderRadius: 12,
    paddingVertical: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: spacing.lg,
    marginBottom: spacing.lg,
    minHeight: 48,
  },
  quickLoginText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  agreementContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  agreementText: {
    fontSize: 12,
    color: '#8F9098',
    marginLeft: spacing.xs,
    flex: 1,
  },
  bottomSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: spacing.xl,
    marginBottom: spacing.xl,
  },
  bottomText: {
    fontSize: 14,
    color: '#8F9098',
  },
  registerLink: {
    fontSize: 14,
    color: '#2E4BEB',
    fontWeight: '600',
    marginLeft: spacing.xs,
  },
});

export default LoginScreen;
