import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Image,
  StatusBar,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing } from '../../styles';
import { authService } from '../../services/api';
import { RootStackParamList } from '../../types/navigation';
import Toast from 'react-native-toast-message';

type RegisterScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Register'
>;

export const RegisterScreen: React.FC = () => {
  const navigation = useNavigation<RegisterScreenNavigationProp>();
  const [username, setUsername] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({
    username: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });

  // 验证用户名
  const validateUsername = (value: string) => {
    if (value.length < 4) {
      return '用户名不能少于4个字符';
    }
    return '';
  };

  // 验证手机号
  const validatePhone = (value: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(value)) {
      return '请输入正确的手机号';
    }
    return '';
  };

  // 验证密码
  const validatePassword = (value: string) => {
    if (value.length < 8) {
      return '密码长度不能少于8位';
    }
    if (!/[a-z]/.test(value)) {
      return '密码必须包含小写字母';
    }
    if (!/[A-Z]/.test(value)) {
      return '密码必须包含大写字母';
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
      return '密码必须包含特殊字符';
    }
    return '';
  };

  // 验证确认密码
  const validateConfirmPassword = (value: string) => {
    if (value !== password) {
      return '两次输入的密码不一致';
    }
    return '';
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    switch (field) {
      case 'username':
        setUsername(value);
        setErrors({ ...errors, username: validateUsername(value) });
        break;
      case 'phone':
        setPhone(value);
        setErrors({ ...errors, phone: validatePhone(value) });
        break;
      case 'password':
        setPassword(value);
        setErrors({ ...errors, password: validatePassword(value) });
        break;
      case 'confirmPassword':
        setConfirmPassword(value);
        setErrors({
          ...errors,
          confirmPassword: validateConfirmPassword(value),
        });
        break;
    }
  };

  // 处理注册
  const handleRegister = async () => {
    // 验证所有字段
    const usernameError = validateUsername(username);
    const phoneError = validatePhone(phone);
    const passwordError = validatePassword(password);
    const confirmPasswordError = validateConfirmPassword(confirmPassword);

    setErrors({
      username: usernameError,
      phone: phoneError,
      password: passwordError,
      confirmPassword: confirmPasswordError,
    });

    if (usernameError || phoneError || passwordError || confirmPasswordError) {
      return;
    }

    setLoading(true);
    try {
      await authService.register({
        username,
        phone,
        password,
      });

      Toast.show({
        type: 'success',
        text1: '注册成功',
        text2: '请使用您的手机号登录',
      });

      // 注册成功后跳转到登录页面，并传递手机号
      navigation.navigate('Login', { phone });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: '注册失败',
        text2: error.message || '请稍后重试',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* 状态栏 */}
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* 顶部导航 */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#000000" />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContentContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {/* 顶部Logo和标题 */}
          <View style={styles.logoSection}>
            <Image
              source={require('../../../assets/logo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
            <Text style={styles.appTitle}>办公助手</Text>
          </View>

          {/* 注册表单 */}
          <View style={styles.formWrapper}>
            <View style={styles.formCard}>
              <Text style={styles.welcomeTitle}>创建账号</Text>
              <Text style={styles.welcomeSubtitle}>请填写以下信息完成注册</Text>

              {/* 用户名输入 */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>用户名</Text>
                <TextInput
                  style={styles.textInput}
                  value={username}
                  onChangeText={(text) => handleInputChange('username', text)}
                  placeholder="请输入用户名（至少4个字符）"
                  placeholderTextColor="#B0B2BF"
                  autoCapitalize="none"
                />
                {errors.username ? (
                  <Text style={styles.errorText}>{errors.username}</Text>
                ) : null}
              </View>

              {/* 手机号输入 */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>手机号</Text>
                <TextInput
                  style={styles.textInput}
                  value={phone}
                  onChangeText={(text) => handleInputChange('phone', text)}
                  placeholder="请输入手机号"
                  placeholderTextColor="#B0B2BF"
                  keyboardType="phone-pad"
                  maxLength={11}
                />
                {errors.phone ? (
                  <Text style={styles.errorText}>{errors.phone}</Text>
                ) : null}
              </View>

              {/* 密码输入 */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>密码</Text>
                <View style={styles.passwordContainer}>
                  <TextInput
                    style={[styles.textInput, styles.passwordInput]}
                    value={password}
                    onChangeText={(text) => handleInputChange('password', text)}
                    placeholder="请输入密码"
                    placeholderTextColor="#B0B2BF"
                    secureTextEntry={!showPassword}
                  />
                  <TouchableOpacity
                    style={styles.eyeButton}
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Ionicons
                      name={showPassword ? 'eye' : 'eye-off'}
                      size={20}
                      color="#B0B2BF"
                    />
                  </TouchableOpacity>
                </View>
                <Text style={styles.passwordHint}>
                  密码需包含大小写字母、特殊字符，且不少于8位
                </Text>
                {errors.password ? (
                  <Text style={styles.errorText}>{errors.password}</Text>
                ) : null}
              </View>

              {/* 确认密码输入 */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>确认密码</Text>
                <View style={styles.passwordContainer}>
                  <TextInput
                    style={[styles.textInput, styles.passwordInput]}
                    value={confirmPassword}
                    onChangeText={(text) =>
                      handleInputChange('confirmPassword', text)
                    }
                    placeholder="请再次输入密码"
                    placeholderTextColor="#B0B2BF"
                    secureTextEntry={!showConfirmPassword}
                  />
                  <TouchableOpacity
                    style={styles.eyeButton}
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    <Ionicons
                      name={showConfirmPassword ? 'eye' : 'eye-off'}
                      size={20}
                      color="#B0B2BF"
                    />
                  </TouchableOpacity>
                </View>
                {errors.confirmPassword ? (
                  <Text style={styles.errorText}>{errors.confirmPassword}</Text>
                ) : null}
              </View>

              {/* 注册按钮 */}
              <TouchableOpacity
                style={styles.registerButton}
                onPress={handleRegister}
                disabled={loading}
              >
                <Text style={styles.registerText}>
                  {loading ? '注册中...' : '注册'}
                </Text>
              </TouchableOpacity>
            </View>

            {/* 底部登录链接 */}
            <View style={styles.bottomSection}>
              <Text style={styles.bottomText}>已有账号？</Text>
              <TouchableOpacity onPress={() => navigation.goBack()}>
                <Text style={styles.loginLink}>立即登录</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  backButton: {
    padding: spacing.xs,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContentContainer: {
    flexGrow: 1,
    paddingHorizontal: spacing.lg,
  },
  logoSection: {
    alignItems: 'center',
    marginTop: spacing.md,
    marginBottom: spacing.md,
  },
  logo: {
    width: 64,
    height: 64,
    marginBottom: spacing.md,
  },
  appTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'center',
  },
  formWrapper: {
    flex: 1,
    justifyContent: 'center',
    minHeight: 400,
  },
  formCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    padding: spacing.lg,
    marginHorizontal: spacing.md,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  welcomeTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  welcomeSubtitle: {
    fontSize: 14,
    color: '#8F9098',
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  inputContainer: {
    marginBottom: spacing.sm,
  },
  inputLabel: {
    fontSize: 14,
    color: '#000000',
    marginBottom: spacing.xs,
    fontWeight: '500',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E8E8E8',
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: 16,
    color: '#000000',
    backgroundColor: '#FFFFFF',
    minHeight: 44,
  },
  passwordContainer: {
    position: 'relative',
  },
  passwordInput: {
    paddingRight: 50,
  },
  eyeButton: {
    position: 'absolute',
    right: spacing.md,
    top: spacing.sm,
    padding: spacing.xs,
  },
  passwordHint: {
    fontSize: 11,
    color: '#8F9098',
    marginTop: spacing.xs,
  },
  errorText: {
    fontSize: 11,
    color: colors.error,
    marginTop: spacing.xs,
  },
  registerButton: {
    backgroundColor: '#2E4BEB',
    borderRadius: 12,
    paddingVertical: spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: spacing.md,
    marginBottom: spacing.md,
    minHeight: 44,
  },
  registerText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  bottomSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: spacing.lg,
    marginBottom: spacing.lg,
  },
  bottomText: {
    fontSize: 14,
    color: '#8F9098',
  },
  loginLink: {
    fontSize: 14,
    color: '#2E4BEB',
    fontWeight: '600',
    marginLeft: spacing.xs,
  },
});
