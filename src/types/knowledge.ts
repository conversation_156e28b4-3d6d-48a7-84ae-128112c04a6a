export interface KnowledgeDocument {
  id: string;
  title: string;
  content: string;
  description?: string;
  type: 'text' | 'audio' | 'pdf' | 'word' | 'image' | 'excel' | 'ppt' | 'other';
  size: number;
  filePath: string;
  fileId?: string; // 服务器返回的文件ID
  categoryId?: string;
  tags: string[];
  summary?: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export interface KnowledgeCategory {
  id: string;
  sceneName: string;
  knowledgeId?: string;
}

export interface KnowledgeSearchResult {
  document: KnowledgeDocument;
  relevanceScore: number;
  matchedContent: string;
  highlights: string[];
}
