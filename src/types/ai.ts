// AI 服务配置
export interface AIServiceConfig {
  apiKey: string;
  baseUrl: string;
  model: string;
  timeout: number;
  retryAttempts: number;
}

// AI 请求类型
export interface AIRequest {
  type: 'chat' | 'summarize' | 'translate' | 'analyze' | 'generate';
  content: string;
  context?: string;
  options?: Record<string, any>;
}

// AI 响应类型
export interface AIResponse {
  success: boolean;
  data?: string;
  error?: string;
  usage?: {
    tokens: number;
    cost: number;
  };
}

// AI 聊天消息类型
export interface AIChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  tokens?: number;
}

// AI 聊天会话类型
export interface AIChatSession {
  id: string;
  title: string;
  messages: AIChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

// AI 功能类型
export type AIFeature = 
  | 'document-chat'
  | 'document-summary'
  | 'content-generation'
  | 'translation'
  | 'writing-assist'
  | 'question-answer';