export interface WritingContent {
  id: string;
  title: string;
  content: string;
  prompt: string;
  type: 'article' | 'optimization' | 'template' | 'freeform';
  wordCount: number;
  language: 'zh' | 'en';
  style: 'formal' | 'casual' | 'professional' | 'creative';
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export interface WritingTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  prompt: string;
  example?: string;
  tags: string[];
  isBuiltIn: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AIWritingRequest {
  prompt: string;
  type: 'generate' | 'optimize' | 'summarize' | 'expand';
  style?: 'formal' | 'casual' | 'professional' | 'creative';
  wordCount?: number;
  language?: 'zh' | 'en';
  context?: string;
}

export interface AIWritingResponse {
  content: string;
  wordCount: number;
  suggestions?: string[];
  metadata?: {
    model: string;
    processingTime: number;
    confidence: number;
  };
}