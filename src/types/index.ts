// 用户模型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  phone?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 录音模型 - 从recording.ts重新导出
export * from './recording';

// 知识库文档模型
export interface KnowledgeDocument {
  id: string;
  fileName: string;
  fileSize: number;
  fileId: string;
  sceneId: string;
  filePath: string;
  fileType: string;
}

// 知识库分类模型
export interface KnowledgeCategory {
  id: string;
  sceneName: string;
  knowledgeId?: string;
}

// AI写作内容模型
export interface WritingContent {
  id: string;
  title: string;
  content: string;
  prompt: string;
  type: 'article' | 'optimization' | 'template';
  wordCount: number;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// API错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}
