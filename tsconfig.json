{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@screens/*": ["src/screens/*"], "@navigation/*": ["src/navigation/*"], "@services/*": ["src/services/*"], "@stores/*": ["src/stores/*"], "@styles/*": ["src/styles/*"], "@types/*": ["src/types/*"], "@utils/*": ["src/utils/*"], "@hooks/*": ["src/hooks/*"], "@assets/*": ["assets/*"]}, "jsx": "react"}, "include": ["**/*.ts", "**/*.tsx"]}