# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Development Server

```bash
# 使用 yarn 启动开发服务器（推荐）
yarn start

# 平台特定启动
yarn android          # 在Android设备/模拟器上运行
yarn ios              # 在iOS设备/模拟器上运行
yarn web              # 在Web浏览器中运行

# 其他启动选项
yarn start:tunnel     # 使用隧道模式（适用于远程调试）
yarn start:lan        # 使用局域网模式
```

### 依赖管理和缓存

```bash
# 安装依赖
yarn install

# 清理缓存并启动
expo start --clear
expo start -c  # 简写形式

# 完全重置（解决顽固的缓存问题）
rm -rf node_modules yarn.lock && yarn install && expo start -c
```

### 代码质量和检查

```bash
# ESLint 检查和修复
yarn lint              # 运行代码检查
yarn lint:fix          # 自动修复可修复的问题

# 代码格式化
yarn format            # 使用Prettier格式化所有文件

# TypeScript 类型检查
yarn type-check        # 检查类型错误但不输出代码
```

### 开发工具脚本

```bash
# 调试工具
yarn screenshot        # 截取设备屏幕
yarn record           # 录制设备屏幕
yarn mirror           # 屏幕镜像

# 原生代码管理
yarn backup           # 备份Android原生代码
yarn restore          # 恢复Android原生代码
yarn check            # 检查原生代码状态
```

## Project Directory Structure

项目为单层结构，所有开发工作都在根目录中进行。主要目录结构：

```
src/
├── components/         # 可复用组件
│   ├── common/        # 通用组件 (Button, Input, Loading等)
│   ├── recording/     # 录音相关组件
│   └── modals/        # 模态对话框组件
├── screens/           # 页面组件 (按功能模块分组)
├── navigation/        # 导航配置
├── services/          # 业务服务层
│   ├── api/          # API客户端和服务
│   ├── audio/        # 音频处理服务
│   ├── speech/       # 语音识别服务
│   └── storage/      # 本地存储服务
├── stores/           # Zustand状态管理
├── hooks/            # 自定义React Hooks
├── styles/           # 样式系统 (colors, typography, spacing)
├── types/            # TypeScript类型定义
└── utils/            # 工具函数

android.back/         # Android原生代码备份
scripts/              # 构建和开发工具脚本
docs/                 # 项目文档
```

## Architecture Overview

### 技术栈

这是一个基于 Expo SDK 52 的 React Native 应用，使用 TypeScript 提供类型安全。主要技术栈包括：

- **前端框架**: React Native 0.76.9 + Expo 52
- **语言**: TypeScript 5.8.3
- **状态管理**: Zustand + AsyncStorage 持久化
- **导航**: React Navigation 6.x (Stack + Bottom Tabs)
- **网络请求**: Axios 自定义 ApiClient
- **音频处理**: 实时音频录制和PCM数据处理
- **语音识别**: 讯飞实时语音转写 WebSocket 集成
- **样式系统**: 集中化设计系统 (colors, typography, spacing)
- **开发工具**: ESLint + Prettier + Husky + lint-staged

### 核心架构模式

#### 导航架构

采用分层导航结构：

- **根导航**: Stack Navigator (登录/主应用切换)
- **主导航**: Bottom Tab Navigator (5个主要功能模块)
  - 首页 (Home)
  - 录音 (Recording)
  - 知识库 (Knowledge)
  - AI写作 (Writing)
  - 设置 (Settings)
- **子导航**: 每个Tab内的独立Stack导航

#### 状态管理模式

基于 Zustand 的模块化状态管理，配合 AsyncStorage 实现持久化：

- `appStore`: 全局应用状态 (主题、语言、加载状态)
- `authStore`: 用户认证和会话状态
- `recordingStore`: 录音功能专用状态
- `knowledgeStore`: 知识库管理状态
- `writingStore`: AI写作功能状态

#### API 集成模式

自定义 `ApiClient` 类 (`src/services/api/client.ts`) 基于 Axios：

- 自动注入认证令牌
- 全局错误处理和 Toast 通知
- 支持标准 REST 操作和文件上传
- 统一的响应数据格式处理

#### 实时音频处理架构

专为语音转录优化的音频处理链：

- **RealtimeAudioRecorder**: 实时音频采集和PCM数据处理
- **XunfeiRealtimeASR**: 讯飞语音识别WebSocket客户端
- **AudioStreamManager** (Android): 原生音频流管理
- 支持16kHz单声道PCM格式实时转写

#### 样式系统

集中化设计系统 (`src/styles/`)：

- 统一的颜色系统、字体规范和间距标准
- 支持主题切换和组件级样式复用

### 关键实现模式

#### 1. 页面组件模式

位于 `src/screens/` 目录，按功能模块组织：

```tsx
export default function ScreenName() {
  // 使用自定义hooks管理状态
  // 遵循统一的导航和样式模式
}
```

#### 2. API调用模式

使用统一的ApiClient实例：

```tsx
import { apiClient } from '@/services/api/client';
const response = await apiClient.get<ResponseType>('/endpoint');
```

#### 3. 状态管理模式

使用Zustand hooks访问全局状态：

```tsx
import { useAppStore } from '@/stores/appStore';
const { isLoading, setLoading } = useAppStore();
```

#### 4. 导航模式

使用类型安全的导航hooks：

```tsx
import { useNavigation } from '@react-navigation/native';
const navigation = useNavigation<NavigationProp>();
```

#### 5. 路径别名模式

项目配置了完整的路径别名系统：

- `@/`: src目录根路径
- `@components`: 组件目录
- `@screens`: 页面目录
- `@services`: 服务目录
- `@stores`: 状态管理目录
- `@styles`: 样式目录
- `@types`: 类型定义目录
- `@utils`: 工具函数目录
- `@hooks`: 自定义hooks目录
- `@assets`: 资源文件目录

### 重要配置文件

- `tsconfig.json`: TypeScript配置，包含路径别名映射
- `.eslintrc.js`: ESLint规则，包含React和TypeScript规范
- `babel.config.js`: Babel配置，支持路径别名解析
- `package.json`: 包含完整的开发脚本和依赖定义
- `app.json`: Expo应用配置

### 当前实现状态

- ✅ **导航架构**: 完整的分层导航系统
- ✅ **基础组件**: 通用组件库和录音相关组件
- ✅ **状态管理**: Zustand模块化状态管理
- ✅ **API客户端**: 基于Axios的统一HTTP客户端
- ✅ **实时音频**: 音频录制和PCM数据处理
- ✅ **语音识别**: 讯飞实时语音转写集成
- ⚠️ **用户认证**: 状态管理就绪，具体实现待完善
- ⚠️ **业务功能**: UI框架搭建完成，功能逻辑待实现
- ❌ **测试覆盖**: 单元测试和集成测试待添加

### 开发注意事项

#### 语言和本地化

- 所有UI文本使用中文（应用已本地化）
- 组件支持主题切换
- 遵循中文UI设计规范

#### 代码规范

- 使用现有的通用组件库 (`src/components/common/`)
- 遵循既定的页面和导航模式
- 路径导入优先使用别名 (`@/`, `@components` 等)
- API调用统一使用 `apiClient` 实例
- 错误处理会自动显示Toast通知

#### 音频和语音功能开发

- 实时音频录制使用 `RealtimeAudioRecorder`
- 语音识别集成 `XunfeiRealtimeASR` 类
- PCM数据格式：16kHz、16bit、单声道
- Android原生代码位于 `android.back/` 目录

### 常见问题解决方案

#### 开发环境问题

1. **Metro Error 500**: 运行 `expo start -c` 清理缓存
2. **依赖版本冲突**: 检查 `package.json` 确保版本兼容Expo 52
3. **路径别名问题**: 确认 `babel.config.js` 和 `tsconfig.json` 配置一致
4. **Android构建问题**: 确保安装Java 17和Android SDK 35

#### 音频功能问题

1. **录音权限**: 确保在设备上授予麦克风权限
2. **音频格式**: 使用16kHz单声道PCM格式
3. **实时转录**: 检查讯飞ASR配置和网络连接
4. **原生代码**: 使用 `yarn backup/restore` 管理Android原生代码

### 构建和部署

#### EAS 构建

```bash
# 开发构建（内部分发）
eas build --profile development --platform android
eas build --profile development --platform ios

# 预览构建（生成APK）
eas build --profile preview --platform android

# 生产构建
eas build --profile production --platform android
eas build --profile production --platform ios
```

#### 原生代码构建准备

项目使用自定义脚本管理原生模块，在EAS构建前会自动执行：

```bash
# EAS构建前的原生代码准备（自动执行）
yarn eas-build-pre
```

### 项目文档参考

项目在 `docs/` 目录下包含详细的技术文档：

- `product-requirements.md`: 产品功能需求和规格
- `design.md`: 架构设计和UI规范
- `tasks.md`: 开发任务和进度管理
- `android-*.md`: Android平台相关指南
- `*-fix-summary.md`: 具体功能实现和问题修复记录
