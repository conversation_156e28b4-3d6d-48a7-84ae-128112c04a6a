import { withDangerousMod } from '@expo/config-plugins';
import fs from 'fs';
import path from 'path';

export default function withCustomNativeCode(config) {
  return withDangerousMod(config, [
    'android',
    async (config) => {
      const projectRoot = config.modRequest.projectRoot;
      const androidProjectRoot = config.modRequest.platformProjectRoot;

      // 正确的源文件路径 - native-code-backup
      const backupDir = path.join(projectRoot, 'native-code-backup');
      const targetBaseDir = path.join(
        androidProjectRoot,
        'app/src/main/java/com/gzai168/zipco'
      );

      // 确保目标目录存在
      if (!fs.existsSync(targetBaseDir)) {
        fs.mkdirSync(targetBaseDir, { recursive: true });
      }

      // 正在从 native-code-backup 复制自定义原生代码

      try {
        // 复制音频模块文件
        const audioSourceDir = path.join(
          backupDir,
          'audio_module_20250724_153048'
        );
        const audioTargetDir = path.join(targetBaseDir, 'audio');

        if (fs.existsSync(audioSourceDir)) {
          if (!fs.existsSync(audioTargetDir)) {
            fs.mkdirSync(audioTargetDir, { recursive: true });
          }

          const audioFiles = fs.readdirSync(audioSourceDir);
          audioFiles.forEach((file) => {
            if (file.endsWith('.kt')) {
              const sourcePath = path.join(audioSourceDir, file);
              const targetPath = path.join(audioTargetDir, file);
              fs.copyFileSync(sourcePath, targetPath);
              // 已复制音频模块文件
            }
          });
        }

        // 复制MainApplication.kt
        const mainAppSource = path.join(
          backupDir,
          'MainApplication_20250724_153048.kt'
        );
        const mainAppTarget = path.join(targetBaseDir, 'MainApplication.kt');
        if (fs.existsSync(mainAppSource)) {
          fs.copyFileSync(mainAppSource, mainAppTarget);
          // 已复制 MainApplication.kt
        }

        // 复制AndroidManifest.xml
        const manifestSource = path.join(
          backupDir,
          'AndroidManifest_20250724_153048.xml'
        );
        const manifestTarget = path.join(
          androidProjectRoot,
          'app/src/main/AndroidManifest.xml'
        );
        if (fs.existsSync(manifestSource)) {
          fs.copyFileSync(manifestSource, manifestTarget);
          // 已复制 AndroidManifest.xml
        }

        // 所有自定义原生代码复制完成
      } catch (error) {
        throw new Error(`复制原生代码时出错: ${error.message}`);
      }

      return config;
    },
  ]);
}
