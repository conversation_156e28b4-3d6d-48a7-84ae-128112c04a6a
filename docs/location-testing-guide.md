# Android 定位修复测试指南

## 测试前准备

### 1. 构建项目

```bash
# 清理项目
cd android && ./gradlew clean

# 构建 debug 版本
./gradlew assembleDebug

# 或者使用 Expo 命令
cd .. && npm run android
```

### 2. 设备准备

- 使用真实的 Android 设备（推荐中国大陆的设备，没有 Google Play Services）
- 确保设备已开启开发者选项和 USB 调试
- 连接设备并授权 ADB 调试

## 测试场景

### 场景 1: 正常定位测试

**目的**: 验证原生定位模块能够正常工作

**步骤**:

1. 启动应用
2. 观察控制台日志，查找以下关键信息：
   ```
   🔧 使用原生定位模块...
   策略1: 尝试原生最后已知位置...
   ✅ 原生定位成功: {latitude: xxx, longitude: xxx}
   ```
3. 验证应用界面显示的位置信息

**预期结果**:

- 应用能在 10 秒内获取到位置
- 位置精度在合理范围内（通常 100-1000 米）
- 不再出现 "LocationServices.API is not available" 错误

### 场景 2: 网络定位测试

**目的**: 验证基于基站和 WiFi 的定位

**步骤**:

1. 在设置中关闭 GPS，只保留网络定位
2. 重启应用
3. 观察定位过程

**预期结果**:

- 能够快速获取到大概位置（通常 1-3 秒）
- 精度可能较低（500-2000 米），但能正常获取

### 场景 3: GPS 定位测试

**目的**: 验证 GPS 定位功能

**步骤**:

1. 在设置中关闭网络定位，只保留 GPS
2. 到户外空旷地带
3. 重启应用
4. 等待 GPS 定位

**预期结果**:

- GPS 首次定位可能需要 30-60 秒
- 精度较高（通常 3-10 米）
- 在室内可能定位失败，这是正常现象

### 场景 4: 权限测试

**目的**: 验证权限处理机制

**步骤**:

1. 在应用设置中撤销定位权限
2. 重启应用
3. 应用会请求权限
4. 选择"拒绝"

**预期结果**:

- 应用显示权限被拒绝的提示
- 自动使用模拟位置（广州市巨大产业园）
- 不会崩溃或无限循环请求权限

### 场景 5: 定位服务关闭测试

**目的**: 验证定位服务关闭时的处理

**步骤**:

1. 在系统设置中完全关闭定位服务
2. 重启应用

**预期结果**:

- 应用检测到定位服务未开启
- 显示相应的错误提示
- 使用模拟位置作为回退

### 场景 6: 弱网络环境测试

**目的**: 验证网络条件差时的定位性能

**步骤**:

1. 切换到较差的网络环境（2G 或弱 WiFi）
2. 重启应用
3. 观察定位耗时

**预期结果**:

- 网络定位可能较慢，但最终能成功
- 超时后会自动尝试其他定位方式
- 不会无限等待

## 关键日志检查

### 成功的日志示例：

```
📱 请求录音权限...
开始初始化位置信息...
开始获取真实位置...
位置服务状态: true
📍 开始位置获取流程...
设备定位服务状态: true
权限详细状态: {"android": {"accuracy": "fine"}, "granted": true, "status": "granted"}
🔧 使用原生定位模块...
策略1: 尝试原生最后已知位置...
✅ 原生最后已知位置获取成功: {latitude: 23.1291, longitude: 113.2644, accuracy: 65}
真实位置初始化成功: {latitude: 23.1291, longitude: 113.2644, address: "广东省广州市..."}
```

### 需要关注的错误日志：

- `PERMISSION_DENIED`: 权限问题
- `LOCATION_DISABLED`: 定位服务未开启
- `NO_PROVIDER`: 没有可用的定位提供者
- `TIMEOUT`: 定位超时

## 性能指标

### 定位成功率

- 目标：> 95%（在有网络的环境下）
- 统计：成功定位次数 / 总尝试次数

### 定位耗时

- 网络定位：< 5 秒
- GPS 定位：< 30 秒（首次）
- 混合定位：< 10 秒

### 精度要求

- 网络定位：100-2000 米可接受
- GPS 定位：< 50 米理想，< 100 米可接受

## 常见问题排查

### 1. 模块未注册

**现象**: 日志显示 "Native location module is not available"
**解决**: 检查 MainApplication.kt 中是否正确添加了 NativeLocationPackage

### 2. 权限问题

**现象**: 一直显示权限被拒绝
**解决**:

- 检查 app.json 中的权限配置
- 手动在设备设置中授予定位权限

### 3. 定位服务问题

**现象**: 提示"定位服务未开启"
**解决**: 在设备设置 > 位置信息中开启定位服务

### 4. 编译错误

**现象**: Kotlin 编译失败
**解决**:

- 检查包名是否正确
- 确保所有依赖都正确导入

## 回退机制验证

测试每个回退层级：

1. **原生最后已知位置** → 成功则停止
2. **原生当前位置（低精度）** → 成功则停止
3. **原生当前位置（中精度）** → 成功则停止
4. **原生当前位置（高精度）** → 成功则停止
5. **Expo Location 最后已知位置** → 成功则停止
6. **Expo Location 当前位置（多种配置）** → 成功则停止
7. **模拟位置** → 最终回退

## 测试报告模板

```
测试时间: ___________
设备型号: ___________
Android 版本: _______
网络状态: ___________

□ 场景1 - 正常定位: 通过/失败 (耗时: ___秒)
□ 场景2 - 网络定位: 通过/失败 (耗时: ___秒)
□ 场景3 - GPS 定位: 通过/失败 (耗时: ___秒)
□ 场景4 - 权限测试: 通过/失败
□ 场景5 - 服务关闭: 通过/失败
□ 场景6 - 弱网络: 通过/失败 (耗时: ___秒)

定位精度: _______ 米
总体评价: □优秀 □良好 □一般 □需改进

问题记录:
_________________________________
```

---

**版本**: v1.0.0  
**更新**: 2025年7月28日
