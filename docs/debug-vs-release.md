# Debug 版本 vs Release 版本说明

## 版本区别

### Debug 版本（测试版）

- **用途**：开发和测试阶段使用
- **特点**：
  - 包含调试信息
  - 未经过代码优化和混淆
  - 文件体积较大
  - 可以连接调试工具
  - 不需要签名（使用默认调试签名）
  - 可以直接安装到任何设备

### Release 版本（正式版）

- **用途**：发布到应用商店或分发给用户
- **特点**：
  - 代码经过优化和混淆
  - 文件体积较小
  - 运行速度更快
  - 需要正式签名
  - 更安全（代码被混淆）
  - 适合上架应用商店

## 构建命令

### 构建 Debug 版本

```bash
./scripts/build-android.sh debug
```

### 构建 Release 版本

```bash
# 1. 先生成签名密钥（只需执行一次）
keytool -genkeypair -v -storetype PKCS12 -keystore zipco-release.keystore -alias zipco -keyalg RSA -keysize 2048 -validity 10000

# 2. 构建 Release APK
./scripts/build-android.sh release
```

## 关于应用图标问题

您的应用图标已经正确配置为 `assets/logo.png`。如果图标没有更新，可能是因为：

1. **缓存问题**
   - 清理项目缓存：`npx expo start -c`
   - 删除 android 目录重新生成：`rm -rf android && npx expo prebuild --platform android`

2. **图标要求**
   - 确保 logo.png 是 1024x1024 像素
   - PNG 格式，透明背景
   - 不要有圆角（系统会自动处理）

3. **Android 自适应图标**
   - Android 还需要 `adaptive-icon.png` 作为前景图
   - 可以将 logo.png 复制为 adaptive-icon.png

## 更新图标步骤

```bash
# 1. 确保图标文件正确
cp assets/logo.png assets/adaptive-icon.png

# 2. 清理缓存
npx expo start -c

# 3. 重新生成 Android 项目
rm -rf android
npx expo prebuild --platform android

# 4. 重新构建
./scripts/build-android.sh debug
```

## 应用名称修改

如果需要修改应用名称（当前是"办公助手preview"），在 app.json 中修改：

```json
"name": "移动办公助手"  // 修改为您想要的名称
```

## 建议

1. **开发阶段**：使用 Debug 版本快速测试
2. **内部测试**：使用 Debug 版本分发给测试人员
3. **正式发布**：必须使用 Release 版本
4. **应用商店**：只接受签名的 Release 版本

---

**提示**：首次建议先构建 Debug 版本测试所有功能，确认无误后再构建 Release 版本。
