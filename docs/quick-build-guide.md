# 快速构建指南

## 方法一：本地构建（推荐先使用）

### 1. 构建 Debug APK（最快速）

```bash
# 使用构建脚本
./scripts/build-android.sh debug

# 或手动执行
npx expo prebuild --platform android
cd android
./gradlew assembleDebug
cd ..
```

APK 位置：`android/app/build/outputs/apk/debug/app-debug.apk`

### 2. 生成签名密钥

```bash
# 在项目根目录执行
keytool -genkeypair -v -storetype PKCS12 -keystore zipco-release.keystore -alias zipco -keyalg RSA -keysize 2048 -validity 10000
```

输入信息示例：

- 密钥库密码：设置一个强密码（如：ZipCo@2024!）
- 密钥密码：可以与密钥库密码相同
- 姓名：您的姓名或公司名
- 组织单位：开发部
- 组织名称：GZAI168
- 城市：广州
- 省份：广东
- 国家代码：CN

### 3. 构建 Release APK

创建 `android/keystore.properties` 文件：

```properties
storePassword=您的密钥库密码
keyPassword=您的密钥密码
keyAlias=zipco
storeFile=../../zipco-release.keystore
```

然后构建：

```bash
./scripts/build-android.sh release
```

## 方法二：使用 EAS Build（需要 Expo 账号）

### 1. 创建 Expo 账号

访问 https://expo.dev 注册账号

### 2. 初始化项目

```bash
# 登录
eas login

# 初始化项目（会自动生成项目 ID）
eas init
```

### 3. 构建 APK

```bash
# 构建测试版
eas build --platform android --profile preview

# 构建生产版
eas build --platform android --profile production
```

## 常见问题解决

### 问题：Invalid UUID appId

**解决方案**：

1. 确保已登录 Expo 账号：`eas login`
2. 运行 `eas init` 初始化项目
3. 检查 app.json 中的 owner 字段是否与您的 Expo 用户名一致

### 问题：构建失败

**解决方案**：

1. 清理缓存：`npx expo start -c`
2. 删除 android 目录重新生成：`rm -rf android && npx expo prebuild --platform android`
3. 清理 gradle 缓存：`cd android && ./gradlew clean`

### 问题：签名文件找不到

**解决方案**：

1. 确保 keystore 文件在项目根目录
2. 检查 keystore.properties 中的路径是否正确
3. 文件名和别名要一致

## 测试 APK

### 在模拟器测试

```bash
# 启动模拟器后
adb install android/app/build/outputs/apk/debug/app-debug.apk
```

### 在真机测试

1. 开启开发者模式和 USB 调试
2. 连接手机到电脑
3. 执行：`adb install android/app/build/outputs/apk/debug/app-debug.apk`

## 下一步

1. **测试功能**：确保所有功能正常工作
2. **优化性能**：检查 APK 大小和启动速度
3. **准备上架材料**：
   - 应用图标（512x512 和 1024x1024）
   - 应用截图（至少 2 张）
   - 应用描述
   - 隐私政策链接

---

**提示**：首次建议使用本地构建方法，更快速且不需要网络。测试通过后再使用 EAS Build 进行正式构建。
