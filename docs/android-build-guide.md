# Android APK 构建和签名指南

## 项目配置说明

### 1. 包名配置

- **包名**: `com.gzai168.zipco`
- **应用名称**: 移动办公助手
- **版本号**: 1.0.0
- **版本代码**: 1

包名已在 `app.json` 中配置完成。这个包名格式正确，符合 Android 应用的命名规范。

### 2. 权限配置

已配置的权限：

- `RECORD_AUDIO` - 录音功能
- `READ_EXTERNAL_STORAGE` - 读取文件
- `WRITE_EXTERNAL_STORAGE` - 保存文件

## 生成签名密钥

### 方法一：使用 EAS Build（推荐）

1. **安装 EAS CLI**

```bash
npm install -g eas-cli
```

2. **登录 Expo 账号**

```bash
eas login
```

3. **配置项目**

```bash
eas build:configure
```

4. **构建 APK**

```bash
# 构建生产版本 APK
eas build --platform android --profile production

# 构建预览版本 APK（用于测试）
eas build --platform android --profile preview
```

EAS Build 会自动处理签名，首次构建时会为您生成签名密钥。

### 方法二：手动生成签名密钥

1. **生成密钥库文件**

```bash
keytool -genkeypair -v -storetype PKCS12 -keystore zipco-release.keystore -alias zipco -keyalg RSA -keysize 2048 -validity 10000
```

执行时需要输入以下信息：

- 密钥库密码（记住这个密码，非常重要）
- 密钥密码（可以与密钥库密码相同）
- 您的姓名
- 组织单位名称
- 组织名称
- 城市或区域名称
- 省/市/自治区名称
- 国家/地区代码（CN）

2. **保存密钥信息**
   创建 `keystore.properties` 文件（不要提交到版本控制）：

```properties
storePassword=您的密钥库密码
keyPassword=您的密钥密码
keyAlias=zipco
storeFile=../zipco-release.keystore
```

3. **备份密钥库**

- 将 `zipco-release.keystore` 文件备份到安全的地方
- 记录好所有密码信息
- **重要**：丢失密钥库将无法更新应用

## 本地构建 APK

### 前置要求

1. 安装 Android Studio
2. 配置 Android SDK
3. 设置环境变量

### 构建步骤

1. **生成 Android 项目**

```bash
npx expo prebuild --platform android
```

2. **进入 Android 目录**

```bash
cd android
```

3. **构建 Debug APK**

```bash
./gradlew assembleDebug
```

4. **构建 Release APK**

```bash
./gradlew assembleRelease
```

APK 文件位置：

- Debug: `android/app/build/outputs/apk/debug/app-debug.apk`
- Release: `android/app/build/outputs/apk/release/app-release.apk`

## 应用上架准备

### 1. 应用图标

确保以下图标文件已准备好：

- `assets/icon.png` - 1024x1024 应用图标
- `assets/adaptive-icon.png` - 1024x1024 自适应图标前景
- `assets/splash-icon.png` - 启动屏图标

### 2. 应用截图

准备以下截图用于应用商店：

- 手机截图（至少2张）：1080x1920 或 1080x2340
- 平板截图（可选）：1200x1920 或 1600x2560
- 功能图形：1024x500

### 3. 应用描述

准备以下文本内容：

- 应用简短描述（80字符以内）
- 应用详细描述（4000字符以内）
- 更新说明
- 关键词

### 4. 隐私政策

- 准备隐私政策文档
- 发布到可访问的网址

### 5. 年龄分级

根据应用内容选择合适的年龄分级

## 常见问题

### Q: 包名可以修改吗？

A: 一旦应用发布到商店，包名就不能修改了。请确保包名正确。

### Q: 如何查看签名信息？

A: 使用以下命令查看 APK 签名：

```bash
keytool -printcert -jarfile app-release.apk
```

### Q: 如何减小 APK 大小？

A:

1. 启用 ProGuard 混淆
2. 使用 App Bundle 格式
3. 优化图片资源
4. 移除未使用的依赖

### Q: 如何测试 Release APK？

A:

```bash
# 安装到设备
adb install app-release.apk

# 查看日志
adb logcat
```

## 下一步操作

1. **生成签名密钥**（如果还没有）
2. **构建 APK**
3. **在真机上测试**
4. **准备上架材料**
5. **提交到应用商店**

## 安全提醒

⚠️ **重要安全提示**：

- 永远不要将密钥库文件提交到版本控制系统
- 妥善保管密钥库文件和密码
- 为不同的应用使用不同的签名密钥
- 定期备份密钥库文件

## 相关命令速查

```bash
# 查看 Java keytool 版本
keytool -version

# 查看密钥库信息
keytool -list -v -keystore zipco-release.keystore

# 清理构建缓存
cd android && ./gradlew clean

# 查看构建任务
./gradlew tasks

# 构建并安装到设备
./gradlew installRelease
```

---

如有任何问题，请参考 [Expo 官方文档](https://docs.expo.dev/) 或 [Android 开发者文档](https://developer.android.com/)。
