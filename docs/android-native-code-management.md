# Expo项目Android原生代码管理指南

## 📋 概述

本文档详细说明了在Expo项目中如何正确管理Android原生代码，包括git版本控制策略、最佳实践和调试工作流。

## 🎯 核心结论

### ✅ 推荐配置

- **android/和ios/目录应该在.gitignore中被忽略**
- **这是Expo官方推荐的最佳实践**
- **符合Continuous Native Generation (CNG)原则**

## 📖 官方文档依据

### Expo官方推荐

根据Expo官方文档：

- 使用`npx expo prebuild`时，将android和ios目录添加到.gitignore
- EAS Build会自动运行prebuild生成全新的原生目录
- 这确保每次构建都是干净、一致的状态

### .gitignore配置示例

```gitignore
# Expo原生目录
android/
ios/

# 保持这些目录不被git追踪
.expo/
```

## 🔍 为什么不追踪android目录

### 1. 文件数量庞大

- android目录包含thousands of generated files
- 大部分是构建工具自动生成的中间文件
- 追踪会大幅增加仓库大小

### 2. 频繁变化

- 每次运行`npx expo prebuild`都会重新生成
- 配置变更会导致大量文件修改
- 容易产生无意义的git diff

### 3. 团队协作问题

- 不同开发环境生成的文件可能有细微差异
- 容易产生大量合并冲突
- 影响代码审查效率

### 4. 云构建兼容性

- EAS Build不使用本地android目录
- 总是重新生成，确保环境一致性
- 本地追踪的修改不会影响生产构建

## 🛠️ 原生代码修改策略

### 方法1: 使用Config Plugins（推荐）

#### 基础配置

```javascript
// app.config.js
export default {
  expo: {
    plugins: [
      [
        'expo-build-properties',
        {
          android: {
            compileSdkVersion: 34,
            targetSdkVersion: 34,
            buildToolsVersion: '34.0.0',
            minSdkVersion: 24,
          },
        },
      ],
    ],
  },
};
```

#### 权限配置

```json
// app.json
{
  "expo": {
    "android": {
      "permissions": [
        "android.permission.RECORD_AUDIO",
        "android.permission.ACCESS_FINE_LOCATION"
      ]
    }
  }
}
```

### 方法2: 选择性追踪关键文件

如果必须修改原生文件，可以选择性追踪：

```gitignore
# .gitignore
android/
ios/

# 但追踪关键配置文件
!android/app/build.gradle
!android/build.gradle
!android/gradle.properties
!android/app/src/main/AndroidManifest.xml
```

**⚠️ 注意：此方法不推荐，可能导致配置冲突**

### 方法3: 补丁文件备份

```bash
# 创建补丁备份目录
mkdir -p patches/android

# 备份重要修改
cp android/app/build.gradle patches/android/
cp android/app/src/main/AndroidManifest.xml patches/android/

# 提交备份文件
git add patches/
git commit -m "backup: android native customizations"
```

## 🔄 开发调试工作流

### 1. 标准开发流程

```bash
# 1. 修改配置文件
vim app.json  # 或 app.config.js

# 2. 清理并重新生成原生代码
npx expo prebuild --clean

# 3. 本地调试
npx expo run:android

# 4. 如需Android Studio调试
open -a "Android Studio" android/
```

### 2. 版本兼容性检查

```bash
# 检查当前配置与标准的差异
npx expo prebuild --platform android --no-install

# 检查git状态（应该显示android目录未被追踪）
git status android/ 2>/dev/null || echo "android目录未被git追踪 ✅"
```

### 3. 依赖更新流程

```bash
# 1. 更新Expo SDK
npx expo upgrade

# 2. 清理原生目录
npx expo prebuild --clean

# 3. 重新构建
yarn android
```

## 🔧 常见问题解决

### Q1: 原生代码修改后丢失了怎么办？

**A:** 这是正常现象，修改应该通过以下方式持久化：

- 使用Config Plugins
- 修改app.json/app.config.js
- 使用补丁文件备份重要修改

### Q2: 需要修改build.gradle文件怎么办？

**A:** 推荐方案：

```javascript
// 使用expo-build-properties插件
{
  "plugins": [
    [
      "expo-build-properties",
      {
        "android": {
          "enableProguardInReleaseBuilds": true,
          "enableShrinkResourcesInReleaseBuilds": true
        }
      }
    ]
  ]
}
```

### Q3: Android Studio提示找不到项目怎么办？

**A:** 运行以下命令：

```bash
npx expo prebuild --platform android
open -a "Android Studio" android/
```

## 📁 项目文件结构

### 推荐的目录结构

```
project-root/
├── src/                    # React Native代码
├── docs/                   # 项目文档
├── patches/               # 原生代码补丁（可选）
│   └── android/           # Android配置备份
├── app.json               # Expo配置
├── .gitignore             # Git忽略规则
├── android/               # 🚫 不追踪（在.gitignore中）
└── ios/                   # 🚫 不追踪（在.gitignore中）
```

### .gitignore完整配置

```gitignore
# Dependencies
node_modules/

# Expo
.expo/
expo-env.d.ts

# Native directories (重要：不追踪)
android/
ios/

# Environment Variables
.env
.env*.local

# Build outputs
web-build/
dist/

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
```

## 🚀 EAS Build配置

### eas.json示例

```json
{
  "cli": {
    "version": ">= 5.4.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal"
    },
    "preview": {
      "distribution": "internal"
    },
    "production": {}
  },
  "submit": {
    "production": {}
  }
}
```

### 构建命令

```bash
# 开发构建
eas build --profile development --platform android

# 生产构建
eas build --profile production --platform android
```

## 📝 检查清单

### 项目设置检查

- [ ] android/目录在.gitignore中
- [ ] ios/目录在.gitignore中
- [ ] app.json配置正确
- [ ] 使用Config Plugins管理原生配置
- [ ] EAS Build配置完整

### 开发流程检查

- [ ] 修改配置后运行`npx expo prebuild --clean`
- [ ] 重要原生修改有备份策略
- [ ] 团队成员了解工作流
- [ ] CI/CD配置正确

## 🔗 相关资源

### 官方文档

- [Expo Prebuild](https://docs.expo.dev/workflow/prebuild/)
- [Config Plugins](https://docs.expo.dev/config-plugins/introduction/)
- [EAS Build](https://docs.expo.dev/build/introduction/)

### 工具链

- [expo-build-properties](https://docs.expo.dev/versions/latest/sdk/build-properties/)
- [Continuous Native Generation](https://docs.expo.dev/workflow/continuous-native-generation/)

## 📅 维护记录

| 日期       | 操作     | 说明                                     |
| ---------- | -------- | ---------------------------------------- |
| 2025-01-23 | 创建文档 | 初始版本，基于LinearGradient错误修复经验 |
|            | 验证配置 | 确认当前android目录与Expo标准一致        |

---

**最后更新：** 2025年1月23日  
**适用版本：** Expo SDK 52+  
**维护者：** 开发团队
