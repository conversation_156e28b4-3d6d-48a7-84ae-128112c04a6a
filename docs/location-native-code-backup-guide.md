# Android 定位模块原生代码备份指南

## 📋 项目背景

由于 Expo 的 `android` 目录在每次 `expo prebuild` 时都会重新生成，我们需要将自定义的原生代码（包括新增的定位模块）保存到 `native-code-backup` 目录，并通过脚本进行备份和恢复。

## 🗂️ 备份目录结构

```
native-code-backup/
├── AndroidManifest.xml           # Android 权限配置
├── MainApplication.kt           # 主应用文件（注册所有模块）
├── audio_module/               # 音频录制模块
│   ├── AudioStreamManager.kt
│   ├── RealtimeAudioModule.kt
│   └── RealtimeAudioPackage.kt
├── floating_module/            # 浮窗模块
│   ├── FloatingWindowManager.kt
│   ├── FloatingWindowModule.kt
│   └── FloatingWindowPackage.kt
└── location_module/            # 🆕 定位模块
    ├── NativeLocationModule.kt
    └── NativeLocationPackage.kt
```

## 🔧 备份脚本功能

### 可用命令

```bash
# 检查所有模块状态
yarn check

# 备份当前原生代码
yarn backup

# 恢复备份的原生代码
yarn restore
```

### 完整操作流程

```bash
# 1. 开发完成后，备份原生代码
yarn backup

# 2. 运行 Expo prebuild（这会覆盖 android 目录）
expo prebuild

# 3. 立即恢复自定义代码
yarn restore

# 4. 验证恢复结果
yarn check

# 5. 构建项目
yarn android
```

## 📊 模块检查结果解读

### ✅ 正常状态

```
🔍 检查自定义原生代码状态...
✅ 音频模块存在
✅ 浮窗模块存在
✅ 定位模块存在
✅ MainApplication.kt 包含自定义音频包
✅ MainApplication.kt 包含自定义浮窗包
✅ MainApplication.kt 包含自定义定位包
✅ 录音权限已配置
✅ 浮窗权限已配置

📁 备份文件状态:
✅ 音频模块备份存在
✅ 浮窗模块备份存在
✅ MainApplication.kt备份存在
✅ AndroidManifest.xml备份存在
✅ 定位模块备份存在
```

### ❌ 异常状态示例

```
❌ 定位模块缺失！
❌ MainApplication.kt 缺少自定义定位包配置！
```

## 🆕 定位模块详细信息

### 文件说明

- \*\*NativeLocationModule
