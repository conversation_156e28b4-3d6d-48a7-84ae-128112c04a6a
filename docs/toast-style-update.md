# Toast 组件样式更新

## 概述

根据 Figma 设计稿要求，我们已经将 Toast 组件的样式从原来的彩色背景样式更新为新的白色胶囊样式。

## 设计变更

### 原始样式

- 彩色背景（成功绿色、错误红色等）
- 方形圆角（8px）
- 白色文字
- 较大的内边距

### 新样式（基于 Figma 设计）

- 白色背景 (#FFFFFF)
- 胶囊形状（999px 圆角）
- 深色文字 (#414352)
- **圆形实体背景图标**（16x16px，8px圆角）
- 白色图标图案（10px）
- 阴影效果
- 自适应宽度
- 紧凑的内边距（8px垂直，16px水平）

## 技术实现

### 主要修改文件

1. **src/components/common/Toast/index.tsx**
   - 更新了 Toast 配置
   - 添加了图标支持
   - 重新设计了样式

2. **App.tsx**
   - 配置了自定义 Toast 配置

### 样式规范

```typescript
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 999,
    shadowColor: 'rgba(100, 103, 122, 0.24)',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 1,
    shadowRadius: 24,
    elevation: 8,
  },
  // ...其他样式
});
```

### 图标配置

- **成功**: 绿色圆形背景 (#21BF55) + 白色 checkmark 图标
- **错误**: 红色圆形背景 (#FF3B30) + 白色 close 图标
- **信息**: 蓝色圆形背景 (#007AFF) + 白色 information 图标
- **警告**: 橙色圆形背景 (#FF9500) + 白色 warning 图标

### 关键样式更新

1. **图标容器**: 20x20px，10px圆角，彩色背景
2. **图标大小**: 12px白色图标
3. **容器尺寸**:
   - 最小高度: 40px
   - 内边距: 12px垂直，20px水平
   - 图标右边距: 10px
4. **文字大小**:
   - 主标题: 16px，行高24px
   - 副标题: 14px，行高20px
5. **阴影效果**: 严格按照设计稿 `box-shadow: 0px 8px 24px 0px rgba(100,103,122,0.24)`
6. **容器宽度**: 使用 `alignSelf: 'center'` 实现自适应宽度

## 使用方式

### 基本使用

Toast 的基本使用方式保持不变：

```typescript
import { ToastService } from '../components/common/Toast';

// 显示成功消息
ToastService.success('复制成功');

// 显示带详细信息的成功消息
ToastService.success('操作成功', '您的操作已经完成');

// 其他类型
ToastService.error('操作失败');
ToastService.info('提示信息');
ToastService.warning('警告');
```

### 配置选项

新增了全局配置功能，可以控制是否显示详细信息（text2）：

```typescript
// 设置是否显示详细信息，默认为 false
ToastService.setShowMessage(true); // 显示详细信息
ToastService.setShowMessage(false); // 只显示主标题

// 或者使用完整配置
ToastService.configure({
  showMessage: true, // 是否显示text2
});

// 获取当前配置
const config = ToastService.getConfig();
```

### 配置说明

- **showMessage**: 控制是否显示 text2（副标题/详细信息）
  - `true`: 显示完整的Toast，包括主标题和副标题
  - `false`: 只显示主标题（默认值）

### 详细使用示例

#### 默认行为（不显示详细信息）

```typescript
// 默认情况下，只显示主标题
ToastService.success('复制成功', '这条详细信息不会显示');
// 结果：只显示 "复制成功"
```

#### 开启详细信息显示

```typescript
// 开启详细信息显示
ToastService.setShowMessage(true);

// 现在会显示完整信息
ToastService.success('操作成功', '您的操作已经完成');
// 结果：显示 "操作成功" 和 "您的操作已经完成"

// 关闭详细信息显示
ToastService.setShowMessage(false);
```

## 实际应用场景

### 场景1：简洁模式（默认）

适用于需要简洁提示的场景：

```typescript
// 文件操作
ToastService.success('复制成功');
ToastService.success('删除成功');
ToastService.success('保存成功');

// 网络操作
ToastService.error('网络错误');
ToastService.info('正在加载');
```

### 场景2：详细模式

适用于需要更多信息的场景：

```typescript
// 开启详细信息
ToastService.setShowMessage(true);

// 文件操作
ToastService.success('上传成功', '文件已保存到云端');
ToastService.error('上传失败', '请检查网络连接后重试');

// 用户操作
ToastService.info('权限提醒', '需要相机权限才能拍照');
ToastService.warning('存储空间不足', '请清理设备存储空间');
```

### 场景3：动态切换

根据用户设置或应用状态动态切换：

```typescript
// 根据用户设置
const userPreference = getUserSettings();
ToastService.setShowMessage(userPreference.showDetailedToasts);

// 根据应用模式
if (isDebugMode) {
  ToastService.setShowMessage(true); // 开发模式显示详细信息
} else {
  ToastService.setShowMessage(false); // 生产模式简洁显示
}
```

## 最佳实践

1. **默认使用简洁模式**：大多数情况下，简洁的提示更符合用户体验
2. **重要操作显示详细信息**：对于重要的操作结果，可以临时开启详细信息
3. **错误信息建议显示详细**：错误提示通常需要更多信息帮助用户理解问题
4. **保持一致性**：在同一个功能模块内保持Toast显示模式的一致性

## 兼容性

- 保持了原有的 API 接口
- 所有现有的 Toast 调用都会自动使用新样式
- 无需修改现有代码

## 注意事项

1. 新样式使用了 Ionicons 图标，确保项目中已安装 `@expo/vector-icons`
2. 阴影效果在 Android 和 iOS 上可能略有差异
3. 字体使用了 PingFang SC，在不支持的设备上会回退到系统默认字体
