# 实时音频转写成功实现 - 技术文档

## 📋 项目概述

### 🎯 目标

实现基于真实麦克风输入的实时语音转写功能，集成讯飞实时ASR服务，确保音频数据的真实性和实时性。

### ⚠️ 核心要求

- **绝对禁止**使用任何模拟、合成或非实时音频数据
- 必须获取真实的麦克风实时音频流
- 支持HarmonyOS设备
- 与讯飞实时ASR服务集成

## 🔍 问题背景

### 初始问题

1. **expo-av局限性**：只能在停止录音时获取音频文件，无法提供实时音频流
2. **第三方库失败**：`expo-audio-stream`等库在项目中集成失败，构建错误频发
3. **模拟器限制**：Android模拟器无法提供真实麦克风输入，导致测试困难

### 关键发现

- 现有React Native音频库普遍不支持真正的实时音频流
- HarmonyOS设备需要特殊的USB调试配置
- 自定义原生模块是唯一可行的解决方案

## 🛠️ 技术方案

### 方案对比

| 方案              | 优点                          | 缺点                     | 结果            |
| ----------------- | ----------------------------- | ------------------------ | --------------- |
| expo-av           | 稳定、文档完善                | 仅支持文件录音，无实时流 | ❌ 不适用       |
| expo-audio-stream | 声称支持实时流                | 构建失败，依赖复杂       | ❌ 集成失败     |
| 自定义原生模块    | 完全控制，直接访问AudioRecord | 开发复杂度高             | ✅ **最终采用** |

### 最终架构

```
React Native App
    ↓
useRealtimeAudioRecorder Hook
    ↓
RealtimeAudioRecorder (TypeScript)
    ↓
RealtimeAudio Native Module (Kotlin)
    ↓
AudioStreamManager (Kotlin)
    ↓
Android AudioRecord API
    ↓
真实麦克风输入
```

## 💻 核心实现

### 1. Android原生模块

#### `RealtimeAudioModule.kt`

```kotlin
package com.gzai168.zipco.audio

import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule

class RealtimeAudioModule(private val reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    private val audioManager = AudioStreamManager()

    override fun getName(): String = "RealtimeAudio"

    @ReactMethod
    fun startRecording(config: ReadableMap, promise: Promise) {
        // 启动实时录音逻辑
    }

    @ReactMethod
    fun stopRecording(promise: Promise) {
        // 停止录音逻辑
    }

    private fun sendEvent(eventName: String, data: Any) {
        reactContext
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            .emit(eventName, data)
    }
}
```

#### `AudioStreamManager.kt`

```kotlin
package com.gzai168.zipco.audio

import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.util.Base64

class AudioStreamManager {
    private var audioRecord: AudioRecord? = null
    private val isRecording = AtomicBoolean(false)
    private var recordingThread: Thread? = null

    fun startRecording(sampleRate: Int = 16000, interval: Int = 250, onAudioData: (String) -> Unit, onError: (String) -> Unit) {
        // 配置AudioRecord
        // 创建录音线程
        // 实时读取音频数据
        // Base64编码并回调
    }

    private fun recordingLoop(bufferSize: Int, interval: Int, onAudioData: (String) -> Unit, onError: (String) -> Unit) {
        // 持续读取音频数据
        // 累积到指定字节数后发送
        // 错误处理和资源清理
    }
}
```

### 2. React Native接口

#### `RealtimeAudioRecorder.ts`

```typescript
import {
  NativeModules,
  NativeEventEmitter,
  PermissionsAndroid,
} from 'react-native';

const { RealtimeAudio } = NativeModules;

export class RealtimeAudioRecorder {
  private audioEmitter: NativeEventEmitter | null = null;

  async startRecording(config: AudioConfig): Promise<boolean> {
    // 权限检查
    // 启动原生录音
    // 设置事件监听
  }

  async stopRecording(): Promise<boolean> {
    // 停止录音
    // 清理资源
  }

  private base64ToFloat32Array(base64: string): Float32Array {
    // Base64解码为音频数据
  }
}
```

#### `useRealtimeAudioRecorder.ts`

```typescript
export const useRealtimeAudioRecorder = (
  config?: RealtimeAudioRecorderConfig
) => {
  const [isRecording, setIsRecording] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);

  const startRecording = useCallback(async (): Promise<boolean> => {
    // 启动录音逻辑
    // 音频数据处理
    // 状态管理
  }, []);

  return {
    isRecording,
    audioLevel,
    startRecording,
    stopRecording,
    // ...其他方法
  };
};
```

### 3. 模块注册

#### `RealtimeAudioPackage.kt`

```kotlin
class RealtimeAudioPackage : ReactPackage {
    override fun createNativeModules(reactContext: ReactApplicationContext): List<NativeModule> {
        return listOf(RealtimeAudioModule(reactContext))
    }
}
```

#### `MainApplication.kt`

```kotlin
override fun getPackages(): List<ReactPackage> {
    val packages = PackageList(this).packages.toMutableList()
    packages.add(RealtimeAudioPackage()) // 添加自定义音频包
    return packages
}
```

## 🔧 HarmonyOS调试配置

### 开发者模式启用

```bash
设置 → 关于手机 → 版本号（连续点击7次）
# 或
设置 → 系统和更新 → 关于手机 → 版本号（连续点击7次）
```

### USB调试设置

```bash
设置 → 系统 → 开发人员选项 → USB调试 ✅
设置 → 搜索"HDB" → 允许通过HDB连接设备 ✅
设置 → 搜索"HDB" → 允许HiSuite通过HDB连接设备 ✅
```

### 连接验证

```bash
# 重启ADB服务
adb kill-server && adb start-server

# 检查设备连接
adb devices
# 期待输出：PKT0220402001426    device

# 在真机运行
yarn android
```

## 🚨 关键问题与解决方案

### 1. expo prebuild覆盖自定义文件

**问题**：`npx expo prebuild`会删除`android/app/src/main/java`下的自定义文件

**解决方案**：

- 在prebuild后重新创建所有Kotlin文件
- 考虑将自定义模块移到prebuild不会影响的目录
- 或者禁用prebuild的clean功能

### 2. ReactModule注解错误

**问题**：`Unresolved reference: ReactModule`

**解决方案**：

- 移除`@ReactModule`注解（新版React Native不需要）
- 或添加正确的import：`import com.facebook.react.module.annotations.ReactModule`

### 3. 授权问题

**问题**：设备显示`unauthorized`状态

**解决方案**：

- 确保手机弹窗中勾选"始终允许使用这台计算机进行调试"
- 重启ADB服务触发重新授权
- 检查USB连接模式（选择文件传输）

### 4. 音频数据全零问题

**问题**：在模拟器上所有音频数据都是0

**解决方案**：

- **必须在真机上测试**
- 确保录音权限已授予
- 验证AudioRecord初始化成功

## 📊 测试验证

### 成功指标

```javascript
// 模拟器上（静音）
"first5Samples": [0, 0, 0, 0, 0]
"level": "0.000"

// 真机上（真实音频）
"first5Samples": [0.045, -0.023, 0.067, -0.031, 0.089]
"level": "0.051"
```

### 讯飞集成验证

- ✅ WebSocket连接成功
- ✅ 音频数据实时发送
- ✅ 转写结果实时接收
- ✅ 无"Client idle timeout"错误

## 🏗️ 文件结构

```
android/app/src/main/java/com/gzai168/zipco/
├── audio/
│   ├── RealtimeAudioModule.kt      # React Native桥接模块
│   ├── AudioStreamManager.kt       # 音频录制核心逻辑
│   └── RealtimeAudioPackage.kt     # 模块包注册
└── MainApplication.kt              # 应用入口（已修改）

src/
├── hooks/
│   └── useRealtimeAudioRecorder.ts # React Hook接口
├── services/audio/
│   └── RealtimeAudioRecorder.ts    # TypeScript封装类
└── components/recording/
    └── RecordingScreen.tsx         # UI组件（已集成）
```

## ✅ 成功成果

### 技术成就

1. ✅ **真实音频流**：成功获取麦克风实时音频数据
2. ✅ **HarmonyOS支持**：完整的调试和运行流程
3. ✅ **讯飞集成**：实时语音转写正常工作
4. ✅ **自定义模块**：证明了原生模块开发的可行性

### 用户价值

- 🎯 核心功能实现：实时录音转写完全正常
- 📱 设备兼容性：支持HarmonyOS设备
- 🔄 实时性保证：无延迟的音频流处理
- 🛡️ 数据真实性：绝对避免合成音频

## 🔄 后续优化计划

### 1. 代码清理（优先级：高）

- [ ] 删除未使用的expo-audio-stream相关代码
- [ ] 移除调试日志中的敏感信息
- [ ] 统一代码风格和注释规范

### 2. 性能优化（优先级：中）

- [ ] 优化音频缓冲区管理
- [ ] 减少Base64编解码开销
- [ ] 改进内存使用效率

### 3. 错误处理（优先级：中）

- [ ] 完善权限被拒绝的处理流程
- [ ] 添加音频设备异常的恢复机制
- [ ] 增强网络断线重连逻辑

### 4. 功能扩展（优先级：低）

- [ ] 支持音频格式配置
- [ ] 添加音频质量监控
- [ ] 实现录音暂停/恢复功能

## 📝 技术债务

1. **Expo prebuild兼容性**：需要找到更好的自定义模块集成方案
2. **权限管理**：Android 6.0+的运行时权限处理可以改进
3. **错误日志**：部分Kotlin deprecated警告需要修复
4. **跨平台**：当前只支持Android，iOS支持需要额外开发

## 🎉 结论

经过详细的技术攻关，我们成功实现了基于自定义Android原生模块的实时音频转写功能。这个解决方案：

- ✅ **满足核心需求**：真实的麦克风音频流，无任何模拟数据
- ✅ **技术可靠**：直接使用Android AudioRecord API，稳定可控
- ✅ **设备兼容**：成功支持HarmonyOS设备
- ✅ **功能完整**：与讯飞ASR服务完美集成

这个实现为项目奠定了坚实的技术基础，证明了实时语音转写功能的完全可行性。

---

**文档创建时间**：2025年1月25日  
**实现状态**：✅ 成功完成  
**下一步**：代码优化和清理
