# React Navigation导航架构说明

**创建时间**: 2025年8月3日  
**项目**: office-assistant-hybrid  
**文档类型**: 架构设计说明

## 概述

本文档详细说明了项目中React Navigation的导航架构设计，特别是AppNavigator和TabNavigator的职责分工以及页面路由的设计原则。

## 导航器架构

### 整体架构层级

```
NavigationContainer
└── AppNavigator (Stack Navigator - 根导航器)
    ├── 认证流程页面
    │   ├── Login
    │   └── Register
    ├── Main (TabNavigator - 标签页导航器)
    │   ├── RecordingTab (录音)
    │   ├── KnowledgeTab (知识库)
    │   ├── WritingTab (AI写作)
    │   └── ProfileTab (我的)
    └── 跨Tab子页面
        ├── WritingCreation (AI写作创作)
        ├── ProfileEdit (编辑资料)
        ├── Settings (设置)
        ├── RecordingDetail (录音详情)
        └── ...其他功能页面
```

## 导航器职责分工

### AppNavigator (Stack Navigator)

**文件位置**: `src/navigation/AppNavigator.tsx`

**职责**:

- 作为整个应用的根导航器
- 管理应用级别的页面流转
- 处理认证流程 (登录/注册)
- 管理跨Tab的功能页面
- 提供统一的导航栈管理

**特点**:

- 使用Stack Navigator提供堆栈式导航
- 支持push/pop操作
- 统一的头部导航栏配置
- 处理深度链接和复杂导航场景

**包含页面类型**:

1. **认证页面**: Login, Register
2. **主应用**: TabNavigator作为一个整体
3. **跨Tab子页面**: 设置类、详情类、功能类页面

### TabNavigator (Bottom Tab Navigator)

**文件位置**: `src/navigation/TabNavigator.tsx`

**职责**:

- 管理主要功能模块的Tab切换
- 提供底部导航栏
- 管理每个Tab的头部显示
- 处理Tab级别的状态管理

**特点**:

- 使用Bottom Tab Navigator
- 固定的底部导航栏
- 每个Tab可以有独立的头部配置
- 支持Tab图标和标签的状态变化

**包含Tab页面**:

1. **RecordingTab**: 录音功能主页
2. **KnowledgeTab**: 知识库主页
3. **WritingTab**: AI写作主页
4. **ProfileTab**: 个人中心主页

## 页面路由设计原则

### 何时使用AppNavigator

**应该放在AppNavigator的页面**:

1. **跨Tab功能页面** - 不属于特定Tab，可能被多个Tab调用
2. **独立功能页面** - 有完整的业务逻辑，需要独立的导航栈
3. **设置类页面** - 系统级别的配置页面
4. **详情类页面** - 从Tab页面跳转的详情展示页面
5. **认证相关页面** - 登录、注册等应用级页面

**示例**:

```typescript
// AppNavigator中的页面配置
<Stack.Screen
  name="WritingCreation"
  component={WritingCreationScreen}
  options={{
    title: 'AI 写作',
    headerStyle: {
      backgroundColor: '#f4f8ff',
    },
    headerShadowVisible: false,
  }}
/>
```

### 何时使用TabNavigator

**应该放在TabNavigator的页面**:

1. **主功能模块** - 应用的核心功能入口
2. **平级页面** - 用户可以通过底部Tab直接切换的页面
3. **列表/概览页面** - 展示某个功能模块的主要内容

**示例**:

```typescript
// TabNavigator中的Tab配置
<Tab.Screen
  name="WritingTab"
  component={WritingScreen}
  options={{
    title: 'AI写作',
    header: () => <CommonHeader title="AI 写作" />,
  }}
/>
```

## 导航流程示例

### AI写作功能的导航流程

1. **用户在WritingTab** (TabNavigator管理)

   ```
   TabNavigator → WritingTab → WritingScreen
   ```

2. **点击"开始创作"按钮**

   ```javascript
   navigation.navigate('WritingCreation');
   ```

3. **跳转到创作页面** (AppNavigator管理)

   ```
   AppNavigator → WritingCreation → WritingCreationScreen
   ```

4. **返回写作Tab**
   ```javascript
   navigation.goBack(); // 或使用返回按钮
   ```

## 导航配置最佳实践

### 头部导航栏配置

**AppNavigator级别配置**:

```typescript
screenOptions={{
  headerStyle: {
    backgroundColor: colors.background.primary,
    borderBottomColor: colors.border,
  },
  headerTintColor: colors.text.primary,
  headerTitleStyle: {
    fontWeight: '600',
  },
}}
```

**页面级别覆盖**:

```typescript
options={{
  title: 'AI 写作',
  headerStyle: {
    backgroundColor: '#f4f8ff', // 自定义背景色
  },
  headerShadowVisible: false, // 移除阴影
}}
```

### Tab导航栏配置

**统一的Tab样式**:

```typescript
tabBarStyle: {
  backgroundColor: '#FFFFFF',
  borderTopLeftRadius: 16,
  borderTopRightRadius: 16,
  height: 60 + insets.bottom,
  elevation: 8,
  shadowColor: '#646778',
  // ...更多样式配置
}
```

## 架构优势

### 清晰的职责分离

- **AppNavigator**: 处理应用级导航和跨Tab页面
- **TabNavigator**: 专注于主功能模块的Tab切换

### 良好的扩展性

- 新增跨Tab功能页面只需在AppNavigator中添加
- 新增主功能模块只需在TabNavigator中添加Tab

### 统一的用户体验

- 相同类型的页面使用相同的导航模式
- 统一的头部样式和交互行为

### 灵活的导航控制

- 支持复杂的导航场景和深度链接
- 可以灵活控制每个页面的导航栏显示

## 常见问题和解决方案

### Q: 为什么WritingCreationScreen不放在TabNavigator中？

**A**: WritingCreationScreen是跨Tab的子页面，具有以下特点：

- 不属于任何特定Tab
- 需要完整的导航栈支持
- 可能被多个Tab调用
- 需要与其他设置类页面保持一致的导航体验

### Q: 如何决定页面应该放在哪个导航器？

**A**: 判断标准：

1. **是否为主功能入口** → TabNavigator
2. **是否需要Tab栏显示** → TabNavigator
3. **是否为跨Tab功能** → AppNavigator
4. **是否为详情/设置页面** → AppNavigator

### Q: 如何处理复杂的导航场景？

**A**: 利用导航器的层级关系：

- 使用`navigation.navigate()`在同级或上级导航器间跳转
- 使用`navigation.goBack()`返回上一页面
- 使用`navigation.reset()`重置导航栈

## 相关文档

- [React Navigation官方文档](https://reactnavigation.org/)
- [项目导航类型定义](../src/types/navigation.ts)
- [AppNavigator实现](../src/navigation/AppNavigator.tsx)
- [TabNavigator实现](../src/navigation/TabNavigator.tsx)

---

**维护人员**: 开发团队  
**最后更新**: 2025年8月3日
