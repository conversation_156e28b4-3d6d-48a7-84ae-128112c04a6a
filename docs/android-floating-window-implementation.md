# Android系统级浮窗实现文档

## 🎯 功能概述

实现了录音胶囊的系统级浮窗功能，允许用户在切换到其他应用时继续显示录音状态。

## 🏗 架构设计

### 技术方案：混合架构

- **原生层**: Kotlin实现浮窗管理和UI渲染
- **桥接层**: React Native Bridge连接原生功能
- **RN层**: Hook和Service封装，集成到现有录音状态管理

### 组件结构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   React Native  │    │   Bridge Module  │    │  Native Android │
│                 │    │                  │    │                 │
│ useFloatingWindow│◄──►│ FloatingWindow   │◄──►│ WindowManager   │
│ RecordingContext│    │ Module.kt        │    │ FloatingView    │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 📁 文件结构

### Android原生代码

```
android/app/src/main/java/com/gzai168/zipco/floating/
├── FloatingWindowManager.kt    # 浮窗管理器核心类
├── FloatingWindowModule.kt     # RN桥接模块
└── FloatingWindowPackage.kt    # 模块包装类
```

### React Native代码

```
src/
├── services/floating/
│   └── FloatingWindowService.ts  # 浮窗服务封装
├── hooks/
│   └── useFloatingWindow.ts      # 浮窗Hook
└── App.tsx                       # 应用入口集成
```

## 🔧 实现细节

### 1. 权限管理

- **权限声明**: `SYSTEM_ALERT_WINDOW` (已在AndroidManifest.xml中)
- **运行时检查**: `Settings.canDrawOverlays()`
- **权限请求**: 引导用户到系统设置页面授权

### 2. 浮窗创建

```kotlin
// 窗口类型（兼容不同Android版本）
val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
} else {
    WindowManager.LayoutParams.TYPE_PHONE
}

// 窗口参数
val layoutParams = WindowManager.LayoutParams(
    FLOATING_WINDOW_WIDTH,
    FLOATING_WINDOW_HEIGHT,
    type,
    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
    PixelFormat.TRANSLUCENT
)
```

### 3. 拖拽功能

- **拖拽检测**: `OnTouchListener`监听手势
- **位置更新**: `WindowManager.updateViewLayout()`
- **收缩逻辑**: 当拖拽到屏幕右侧边缘时自动收缩
- **边界限制**: 防止浮窗完全移出屏幕

### 4. 状态同步

- **RN到原生**: 通过Bridge传递录音状态数据
- **原生到RN**: 通过EventEmitter发送用户交互事件
- **应用状态监听**: AppState变化时显示/隐藏浮窗

## 🔌 API接口

### JavaScript API

```typescript
// 检查权限
const hasPermission = await floatingWindowService.hasOverlayPermission();

// 请求权限
const granted = await floatingWindowService.requestOverlayPermission();

// 显示浮窗
const success = await floatingWindowService.showFloatingWindow({
  duration: 120,
  isRecording: true,
  isPaused: false,
});

// 隐藏浮窗
await floatingWindowService.hideFloatingWindow();

// 更新浮窗内容
await floatingWindowService.updateFloatingWindow(data);

// 监听事件
floatingWindowService.addEventListener('onFloatingWindowPress', callback);
floatingWindowService.addEventListener('onFloatingWindowStop', callback);
```

### Hook使用

```typescript
function RecordingScreen() {
  const {
    isSupported,
    showFloatingWindow,
    hideFloatingWindow,
    checkAndRequestPermission,
  } = useFloatingWindow();

  // Hook自动处理应用状态变化和浮窗显示
}
```

## 🎨 UI设计

### 浮窗布局

```
┌─────────────────────────────────────┐
│ [●●●●●] 01:23 录音中    [⏹]        │
└─────────────────────────────────────┘
  波形指示器    状态文本    停止按钮
```

### 收缩状态

```
┌──────┐
│ [●●●●●] │ ← 只显示波形部分
└──────┘
```

### 样式规范

- **背景色**: 白色 (#FFFFFF)
- **文字颜色**: 深灰 (#2A2B33)
- **波形颜色**: 蓝色 (#417FFB)
- **圆角**: 左侧24px圆角，右侧直角
- **阴影**: 左侧阴影效果

## 🧪 测试指南

### 1. 权限测试

```bash
# 检查应用是否有浮窗权限
adb shell settings get secure enabled_accessibility_services

# 手动授权浮窗权限
设置 > 应用 > 特殊权限 > 显示在其他应用的上层
```

### 2. 功能测试

1. **启动录音**: 验证胶囊显示
2. **切换到后台**: 验证浮窗自动显示
3. **拖拽测试**: 验证拖拽和收缩功能
4. **点击测试**: 验证浮窗点击回调到应用
5. **回到前台**: 验证浮窗自动隐藏

### 3. 兼容性测试

- **Android版本**: 6.0+ (API 23+)
- **厂商ROM**: 测试小米、华为、OPPO等
- **权限策略**: 不同厂商的权限管理差异

## ⚠️ 注意事项

### 1. 权限限制

- Android 6.0+需要用户手动授权
- 某些厂商ROM有额外限制
- 需要引导用户正确授权

### 2. 性能考虑

- 后台运行时优化资源占用
- 合理的状态更新频率
- 及时释放WindowManager资源

### 3. 用户体验

- 清晰的权限说明和引导
- 优雅的权限被拒绝处理
- 与应用内胶囊的无缝切换

### 4. 系统兼容性

- 不同Android版本的API差异
- 厂商定制ROM的限制
- 电池优化白名单考虑

## 🚀 部署指南

### ⚠️ 重要说明：Expo项目的原生代码管理

由于这是Expo项目，`android/`目录在每次`expo prebuild`时都会被重新生成，因此原生代码需要特殊管理：

### 1. 原生代码备份与恢复

**在开发过程中：**

```bash
# 1. 备份当前自定义原生代码（包含浮窗模块）
./scripts/backup-native-code.sh backup

# 2. 检查备份状态
./scripts/backup-native-code.sh check

# 3. 如果需要运行prebuild（会清除自定义代码）
expo prebuild

# 4. 立即恢复自定义代码
./scripts/backup-native-code.sh restore
```

**备份文件位置：**

```
native-code-backup/
├── audio_module_YYYYMMDD_HHMMSS/          # 音频模块
├── floating_module_YYYYMMDD_HHMMSS/       # 浮窗模块
├── MainApplication_YYYYMMDD_HHMMSS.kt     # 修改后的主应用类
└── AndroidManifest_YYYYMMDD_HHMMSS.xml    # 权限配置
```

### 2. 编译构建

```bash
# 1. 确保原生代码已恢复
./scripts/backup-native-code.sh check

# 2. 清理构建缓存
cd android && ./gradlew clean

# 3. 构建Debug版本
./gradlew assembleDebug

# 4. 安装到设备
adb install app/build/outputs/apk/debug/app-debug.apk
```

### 3. 权限配置

浮窗功能需要以下权限（已在AndroidManifest.xml中配置）：

```xml
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
<uses-permission android:name="android.permission.RECORD_AUDIO"/>
```

### 4. 团队协作工作流程

**代码修改后：**

```bash
# 修改原生代码后，立即备份
./scripts/backup-native-code.sh backup
git add native-code-backup/
git commit -m "backup: 更新浮窗模块代码"
```

**拉取代码后：**

```bash
# 拉取最新代码
git pull

# 恢复原生代码
./scripts/backup-native-code.sh restore

# 检查恢复状态
./scripts/backup-native-code.sh check
```

### 5. 混淆配置

如果使用ProGuard，添加以下规则：

```proguard
-keep class com.gzai168.zipco.floating.** { *; }
-keep class com.gzai168.zipco.audio.** { *; }
-keep class * extends com.facebook.react.bridge.ReactContextBaseJavaModule { *; }
```

## 🐛 常见问题

### Q: 浮窗不显示？

A: 检查权限授权、Android版本兼容性、WindowManager异常

### Q: 拖拽不响应？

A: 检查触摸事件监听、布局参数设置、手势冲突

### Q: 应用切换时浮窗闪烁？

A: 优化AppState监听逻辑、添加防抖处理

### Q: 某些设备上权限请求失败？

A: 适配厂商ROM的权限管理差异、提供手动引导

## 📈 后续优化

1. **UI优化**: 使用自定义图标、动画效果
2. **功能扩展**: 支持更多交互操作、快捷功能
3. **性能优化**: 减少内存占用、优化渲染性能
4. **兼容性**: 适配更多厂商ROM和Android版本
