# 新旧 UI 设计对比与实施方案

_生成时间：2025-07-21 16:03 (UTC+8)_

## 1. 概述

本文档对比了 **现行 Hybrid App UI** 与 **最新设计稿（design/ 目录）** 的视觉及交互差异，提炼冲突点、可借鉴要素，并给出分阶段实施方案，供设计、研发、产品决策参考。

---

## 2. 视觉风格对比

| 类目         | 现行 UI                    | 新设计稿                   | 差异/冲突                      |
| ------------ | -------------------------- | -------------------------- | ------------------------------ |
| **主色**     | 纯色蓝 `#007AFF`           | 蓝绿渐变 `#46A8FF→#78F6D3` | 需扩展渐变主题；纯色按钮显单调 |
| **辅助色**   | 紫 `#5856D6`、橙 `#FF9500` | 纯蓝 / 纯绿                | 需统一 Color Token             |
| **灰度**     | 明确分级 `gray.50~900`     | 灰更浅，带淡蓝高光阴影     | 分割线/背景对比度需更新        |
| **圆角**     | 8~12px                     | 16~20px                    | 组件需整体加大圆角             |
| **阴影**     | 几乎无阴影                 | 卡片柔和阴影，层次分明     | RN iOS/Android 阴影表现需适配  |
| **图标**     | Feather / Ionicons 线性    | 填充/双色面性 SVG          | 图标库需替换或补充             |
| **字体排版** | 17sp 标题 / 14sp 正文      | 相似但行高更高(1.35)       | 行高、字重需调整               |
| **布局留白** | Material / 分割线          | iOS 卡片化＋大留白         | 需更新栅格与边距 token         |

---

## 3. 组件与交互差异

| 模块          | 现行实现                | 新设计                 | 调整方向                |
| ------------- | ----------------------- | ---------------------- | ----------------------- |
| **按钮**      | 扁平纯色                | 主按钮渐变+投影        | 封装 `GradientButton`   |
| **列表 Item** | 分割线列表              | iOS 风 Cell + 右滑动作 | 封装 `SwipeListItem`    |
| **状态指示**  | Tag + Icon              | 彩色胶囊/进度          | 封装 `StatusPill`       |
| **弹窗**      | 原生 Alert / 全屏 Modal | 半高底部行动表         | 引入 `BottomSheetModal` |
| **录音首页**  | 纯列表                  | 顶部操作栏+卡片        | 重构录音模块 UI         |
| **AI 速览**   | 页面铺底                | 卡片叠加+渐变底        | 重构结果页排版          |

---

## 4. 风险与注意事项

1. **无障碍对比度**：渐变按钮需保证 WCAG 4.5:1，对浅端文字加深或描边。
2. **性能问题**：大量渐变/阴影在低端机可能掉帧，需懒渲染。
3. **图标资产**：面性 SVG 替换成本高，需设计团队配合。
4. **圆角阴影**：Android `overflow: hidden` 会裁剪阴影，需 `elevation`＋蒙版方案。

---

## 5. 分阶段实施方案

| 阶段        | 时间 | 目标                  | 关键交付物                                                                          |
| ----------- | ---- | --------------------- | ----------------------------------------------------------------------------------- |
| **Phase 0** | 1 d  | 设计 Token & 组件规范 | `colors.ts`, `radius.ts`, 渐变/阴影规范                                             |
| **Phase 1** | 3 d  | 核心组件封装          | `GradientButton`, `GradientCard`, `StatusPill`, `SwipeListItem`, `BottomSheetModal` |
| **Phase 2** | 2 d  | 录音模块视觉升级      | 录音首页/详情/录音中 页面重构                                                       |
| **Phase 3** | 3 d  | AI 相关页面升级       | AI 纪要/速览/问问系列重构                                                           |
| **Phase 4** | 2 d  | 全局 QA & 无障碍      | 深浅模式、字体放大、对比度 & 性能测试                                               |
| **Phase 5** | 1 d  | 发布 & 回溯           | 用户反馈收集、性能监控                                                              |

> **总工期建议：** ~12 工作日，按模块灰度发布。

---

## 6. 结论

新设计在渐变、圆角及交互细节上大幅提升科技感和易用性。建议先以录音模块试点，验证技术方案与用户反馈后逐步全局推广，既保证了视觉统一，也降低了一次性改动风险。
