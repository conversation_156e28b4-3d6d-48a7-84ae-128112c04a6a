{"generated_at": "2025-07-21T16:11:09+08:00", "phases": [{"id": "phase0", "name": "Design Tokens & Component Guidelines", "duration_days": 1, "tasks": [{"id": "0.1", "title": "扩展颜色与渐变 Token", "description": "在 src/styles/colors.ts 中新增 gradient.primary 等；新增 src/styles/radius.ts 统一圆角。", "deliverables": ["src/styles/colors.ts", "src/styles/radius.ts"], "depends_on": []}, {"id": "0.2", "title": "定义阴影与留白规范", "description": "更新 docs/design.md，补充阴影、间距、渐变使用规范。", "deliverables": ["docs/design.md"], "depends_on": ["0.1"]}]}, {"id": "phase1", "name": "核心组件封装", "duration_days": 3, "tasks": [{"id": "1.1", "title": "创建 GradientButton 组件", "description": "支持线性渐变、尺寸、禁用态、多语言。", "deliverables": ["src/components/common/GradientButton/index.tsx"], "depends_on": ["0.1"]}, {"id": "1.2", "title": "创建 GradientCard 组件", "description": "通用卡片容器，支持渐变背景、阴影、可选 header/footer。", "deliverables": ["src/components/common/GradientCard/index.tsx"], "depends_on": ["0.1"]}, {"id": "1.3", "title": "创建 StatusPill 组件", "description": "彩色胶囊，支持类型 success/warning/info/error progress。", "deliverables": ["src/components/common/StatusPill/index.tsx"], "depends_on": []}, {"id": "1.4", "title": "创建 SwipeListItem 组件", "description": "基于 react-native-gesture-handler Swipeable，实现左/右滑动作区。", "deliverables": ["src/components/common/SwipeListItem/index.tsx"], "depends_on": []}, {"id": "1.5", "title": "集成 BottomSheetModal 方案", "description": "选型 react-native-modalize 或自研半高弹窗；封装通用 modal 组件。", "deliverables": ["src/components/common/BottomSheetModal/index.tsx"], "depends_on": []}]}, {"id": "phase2", "name": "录音模块视觉升级", "duration_days": 2, "tasks": [{"id": "2.1", "title": "重构录音首页", "description": "替换顶部渐变操作栏、列表项为 SwipeListItem、状态胶囊。", "deliverables": ["src/screens/recording/RecordingScreen.tsx"], "depends_on": ["phase1"]}, {"id": "2.2", "title": "重构录音详情/录音中页面", "description": "应用 GradientCard、GradientButton、BottomSheetModal。", "deliverables": ["src/screens/recording/RecordingDetailScreen.tsx", "src/screens/recording/RecordingInProgressScreen.tsx"], "depends_on": ["phase1"]}]}, {"id": "phase3", "name": "AI 相关页面升级", "duration_days": 3, "tasks": [{"id": "3.1", "title": "重构 AI 速览/纪要 页面", "description": "使用 GradientCard 显示摘要、逐条要点；顶部标签栏支持渐变选中态。", "deliverables": ["src/screens/ai/AISummaryScreen.tsx", "src/screens/ai/AIOverviewScreen.tsx"], "depends_on": ["phase1"]}, {"id": "3.2", "title": "重构 AI 问问 页面", "description": "应用新按钮、底部弹窗输入栏。", "deliverables": ["src/screens/ai/AIChatScreen.tsx"], "depends_on": ["phase1"]}]}, {"id": "phase4", "name": "全局 QA & 无障碍", "duration_days": 2, "tasks": [{"id": "4.1", "title": "深浅模式与动态字体测试", "description": "检查深色模式、字体放大 120% 布局。", "deliverables": ["QA 报告"], "depends_on": ["phase2", "phase3"]}, {"id": "4.2", "title": "对比度与性能 profiling", "description": "使用 Lighthouse/Flipper 检测 WCAG 对比度和 FPS。", "deliverables": ["无障碍报告", "性能报告"], "depends_on": ["4.1"]}]}, {"id": "phase5", "name": "发布 & 回溯", "duration_days": 1, "tasks": [{"id": "5.1", "title": "灰度发布新 UI", "description": "通过 EAS Channel 或内部测试轨道推送新版本。", "deliverables": ["新 UI 版本 (build)"], "depends_on": ["phase4"]}, {"id": "5.2", "title": "收集用户反馈并回溯优化", "description": "分析监控数据、FEEDBACK 表，列出后续优化任务。", "deliverables": ["回溯报告", "后续优化 Backlog"], "depends_on": ["5.1"]}]}]}