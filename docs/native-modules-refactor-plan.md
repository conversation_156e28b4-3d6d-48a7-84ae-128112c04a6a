# 原生模块重构方案（Expo Module + Config Plugin）

> 项目：**office-assistant-hybrid**  
> 作者：Cline（自动生成）  
> 日期：2025-07-28

---

## 1. 背景

- 当前仓库采用 **native-code-backup + restore 脚本** 方式维护自定义原生代码。
- 编译或 `git clean` 后需手动执行 `yarn restore`，易出错、CI 不友好。
- Expo 官方推荐使用 **Expo Module + Config Plugin** 来注入原生能力，并用 **自定义 Dev Client** 进行调试。
- 本文档提出详细迁移 / 重构方案，并拆分为可执行的细颗粒度 TodoList。

---

## 2. 总体目标

1. **彻底移除** `native-code-backup/` 及相关脚本。
2. 将 **audio / floating window / location** 三个 Kotlin 模块封装为独立的 **Expo Module**。
3. 在每个模块中实现 **Config Plugin**，实现 Manifest 权限与 Meta-data 注入。
4. 通过 `expo prebuild` + **自动链接** 注入原生代码，保持 Managed Workflow。
5. 采用 **Dev Client** 开发，`EAS Build + OTA (EAS Update)` 生产。
6. 保留 OTA 能力，保证 JS/TS 业务逻辑无感迁移。

---

## 3. 目录结构调整

```text
modules/
  expo-realtime-audio/
    android/
    ios/ (可选)
    plugin/
      withRealtimeAudio.ts
  expo-floating-window/
  expo-native-location/

docs/
  native-modules-refactor-plan.md   ← 当前文档
```

---

## 4. 分阶段实施方案

### A. 前期准备

| 步骤     | 说明                                                                                                         |
| -------- | ------------------------------------------------------------------------------------------------------------ |
| 升级依赖 | `expo@~52.0.0` `expo-modules-core@~2.5.0` `@expo/config-plugins@~10.1.2` `@expo/cli@latest` `eas-cli@latest` |
| Git 规范 | `.gitignore` 排除 `android/ ios/`；保留 `modules/**/android/`                                                |
| 新建分支 | `git checkout -b feat/native-modules-refactor`                                                               |

### B. 模块化拆分

1. **脚手架创建**

   ```bash
   # 在项目根目录创建 modules 目录
   mkdir -p modules
   cd modules

   # 创建本地模块（推荐方式）
   npx create-expo-module@latest --local expo-realtime-audio
   npx create-expo-module@latest --local expo-floating-window
   npx create-expo-module@latest --local expo-native-location

   cd ..
   ```

2. **迁移 Kotlin 源码**
   - 从 `native-code-backup/*_module/**` 复制至对应模块 `android/src/main/java/...`
   - 统一包名：`expo.modules.officeassistant.<module>`（推荐项目特定包名）
3. **实现 Module 类**

   ```kotlin
   // modules/expo-realtime-audio/android/src/main/java/expo/modules/officeassistant/realtimeaudio/RealtimeAudioModule.kt
   package expo.modules.officeassistant.realtimeaudio

   import expo.modules.kotlin.modules.Module
   import expo.modules.kotlin.modules.ModuleDefinition

   class RealtimeAudioModule : Module() {
     override fun definition() = ModuleDefinition {
       Name("RealtimeAudio")

       // 定义同步函数
       Function("initialize") { /* 初始化录音 */ }
       Function("startRecording") { /* 开始录音 */ }
       Function("stopRecording") { /* 停止录音 */ }

       // 定义异步函数
       AsyncFunction("processAudioData") { data: ByteArray ->
         // 处理音频数据
         return@AsyncFunction "success"
       }

       // 定义事件
       Events("onAudioData", "onRecordingStatusChanged")
     }
   }
   ```

4. **Gradle 配置**

   ```gradle
   // modules/expo-realtime-audio/android/build.gradle
   dependencies {
     implementation 'expo.modules:expo-modules-core:2.5.0'
     // 其他依赖...
   }

   android {
     compileSdkVersion 34
     defaultConfig {
       minSdkVersion 23
       targetSdkVersion 34
     }
   }
   ```

### C. Config Plugin 开发

| 模块     | 插件文件                      | 注入内容                                               |
| -------- | ----------------------------- | ------------------------------------------------------ |
| audio    | `plugin/withRealtimeAudio.ts` | `RECORD_AUDIO` `MODIFY_AUDIO_SETTINGS` 权限；Meta-data |
| floating | `withFloatingWindow.ts`       | `SYSTEM_ALERT_WINDOW` 权限；overlay 配置               |
| location | `withNativeLocation.ts`       | `ACCESS_FINE_LOCATION` 等权限；queries 节点            |

**详细示例**：

```typescript
// modules/expo-realtime-audio/plugin/src/withRealtimeAudio.ts
import {
  withAndroidManifest,
  AndroidConfig,
  ConfigPlugin,
  withPlugins,
} from '@expo/config-plugins';

export const withRealtimeAudio: ConfigPlugin = (config) => {
  return withPlugins(config, [
    // 添加权限
    (config) =>
      AndroidConfig.Permissions.withPermissions(config, [
        'android.permission.RECORD_AUDIO',
        'android.permission.MODIFY_AUDIO_SETTINGS',
        'android.permission.WRITE_EXTERNAL_STORAGE',
      ]),

    // 自定义 Manifest 修改
    (config) =>
      withAndroidManifest(config, (config) => {
        const androidManifest = config.modResults;
        const mainApplication = androidManifest.manifest.application?.[0];

        if (mainApplication) {
          // 添加 meta-data（如需要）
          if (!mainApplication['meta-data']) {
            mainApplication['meta-data'] = [];
          }

          mainApplication['meta-data'].push({
            $: {
              'android:name':
                'expo.modules.officeassistant.realtimeaudio.enabled',
              'android:value': 'true',
            },
          });
        }

        return config;
      }),
  ]);
};

export default withRealtimeAudio;
```

```typescript
// modules/expo-floating-window/plugin/src/withFloatingWindow.ts
import {
  withAndroidManifest,
  AndroidConfig,
  ConfigPlugin,
  withPlugins,
} from '@expo/config-plugins';

export const withFloatingWindow: ConfigPlugin = (config) => {
  return withPlugins(config, [
    // 添加权限
    (config) =>
      AndroidConfig.Permissions.withPermissions(config, [
        'android.permission.SYSTEM_ALERT_WINDOW',
        'android.permission.FOREGROUND_SERVICE',
      ]),

    // Manifest 配置
    (config) =>
      withAndroidManifest(config, (config) => {
        const androidManifest = config.modResults;
        const mainApplication = androidManifest.manifest.application?.[0];

        if (mainApplication) {
          // 添加 Service 声明
          if (!mainApplication.service) {
            mainApplication.service = [];
          }

          mainApplication.service.push({
            $: {
              'android:name':
                'expo.modules.officeassistant.floatingwindow.FloatingWindowService',
              'android:enabled': 'true',
              'android:exported': 'false',
            },
          });
        }

        return config;
      }),
  ]);
};

export default withFloatingWindow;
```

```typescript
// modules/expo-native-location/plugin/src/withNativeLocation.ts
import {
  withAndroidManifest,
  AndroidConfig,
  ConfigPlugin,
  withPlugins,
} from '@expo/config-plugins';

export const withNativeLocation: ConfigPlugin = (config) => {
  return withPlugins(config, [
    // 添加权限
    (config) =>
      AndroidConfig.Permissions.withPermissions(config, [
        'android.permission.ACCESS_FINE_LOCATION',
        'android.permission.ACCESS_COARSE_LOCATION',
        'android.permission.ACCESS_BACKGROUND_LOCATION',
      ]),

    // Queries 节点（Android 11+ 包可见性）
    (config) =>
      withAndroidManifest(config, (config) => {
        const androidManifest = config.modResults;

        if (!androidManifest.manifest.queries) {
          androidManifest.manifest.queries = [];
        }

        androidManifest.manifest.queries.push({
          intent: [
            {
              action: [
                {
                  $: { 'android:name': 'android.intent.action.VIEW' },
                },
              ],
              data: [
                {
                  $: { 'android:scheme': 'geo' },
                },
              ],
            },
          ],
        });

        return config;
      }),
  ]);
};

export default withNativeLocation;
```

### D. 主工程集成

1. **package.json**

```jsonc
"dependencies": {
  "expo-realtime-audio": "file:modules/expo-realtime-audio",
  "expo-floating-window": "file:modules/expo-floating-window",
  "expo-native-location": "file:modules/expo-native-location"
}
```

2. **app.json**

```jsonc
"expo": {
  "plugins": [
    "./modules/expo-realtime-audio/plugin/src/withRealtimeAudio",
    "./modules/expo-floating-window/plugin/src/withFloatingWindow",
    "./modules/expo-native-location/plugin/src/withNativeLocation"
  ]
}
```

> **注意**: 需要确保每个插件目录下有正确的 `package.json` 文件和入口点

3. **删除旧方案**
   - `rm -rf native-code-backup`
   - 删除 `scripts/backup-native-code.sh` 及相关 npm script（backup/restore/check/eas-build-pre）
   - 更新/删除旧文档

### E. Development Build 与构建验证

Development Build 是一个**包含自定义原生模块的专属 Expo Go 客户端**，让你能够在真实设备上测试完整功能。

#### 🎯 Development Build 概念

**本质**：基于官方 Expo Go 源码构建的定制版客户端，预编译了你的自定义原生模块。

| 特性     | 标准 Expo Go     | Development Build      |
| -------- | ---------------- | ---------------------- |
| 原生模块 | 仅 Expo SDK 内置 | **包含你的自定义模块** |
| 适用项目 | 所有 Expo 项目   | **仅你的项目**         |
| 分发方式 | App Store 下载   | **你自己构建安装**     |
| 更新方式 | Expo 官方维护    | **你控制版本**         |

#### 🔄 完整工作流程

```bash
# 1. 清理并预构建原生代码
expo prebuild --platform android --clean

# 2. 构建包含自定义模块的 Development Build
eas build --platform android --profile development

# 3. 安装到设备（构建完成后）
eas build:run --platform android --latest

# 4. 日常开发使用
expo start --dev-client  # 启动开发服务器，连接 Development Build

# 5. 构建生产版本
eas build --platform android --profile production
```

#### ⚡ Development Build 优势

1. **完整功能测试**：录音、悬浮窗、定位等原生功能在真机上正常工作
2. **快速迭代开发**：JavaScript 代码支持热重载和 OTA 更新
3. **真实环境测试**：在真机上测试，避免模拟器限制
4. **团队环境一致**：团队成员安装同一个 APK，保证开发环境统一
5. **调试工具支持**：完整支持 Flipper、React DevTools 等调试工具

#### 📱 使用体验

- **外观操作**：与 Expo Go 几乎相同的界面和操作方式
- **启动方式**：启动后显示 QR 码扫描界面，扫码连接开发服务器
- **功能完整**：加载应用后，所有自定义原生功能（录音、悬浮窗、定位）正常可用
- **开发体验**：支持热重载，代码修改即时生效

#### 🔄 版本管理策略

```bash
# 原生模块代码更新时 - 需要重新构建 Development Build
eas build --platform android --profile development

# 仅 JavaScript/TypeScript 代码更新时 - 无需重新构建
expo start --dev-client  # 热重载自动生效
# 或使用 OTA 更新
eas update --branch development
```

### F. 文档与交付

- 更新 `README.md` 与 `docs/android-native-code-management.md`
- 添加迁移说明、使用说明、调试指南
- 更新 `CHANGELOG.md`，版本升至 **v2.0.0**

---

## 5. 细颗粒度 TodoList

### 0️⃣ 计划

- [ ] 创建任务看板，拆分下列子任务

### 1️⃣ 环境升级

- [ ] 升级到指定版本：`expo@~52.0.0` `expo-modules-core@~2.5.0` `@expo/config-plugins@~10.1.2`
- [ ] 安装/升级 CLI：`@expo/cli@latest` `eas-cli@latest`
- [ ] `yarn install`
- [ ] `npx expo-doctor` 检查环境配置
- [ ] 验证 Node.js >= 18 和 Java 17+

### 2️⃣ 模块脚手架

- [ ] 使用 `npx create-expo-module@latest --local expo-realtime-audio` 创建音频模块
- [ ] 使用 `npx create-expo-module@latest --local expo-floating-window` 创建悬浮窗模块
- [ ] 使用 `npx create-expo-module@latest --local expo-native-location` 创建定位模块
- [ ] 验证每个模块的基础结构和自动生成的文件

### 3️⃣ 迁移 Kotlin 代码

- [ ] 复制 audio 源码到 `modules/expo-realtime-audio/android/src/main/java/`，调整包名为 `expo.modules.officeassistant.realtimeaudio`
- [ ] 复制 floating 源码到 `modules/expo-floating-window/android/src/main/java/`，调整包名为 `expo.modules.officeassistant.floatingwindow`
- [ ] 复制 location 源码到 `modules/expo-native-location/android/src/main/java/`，调整包名为 `expo.modules.officeassistant.nativelocation`
- [ ] 更新所有 import 语句和类引用

### 4️⃣ Module 类实现

- [ ] `RealtimeAudioModule.kt` 继承 `Module` 并实现 `definition()` 方法
- [ ] `FloatingWindowModule.kt` 继承 `Module` 并实现 `definition()` 方法
- [ ] `NativeLocationModule.kt` 继承 `Module` 并实现 `definition()` 方法
- [ ] 验证模块注册和自动链接

### 5️⃣ Gradle 配置

- [ ] 每模块 `build.gradle` 引入 `expo-modules-core:2.5.0`
- [ ] 校验 `compileSdkVersion 34` / `minSdkVersion 23` / `targetSdkVersion 34`
- [ ] 确保与主工程 Gradle 版本一致

### 6️⃣ Config Plugin

- [ ] 编写 `withRealtimeAudio.ts` 并配置正确的权限和 meta-data
- [ ] 编写 `withFloatingWindow.ts` 并配置 Service 声明
- [ ] 编写 `withNativeLocation.ts` 并配置 queries 节点
- [ ] 为每个插件创建正确的 `package.json` 入口点
- [ ] 验证插件在 `expo prebuild` 时正确工作

### 7️⃣ 主工程调整

- [ ] `package.json` 添加本地依赖
- [ ] `app.json` 添加插件路径
- [ ] `yarn install`

### 8️⃣ 清理旧实现

- [ ] 删除 `native-code-backup/`
- [ ] 删除 `scripts/backup-native-code.sh`
- [ ] 移除相关 npm scripts
- [ ] 更新 docs

### 9️⃣ 构建与验证

- [ ] `expo prebuild --platform android --clean` 本地通过
- [ ] 验证 `android/app/src/main/AndroidManifest.xml` 权限注入正确
- [ ] `eas build --platform android --profile development` 构建 Dev Client
- [ ] 在 Dev Client 中功能测试：录音、悬浮窗、定位
- [ ] `eas build --platform android --profile preview` 预览版构建成功
- [ ] `eas build --platform android --profile production` 生产版构建成功

### 🔟 文档 & CI

- [ ] 更新 CI workflow cache
- [ ] 更新 README 安装 & 调试指南
- [ ] 更新 / 新增文档
- [ ] 写 `CHANGELOG.md v2.0.0`

### 1️⃣1️⃣ PR & 发布

- [ ] Push 分支并打开 PR
- [ ] Review & Merge
- [ ] Tag `v2.0.0`
- [ ] `eas update --branch prod` 发布 OTA

---

## 6. 里程碑与时间预估

| 任务块            | 负责人   | 预估工时    |
| ----------------- | -------- | ----------- |
| 环境升级 & 分支   | @dev     | 0.5 d       |
| 模块脚手架 & 迁移 | @android | 1.0 d       |
| Module & Gradle   | @android | 0.5 d       |
| Config Plugin     | @ts      | 1.0 d       |
| 主工程集成        | @dev     | 0.5 d       |
| 构建/测试         | @qa      | 0.5 d       |
| 文档 & PR         | @dev     | 0.5 d       |
| **合计**          | -        | **~4 人日** |

---

## 7. 风险与回滚

| 风险                     | 缓解措施                                                  |
| ------------------------ | --------------------------------------------------------- |
| 插件权限遗漏导致功能失效 | 单元测试 + 真机手动验证                                   |
| Dev Client 编译失败      | 提前本地 `expo prebuild` 检查                             |
| Monorepo 路径问题        | 使用绝对路径 `file:modules/...` 或 yarn workspace         |
| OTA 回滚                 | 保留旧分支 `release/1.x`，必要时 `eas update --roll-back` |

---

## 8. 重要技术细节补充

### 📁 完整目录结构

```text
modules/
  expo-realtime-audio/
    android/
      src/main/java/expo/modules/officeassistant/realtimeaudio/
        RealtimeAudioModule.kt
        AudioStreamManager.kt
        # 其他相关类...
    plugin/
      src/
        withRealtimeAudio.ts
      package.json              # 插件入口点
    package.json               # 模块主入口点
    expo-module.config.json    # 自动生成的配置
  expo-floating-window/
    android/src/main/java/expo/modules/officeassistant/floatingwindow/
    plugin/src/withFloatingWindow.ts
    # 类似结构...
  expo-native-location/
    android/src/main/java/expo/modules/officeassistant/nativelocation/
    plugin/src/withNativeLocation.ts
    # 类似结构...
```

### 🔧 关键配置文件模板

**模块插件 package.json**:

```json
// modules/expo-realtime-audio/plugin/package.json
{
  "name": "expo-realtime-audio-plugin",
  "version": "1.0.0",
  "main": "src/withRealtimeAudio.js",
  "types": "src/withRealtimeAudio.d.ts"
}
```

**模块 expo-module.config.json**:

```json
{
  "platforms": ["android", "ios"],
  "android": {
    "modules": [
      "expo.modules.officeassistant.realtimeaudio.RealtimeAudioModule"
    ]
  }
}
```

### ⚠️ 常见陷阱和注意事项

1. **包名一致性**: 确保 Kotlin 包名、Config Plugin 中的服务名称、以及 expo-module.config.json 中的模块声明完全一致

2. **Gradle 版本匹配**: 模块的 Gradle 配置必须与主工程保持一致，特别是 `compileSdkVersion` 和 `targetSdkVersion`

3. **权限声明完整性**: Config Plugin 中的权限必须覆盖模块的所有需求，遗漏权限会导致运行时崩溃

4. **插件入口点正确性**: `app.json` 中的插件路径必须指向正确的 TypeScript/JavaScript 文件

5. **自动链接验证**: 使用 `expo prebuild` 后检查生成的 `android/settings.gradle` 确保模块被正确包含

### 🚀 开发工作流对比

#### 重构前（当前方案）

```bash
yarn restore          # 手动恢复原生代码
yarn android          # 启动开发服务器
# 在物理设备或模拟器上测试
```

#### 重构后（新方案）

**方式1：本地开发（推荐日常开发）**

```bash
expo run:android       # 自动 prebuild + 启动，包含所有原生模块
# 或
yarn android          # 依然可用，体验完全相同
```

**方式2：Development Build（推荐团队协作）**

```bash
eas build --profile development  # 构建包含原生模块的开发版本
expo start --dev-client          # 启动开发服务器，连接到 Development Build
```

### 📱 测试方式支持对照

| 测试方式           | 重构前        | 重构后        | 支持自定义原生模块 |
| ------------------ | ------------- | ------------- | ------------------ |
| `yarn android`     | ✅ 支持       | ✅ 支持       | ✅ 完全支持        |
| `expo run:android` | ❌ 需手动配置 | ✅ 自动化     | ✅ 完全支持        |
| 标准 Expo Go       | ❌ 不支持     | ❌ 不支持     | ❌ 无法支持        |
| Development Build  | ❌ 需复杂配置 | ✅ 自动化构建 | ✅ 完全支持        |
| 本地模拟器/真机    | ✅ 支持       | ✅ 支持       | ✅ 完全支持        |

> **重要提醒**: 标准 Expo Go 无法加载任何自定义原生模块，必须使用 Development Build 或本地构建方式进行测试。

## 9. 参考链接

- **Expo Modules API**: <https://docs.expo.dev/modules/overview/>
- **Config Plugins 指南**: <https://docs.expo.dev/guides/config-plugins/>
- **创建 Expo Module**: <https://docs.expo.dev/modules/get-started/>
- **Dev Client 使用**: <https://docs.expo.dev/clients/installation/>
- **EAS Build 配置**: <https://docs.expo.dev/build/introduction/>
- **Module API 参考**: <https://docs.expo.dev/modules/module-api/>

---

**⚡ 完成以上步骤后，仓库将完全基于 Expo 官方最佳实践，显著降低维护成本并提升可扩展性。**

**📋 执行建议**: 严格按照 TodoList 顺序执行，每个步骤完成后进行验证，确保不出现基础配置错误。
