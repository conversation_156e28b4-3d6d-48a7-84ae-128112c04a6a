# 📦 NPM Scripts 快捷命令指南

> 为 React Native 开发提供的便捷命令集合

## 🚀 **启动命令**

### 基础启动

```bash
npm start                    # 启动 Metro 开发服务器
npm run android             # 构建并运行 Android 应用
npm run ios                 # 构建并运行 iOS 应用
npm run web                 # 启动 Web 版本
```

### 网络模式启动 🌐

```bash
npm run start:tunnel        # 隧道模式（可通过互联网访问）
npm run start:lan           # 局域网模式（同一WiFi设备可访问）
```

**使用场景**：

- **隧道模式**：远程调试、跨网络开发、演示给客户
- **局域网模式**：团队协作、多设备测试、本地网络调试

## 📱 **调试工具命令**

### 屏幕截图和录制

```bash
npm run screenshot          # 📸 快速截取手机屏幕
npm run record             # 🎥 录制手机屏幕操作
npm run mirror             # 🖥️ 启动屏幕镜像（需要 scrcpy）
```

**实际效果**：

- 截图自动保存到 `./debug-screenshots/` 目录
- 在 macOS 上自动打开截图文件
- 录制结束后自动拉取到电脑

### 代码保护命令 🛡️

```bash
npm run backup             # 备份自定义原生代码
npm run restore            # 恢复自定义原生代码
npm run check              # 检查原生代码完整性
```

**关键用途**：

- **expo prebuild 前**：必须先 `npm run backup`
- **prebuild 后**：立即 `npm run restore`
- **日常检查**：`npm run check` 验证代码状态

## 🔧 **代码质量命令**

```bash
npm run lint               # 检查代码规范
npm run lint:fix           # 自动修复代码规范问题
npm run format             # 格式化代码
npm run type-check         # TypeScript 类型检查
```

## 📖 **常用开发工作流**

### 1️⃣ **日常开发**

```bash
# 启动开发服务器
npm start

# 在另一个终端构建应用
npm run android

# 修改代码 → 自动热更新 ✅
# 需要截图时
npm run screenshot
```

### 2️⃣ **原生代码修改**

```bash
# 1. 先备份
npm run backup

# 2. 修改原生代码
# 编辑 android/app/src/main/java/...

# 3. 重新构建
npm run android

# 4. 验证功能
npm run check
```

### 3️⃣ **expo prebuild 安全操作**

```bash
# ⚠️ 危险操作流程
npm run backup              # 1. 备份原生代码
expo prebuild               # 2. 执行危险操作
npm run restore             # 3. 立即恢复代码
npm run android             # 4. 重新构建
npm run check              # 5. 验证完整性
```

### 4️⃣ **团队协作调试**

```bash
# 启动局域网模式
npm run start:lan

# 其他设备扫码连接
# 截图分享给团队
npm run screenshot

# 录制操作演示
npm run record
```

### 5️⃣ **远程演示**

```bash
# 启动隧道模式
npm run start:tunnel

# 客户端通过二维码连接
# 录制演示视频
npm run record
```

## ⚡ **命令速查表**

| 命令                   | 功能              | 使用频率   |
| ---------------------- | ----------------- | ---------- |
| `npm start`            | 启动开发服务器    | ⭐⭐⭐⭐⭐ |
| `npm run android`      | 构建 Android 应用 | ⭐⭐⭐⭐⭐ |
| `npm run screenshot`   | 快速截图          | ⭐⭐⭐⭐   |
| `npm run check`        | 检查代码状态      | ⭐⭐⭐⭐   |
| `npm run backup`       | 备份原生代码      | ⭐⭐⭐     |
| `npm run start:lan`    | 局域网模式        | ⭐⭐⭐     |
| `npm run mirror`       | 屏幕镜像          | ⭐⭐⭐     |
| `npm run record`       | 录制屏幕          | ⭐⭐       |
| `npm run start:tunnel` | 隧道模式          | ⭐⭐       |
| `npm run restore`      | 恢复原生代码      | ⭐         |

## 🎯 **最佳实践**

### ✅ **推荐做法**

1. **开发前检查**：

   ```bash
   npm run check    # 确保原生代码完整
   npm start        # 启动开发服务器
   ```

2. **调试UI时**：

   ```bash
   npm run screenshot    # 截图对比设计稿
   npm run mirror       # 实时查看操作
   ```

3. **修改原生代码前**：

   ```bash
   npm run backup       # 预防数据丢失
   ```

4. **团队协作时**：
   ```bash
   npm run start:lan    # 方便多设备测试
   ```

### ❌ **避免做法**

1. ❌ 直接运行 `expo prebuild` 而不备份
2. ❌ 修改原生代码后忘记重新构建
3. ❌ 在网络不稳定时使用隧道模式
4. ❌ 长时间录制导致文件过大

## 🔍 **故障排除**

### 命令执行失败

```bash
# 权限问题
chmod +x scripts/*.sh

# 设备连接问题
npm run check    # 检查ADB连接

# Metro 缓存问题
npm start -- --clear
```

### 截图/录制失败

```bash
# 检查设备连接
adb devices

# 检查USB调试权限
# 设置 > 开发者选项 > USB调试 ✅
```

---

> 💡 **提示**：将常用命令添加到 IDE 的任务或快捷键中，提高开发效率！
