# 🔧 开发工作流指南

> **重要**：本项目包含自定义原生模块，需要特殊的开发流程来避免代码丢失

## 📋 日常开发流程

### 1️⃣ **热更新开发**（大部分情况）

**✅ 支持热更新的修改**：

```bash
# 修改 TypeScript/JavaScript 代码后，无需任何操作
# 支持热更新的文件类型：
- src/**/*.{ts,tsx}     # React 组件、Hook、工具类
- src/styles/*          # 样式配置
- src/config/*          # 配置文件
```

**修改后效果**：

- 保存文件后 2-3 秒自动更新
- 状态保持不变（除非组件重新挂载）
- 无需重新构建

### 2️⃣ **原生代码修改**（谨慎操作）

**❌ 需要重新构建的修改**：

```bash
# 原生模块代码修改后：
android/app/src/main/java/com/gzai168/zipco/audio/*
android/app/src/main/java/com/gzai168/zipco/MainApplication.kt
android/app/src/main/AndroidManifest.xml

# 需要运行：
yarn android
```

**修改流程**：

1. 备份代码：`./scripts/backup-native-code.sh backup`
2. 修改原生代码
3. 重新构建：`yarn android`
4. 测试功能

## 🛠️ 调试工具使用

### 📱 **截图和录屏**

```bash
# 快速截图
./scripts/debug-tools.sh screenshot

# 录制屏幕
./scripts/debug-tools.sh record

# 启动屏幕镜像（推荐安装 scrcpy）
./scripts/debug-tools.sh mirror
```

### 📊 **设备管理**

```bash
# 查看连接的设备
./scripts/debug-tools.sh devices

# 查看应用日志
./scripts/debug-tools.sh logcat

# 重启应用
./scripts/debug-tools.sh restart-app
```

### 🔄 **常用调试命令**

```bash
# 设置端口转发
./scripts/debug-tools.sh reverse

# 清理应用缓存
./scripts/debug-tools.sh clear-cache

# 安装调试 APK
./scripts/debug-tools.sh install
```

## ⚠️ **Expo Prebuild 风险管理**

### 🚨 **危险操作警告**

**绝对不要在没有备份的情况下运行**：

```bash
expo prebuild        # ❌ 会删除所有自定义原生代码！
npx expo prebuild    # ❌ 同样危险！
```

### 🛡️ **安全操作流程**

如果必须运行 `expo prebuild`：

```bash
# 1. 先备份自定义代码
./scripts/backup-native-code.sh backup

# 2. 运行危险操作
expo prebuild

# 3. 立即恢复自定义代码
./scripts/backup-native-code.sh restore

# 4. 重新构建
yarn android

# 5. 验证功能
./scripts/backup-native-code.sh check
```

### 🔧 **长期解决方案：Config Plugin**

```bash
# 创建 Expo Config Plugin（推荐）
./scripts/backup-native-code.sh create-plugin

# 然后在 app.json 中添加：
{
  "plugins": [
    "./plugins/withRealtimeAudio"
  ]
}
```

## 📦 **构建和部署**

### 🔨 **开发构建**

```bash
# 首次构建或原生代码修改后
yarn android

# 清理构建（解决奇怪问题时）
cd android && ./gradlew clean && cd ..
yarn android
```

### 🚀 **生产构建**

```bash
# 构建 Release 版本
cd android
./gradlew assembleRelease

# APK 位置
android/app/build/outputs/apk/release/app-release.apk
```

## 🐛 **常见问题解决**

### 1. **AudioProcessor 引用错误**

```bash
# 错误: Cannot find module '../services/audio/AudioProcessor'
# 解决: 已在代码清理中修复，确保使用最新代码
git pull origin master
```

### 2. **原生模块未找到**

```bash
# 错误: Unable to resolve module "RealtimeAudio"
# 解决: 检查原生代码是否存在
./scripts/backup-native-code.sh check

# 如果缺失，恢复备份
./scripts/backup-native-code.sh restore
yarn android
```

### 3. **权限被拒绝**

```bash
# 错误: Recording permission denied
# 解决: 检查权限配置
./scripts/backup-native-code.sh check

# 手动授权（设备上）
设置 > 应用 > office-assistant > 权限 > 麦克风 > 允许
```

### 4. **Metro 连接失败**

```bash
# 设置端口转发
./scripts/debug-tools.sh reverse

# 重启 Metro
yarn start --reset-cache
```

## 📝 **开发最佳实践**

### ✅ **推荐做法**

1. **频繁备份**：每次修改原生代码前先备份
2. **小步迭代**：优先在 TypeScript 层面调试逻辑
3. **使用工具**：善用调试脚本提高效率
4. **版本控制**：重要修改及时提交到 Git

### ❌ **避免做法**

1. **盲目 prebuild**：没有备份就运行 expo prebuild
2. **跳过测试**：修改原生代码后不验证功能
3. **忽略警告**：忽视编译时的警告信息
4. **手动操作**：重复的调试操作不使用脚本

## 🎯 **快速参考**

```bash
# 常用命令速查
./scripts/debug-tools.sh screenshot    # 截图
./scripts/debug-tools.sh mirror        # 屏幕镜像
./scripts/backup-native-code.sh backup # 备份原生代码
./scripts/backup-native-code.sh check  # 检查状态
yarn android                           # 重新构建
```

---

> 💡 **提示**：将此文档添加到书签，开发时随时参考！
