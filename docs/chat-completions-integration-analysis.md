# Chat Completions 接口集成分析报告

## 执行摘要

基于对 documents-assistant-h5 项目的深度分析，本报告详细说明了如何在我们的 React Native 项目中实现完整的 AI 对话功能，包括 chat/completions 接口的参数构建、消息处理流程和会话管理机制。

## 1. 关键参数分析

### 1.1 createSession 返回值覆盖情况

| 参数 | 来源 | 可用性 | 说明 |
|------|------|--------|------|
| `botId` | createSession.resultObject.botId | ✅ 可用 | 机器人ID |
| `sessionId` | createSession.resultObject.sessionId | ✅ 可用 | 会话ID |
| `tenantId` | createSession.resultObject.tenantId | ✅ 可用 | 租户ID |
| `sceneId` | 配置常量 | ❌ 需配置 | 场景ID，默认值待确定 |
| `clientId` | UUID生成 | ❌ 需生成 | 每次请求的唯一客户端ID |
| `commonFlag` | 配置常量 | ❌ 需配置 | 通用标识，通常为false |
| `webSearchEnabled` | 配置常量 | ❌ 需配置 | 网络搜索开关，通常为false |
| `deepThinkEnabled` | 配置常量 | ❌ 需配置 | 深度思考开关，通常为false |

**覆盖率**: 3/8 = 37.5%，需要补充5个参数。

### 1.2 参考项目中的参数处理方式

根据 documents-assistant-h5 项目分析：

```typescript
// 1. clientId 生成方式
const clientId = uuid(); // 使用 uuid v4

// 2. sceneId 管理方式
// - 普通对话: 不传sceneId或传空值  
// - 特定场景: 从消息参数获取或配置中设置
// - 退出场景: 设置为 '-1'

// 3. 默认配置值
const defaultChatParams = {
  commonFlag: false,
  webSearchEnabled: false, 
  deepThinkEnabled: false,
  message: {
    role: 'user',
    type: 'input', // 用户输入固定为 'input'
  }
};
```

## 2. 实现方案

### 2.1 配置常量定义

```typescript
// src/services/api/const.ts 新增配置
export const CHAT_CONFIG = {
  // 默认场景配置
  DEFAULT_SCENE_ID: undefined, // 普通对话不设置sceneId
  
  // 功能开关默认值
  DEFAULT_FLAGS: {
    commonFlag: false,
    webSearchEnabled: false,
    deepThinkEnabled: false,
  },
  
  // 消息类型配置
  MESSAGE_TYPES: {
    USER_INPUT: 'input',
    SYSTEM_POINT: 'point',
    TEXT_RESPONSE: 'text',
  },
  
  // 消息角色
  MESSAGE_ROLES: {
    USER: 'user',
    ASSISTANT: 'assistant',
    SYSTEM: 'system',
  }
} as const;
```

### 2.2 聊天服务实现

```typescript
// src/services/api/chatService.ts
import { apiClient } from './client';
import { SESSION_CONFIG, CHAT_CONFIG } from './const';
import { v4 as uuidv4 } from 'uuid';

// 聊天消息接口定义
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  type?: 'input' | 'point' | 'text';
  files?: Array<{
    fileId: string;
    fileName: string;
    fileType: string;
  }>;
}

export interface ChatCompletionsParams {
  botId: string;
  sessionId: string;
  message: ChatMessage;
  tenantId: string;
  commonFlag?: boolean;
  sceneId?: string;
  clientId: string;
  webSearchEnabled?: boolean;
  deepThinkEnabled?: boolean;
}

export interface ChatCompletionsResponse {
  resultCode: string;
  resultMsg: string;
  resultObject?: any;
  stack?: string;
  errorInfos?: any[];
}

/**
 * 构建聊天请求参数
 * 基于 createSession 返回值和用户输入构建完整的请求参数
 */
export const buildChatParams = (
  sessionData: any, // createSession 的返回值
  content: string,
  options: {
    sceneId?: string;
    webSearchEnabled?: boolean;
    deepThinkEnabled?: boolean;
    messageType?: string;
    files?: Array<any>;
  } = {}
): ChatCompletionsParams => {
  // 从 createSession 结果中提取参数
  const { botId, sessionId, tenantId } = sessionData.resultObject;
  
  // 生成唯一的客户端ID
  const clientId = uuidv4();
  
  // 构建消息对象
  const message: ChatMessage = {
    role: CHAT_CONFIG.MESSAGE_ROLES.USER,
    content,
    type: options.messageType as any || CHAT_CONFIG.MESSAGE_TYPES.USER_INPUT,
    ...(options.files && { files: options.files }),
  };
  
  // 构建完整参数
  return {
    botId: String(botId),
    sessionId: String(sessionId),
    tenantId: String(tenantId),
    message,
    clientId,
    commonFlag: CHAT_CONFIG.DEFAULT_FLAGS.commonFlag,
    sceneId: options.sceneId || CHAT_CONFIG.DEFAULT_SCENE_ID,
    webSearchEnabled: options.webSearchEnabled ?? CHAT_CONFIG.DEFAULT_FLAGS.webSearchEnabled,
    deepThinkEnabled: options.deepThinkEnabled ?? CHAT_CONFIG.DEFAULT_FLAGS.deepThinkEnabled,
  };
};

/**
 * 发送聊天消息
 * 使用统一的 apiClient，自动获得 Bearer Token 鉴权
 */
export const sendChatMessage = async (
  params: ChatCompletionsParams
): Promise<ChatCompletionsResponse> => {
  // 构建特殊的请求头（保留 BOTE 系统必需的头部）
  const headers = {
    'Tenant-Id': params.tenantId,
    'Content-Type': 'application/json',
  };

  const response = await apiClient.post<ChatCompletionsResponse>(
    `${SESSION_CONFIG.BASE_URL}/chat/completions`,
    params,
    { headers }
  );

  return response;
};

/**
 * 便捷方法：基于会话数据发送消息
 */
export const sendMessageWithSession = async (
  sessionData: any, // createSession 的返回值
  content: string,
  options: {
    sceneId?: string;
    webSearchEnabled?: boolean;
    deepThinkEnabled?: boolean;
    files?: Array<any>;
  } = {}
): Promise<ChatCompletionsResponse> => {
  const chatParams = buildChatParams(sessionData, content, options);
  return await sendChatMessage(chatParams);
};
```

### 2.3 录音相关类型扩展

```typescript
// src/types/recording.ts 扩展
export interface Recording {
  // ... 现有字段
  
  // 🆕 聊天相关字段
  sessionId?: number;
  sessionData?: Record<string, unknown>;
  chatHistory?: ChatMessage[]; // 聊天历史记录
  lastClientId?: string; // 最后一次请求的客户端ID
}

// 新增聊天消息类型
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  type: 'input' | 'point' | 'text';
  timestamp: Date;
  status?: 'sending' | 'sent' | 'error';
  clientId?: string;
  files?: Array<{
    fileId: string;
    fileName: string;
    fileType: string;
  }>;
}
```

### 2.4 AI聊天组件适配

```typescript
// src/components/recording/ai-features/AIChatContent.tsx 扩展
import React, { useState, useCallback } from 'react';
import { useRecordingStore } from '../../../stores/recordingStore';
import { 
  sendMessageWithSession,
  buildChatParams 
} from '../../../services/api/chatService';

interface AIChatContentProps {
  recording?: any;
}

const AIChatContent: React.FC<AIChatContentProps> = ({ recording }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [inputText, setInputText] = useState('');
  const updateRecording = useRecordingStore((state) => state.updateRecording);

  const handleSendMessage = useCallback(async () => {
    if (!inputText.trim() || !recording?.sessionData) {
      return;
    }

    setIsLoading(true);
    try {
      // 使用会话数据发送消息
      const response = await sendMessageWithSession(
        recording.sessionData,
        inputText.trim(),
        {
          // 可选：如果录音有转写结果，可以作为上下文
          files: recording.transcription ? [{
            fileId: recording.id,
            fileName: recording.title,
            fileType: 'audio',
          }] : undefined,
        }
      );

      console.log('✅ 消息发送成功:', response);
      
      // 更新聊天历史
      const newMessage = {
        id: Date.now().toString(),
        role: 'user' as const,
        content: inputText,
        type: 'input' as const,
        timestamp: new Date(),
        status: 'sent' as const,
      };

      const updatedChatHistory = [
        ...(recording.chatHistory || []),
        newMessage,
      ];

      // 更新录音记录
      updateRecording(recording.id, {
        chatHistory: updatedChatHistory,
      });

      // 清空输入框
      setInputText('');
    } catch (error) {
      console.error('❌ 消息发送失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, [inputText, recording, updateRecording]);

  return (
    <View style={{ flex: 1 }}>
      {/* 聊天历史显示区域 */}
      <ScrollView style={{ flex: 1 }}>
        {recording?.chatHistory?.map((message: any) => (
          <View key={message.id} style={message.role === 'user' ? styles.userMessage : styles.assistantMessage}>
            <Text>{message.content}</Text>
          </View>
        ))}
        {isLoading && (
          <View style={styles.loadingMessage}>
            <Text>正在思考中...</Text>
          </View>
        )}
      </ScrollView>

      {/* 输入区域 */}
      <View style={styles.inputContainer}>
        <TextInput
          value={inputText}
          onChangeText={setInputText}
          placeholder="点击输入与录音相关的问题"
          style={styles.textInput}
          multiline
        />
        <TouchableOpacity 
          onPress={handleSendMessage}
          disabled={isLoading || !inputText.trim()}
          style={styles.sendButton}
        >
          <Text style={styles.sendButtonText}>发送</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 16,
    margin: 8,
  },
  assistantMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#F0F0F0',
    padding: 12,
    borderRadius: 16,
    margin: 8,
  },
  loadingMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#F0F0F0',
    padding: 12,
    borderRadius: 16,
    margin: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'flex-end',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    maxHeight: 100,
  },
  sendButton: {
    marginLeft: 12,
    backgroundColor: '#007AFF',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  sendButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});

export default AIChatContent;
```

## 3. 测试和验证

### 3.1 参数构建测试

```typescript
// 测试函数
export const testChatParams = () => {
  const mockSessionData = {
    resultCode: '0',
    resultObject: {
      sessionId: 1267398439069282304,
      botId: 1266014058983452672,
      tenantId: 1265133818765635584,
    }
  };

  const chatParams = buildChatParams(
    mockSessionData,
    '你好，请帮我总结一下这个录音的内容',
    {
      webSearchEnabled: true,
      sceneId: '1266013795988008960',
    }
  );

  console.log('📝 构建的聊天参数:', JSON.stringify(chatParams, null, 2));
};
```

### 3.2 期望的输出格式

```json
{
  "botId": "1266014058983452672",
  "sessionId": "1267398439069282304",
  "message": {
    "role": "user",
    "content": "你好，请帮我总结一下这个录音的内容",
    "type": "input"
  },
  "tenantId": "1265133818765635584",
  "commonFlag": false,
  "sceneId": "1266013795988008960",
  "clientId": "6e5f7373-1511-4636-9e98-0f047ce77d68",
  "webSearchEnabled": true,
  "deepThinkEnabled": false
}
```

## 4. 实施建议

### 4.1 分阶段实施

1. **第一阶段**: 实现基础的 chatService 和参数构建逻辑
2. **第二阶段**: 集成到 AIChatContent 组件中
3. **第三阶段**: 添加聊天历史存储和状态管理
4. **第四阶段**: 实现流式响应处理（如需要）

### 4.2 注意事项

1. **认证机制**: 继续使用统一的 apiClient，确保 Bearer Token 认证
2. **错误处理**: 实现完整的错误处理和用户提示
3. **类型安全**: 严格的 TypeScript 类型定义
4. **状态管理**: 与现有的 Zustand 状态管理集成
5. **性能优化**: 考虑消息缓存和分页加载

## 5. 总结

通过对 documents-assistant-h5 项目的深入分析，我们可以在 React Native 项目中实现完整的 AI 对话功能。关键在于正确构建 chat/completions 接口的参数，特别是补充 createSession 返回值中缺失的参数（clientId、sceneId、各种功能开关等）。

实施后，用户将能够在录音详情的 AI 聊天界面中与 AI 助手进行自然对话，充分利用录音转写结果作为上下文，提供智能的问答和分析服务。

---

**文档版本**: 1.0  
**创建日期**: 2025-07-29  
**最后更新**: 2025-07-29  
**作者**: Claude Code Assistant