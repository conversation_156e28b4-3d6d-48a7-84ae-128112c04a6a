# 用户数据隔离实现

## 问题描述

当切换账号时，数据没有做隔离，不同用户之间共享相同的存储数据。

## 解决方案

通过在存储键名中添加用户ID，为每个用户创建独立的存储空间，实现数据隔离。

## 实现步骤

1. 在知识库存储(knowledgeStore.ts)和录音存储(recordingStore.ts)中添加用户ID获取和存储名称更新功能
2. 创建统一的初始化函数(initUserStorage.ts)，用于更新所有存储的名称
3. 在应用启动时调用初始化函数(App.tsx)
4. 在用户登录和登出时保存/移除用户信息并重新初始化存储

## 技术细节

### 用户ID获取

通过AsyncStorage中的'userInfo'项获取用户ID：

```typescript
const getUserId = async () => {
  try {
    const userInfo = await AsyncStorage.getItem('userInfo');
    if (!userInfo) {
      console.warn('用户信息未找到');
      return 1;
    }
    const parsedUserInfo = JSON.parse(userInfo);
    return Number(parsedUserInfo?.attributes?.userInfo?.userId) || 1;
  } catch (error) {
    console.error('获取用户ID失败:', error);
    return 1;
  }
};
```

### 存储名称更新

创建了中央化的存储名称生成工具：

```typescript
// 在 src/utils/userIdUtils.ts 中
export const generateStorageName = async (
  baseName: string
): Promise<string> => {
  const userId = await getUserId();
  return `${baseName}-user-${userId}`;
};

// 在每个store中使用
export const updateKnowledgeStorageName = async (): Promise<string> => {
  return generateStorageName('knowledge-storage');
};
```

### 存储配置优化

在Zustand的配置中使用自定义getStorage实现：

```typescript
{
  name: 'knowledge-storage',
  storage: createJSONStorage(() => AsyncStorage),
  getStorage: () => {
    // 创建一个支持动态存储键的存储对象
    return {
      getItem: async (name) => {
        const storageName = await generateStorageName(name);
        const data = await AsyncStorage.getItem(storageName);
        return data ? JSON.parse(data) : null;
      },
      setItem: async (name, value) => {
        const storageName = await generateStorageName(name);
        await AsyncStorage.setItem(storageName, JSON.stringify(value));
      },
      removeItem: async (name) => {
        const storageName = await generateStorageName(name);
        await AsyncStorage.removeItem(storageName);
      },
    };
  }
}
```

### 初始化触发点

- 应用启动时
- 用户登录成功后
- 用户登出后

## 测试方法

1. 使用用户A登录，创建一些知识库文档和录音
2. 登出，使用用户B登录
3. 验证用户B看不到用户A创建的内容
4. 用户B创建新内容
5. 再次切换回用户A，验证只能看到自己之前创建的内容
