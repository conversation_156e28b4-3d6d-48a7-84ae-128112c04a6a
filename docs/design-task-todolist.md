# AI 施工任务 TodoList

_生成时间：2025-07-21 16:16 (UTC+8)_

## 使用说明

- 所有任务已拆解为最小可执行单元，遵循 **小步快跑** 原则。
- ⬜ 待办   📝 进行中   ✅ 已完成 —— 执行过程中请实时更新状态。

---

### Phase 0 设计 Token & 组件规范

- ⬜ **在 src/styles/colors.ts 中新增渐变 token**
  - ⬜ 定义 `gradient.primary` `gradient.secondary` 等数组
  - ⬜ 导出并在现有颜色对象中加入注释
- ⬜ **创建 src/styles/radius.ts 并定义圆角常量**
  - ⬜ 定义 `radius.sm / md / lg / xl`
  - ⬜ 在现有组件中试用 `radius.lg`
- ⬜ **更新 docs/design.md 添加颜色/阴影/留白规范**
  - ⬜ 撰写渐变与阴影指引
  - ⬜ 制作配色与圆角示例图

### Phase 1 核心组件封装

- ⬜ **GradientButton** (src/components/common/GradientButton/)
  - ⬜ 创建目录及 index.tsx
  - ⬜ 新建 types.ts 编写 props 接口
  - ⬜ 新建 styles.ts 集成 react-native-linear-gradient
  - ⬜ 添加 **tests**/GradientButton.test.tsx
- ⬜ **GradientCard** (src/components/common/GradientCard/)
  - ⬜ index.tsx / types.ts / styles.ts
  - ⬜ 支持可选 header / footer slot
  - ⬜ 添加单元测试
- ⬜ **StatusPill** (src/components/common/StatusPill/)
  - ⬜ 胶囊形状，variant(success/warning/info/error)
  - ⬜ 进度模式：显示百分比动画
  - ⬜ 单元测试
- ⬜ **SwipeListItem** (src/components/common/SwipeListItem/)
  - ⬜ 集成 react-native-gesture-handler Swipeable
  - ⬜ 实现左右滑出动作区，定义 actions prop
  - ⬜ 样式文件与单元测试
- ⬜ **BottomSheetModal** (src/components/common/BottomSheetModal/)
  - ⬜ 安装 react-native-modalize
  - ⬜ 封装 open/close/hook API
  - ⬜ 支持 handle bar 与滚动内容
  - ⬜ 单元测试

### Phase 2 录音模块视觉升级

- ⬜ **RecordingScreen 重构**
  - ⬜ 顶部操作栏改用 GradientCard + GradientButton
  - ⬜ 列表项替换为 SwipeListItem
  - ⬜ 录音状态使用 StatusPill
- ⬜ **RecordingDetailScreen 重构**
  - ⬜ 摘要区使用 GradientCard
  - ⬜ 底部操作栏使用 BottomSheetModal
  - ⬜ 按钮替换为 GradientButton
- ⬜ **RecordingInProgressScreen 重构**
  - ⬜ 波形区卡片化并加渐变背景
  - ⬜ 操作按钮使用新组件
  - ⬜ 转写进度用 StatusPill

### Phase 3 AI 相关页面升级

- ⬜ **AI 速览 / 纪要**
  - ⬜ 布局改为卡片叠加
  - ⬜ 标签栏按钮用 GradientButton variant
  - ⬜ 要点条目使用 GradientCard
- ⬜ **AI 问问**
  - ⬜ 输入栏置于 BottomSheetModal
  - ⬜ 发送按钮用 GradientButton
  - ⬜ 历史对话卡片化

### Phase 4 全局 QA & 无障碍

- ⬜ 深浅模式视觉检查
- ⬜ 字体放大 120% 布局检查
- ⬜ Lighthouse/Flipper 对比度 & FPS profiling
- ⬜ 生成 QA 报告与无障碍报告

### Phase 5 发布 & 回溯

- ⬜ EAS Channel 灰度发布新 UI 版本
- ⬜ 收集监控 & 用户反馈
- ⬜ 汇总并撰写回溯报告，更新优化 Backlog
