{"resultCode": "0", "resultMsg": "success", "resultObject": {"botId": "1266014058983452672", "tenantId": "1265133818765635584", "botName": " 小兹 你的全能办公搭子", "botUse": "对录音内容进行问答", "prologue": "<span style=\"color:#64677a\">我能学习和理解人类语言，提供业务知识解答服务</span>", "botIcon": "data:image/png;base64iVBORw0KGgoAAAANSUh...", "isDefault": "F", "botStatus": "1", "pageBaseInfo": null, "pageBaseInfoJson": {"botIcon": "data:image/png;base64iVBORw0KGgoAAAANSUh...", "botName": " 小兹 你的全能办公搭子", "prologue": "<span style=\"color:#64677a\">我能学习和理解人类语言，提供业务知识解答服务</span>"}, "pageSettingInfo": "{\"themeOpt\":\"2000\",\"quickLinkSettingInfo\":null,\"questionSettingInfo\":{\"display\":true,\"list\":[{\"statusCd\":\"00A\",\"creatorId\":1222787624235388928,\"updatorId\":1222787624235388928,\"createdTime\":\"2025-07-25 20:54:38\",\"updatedTime\":\"2025-07-25 20:54:38\",\"statusTime\":null,\"remark\":null,\"tenantCode\":null,\"experienceId\":1266015722658328576,\"botId\":1266014058983452672,\"content\":\"帮我整理一下会议的待办事项\",\"type\":\"request\",\"sceneId\":null,\"title\":\"帮我整理一下会议的待办事项\",\"pageContent\":null,\"pageTemplateJson\":null,\"tenantId\":1265133818765635584,\"updatorName\":null,\"sceneName\":null,\"pageContentInfo\":null,\"sceneStatus\":null,\"staticCodeList\":null},{\"statusCd\":\"00A\",\"creatorId\":1222787624235388928,\"updatorId\":1222787624235388928,\"createdTime\":\"2025-07-25 20:54:48\",\"updatedTime\":\"2025-07-25 20:54:48\",\"statusTime\":null,\"remark\":null,\"tenantCode\":null,\"experienceId\":1266015764618145792,\"botId\":1266014058983452672,\"content\":\"帮我总结一下会议的核心要点\",\"type\":\"request\",\"sceneId\":null,\"title\":\"帮我总结一下会议的核心要点\",\"pageContent\":null,\"pageTemplateJson\":null,\"tenantId\":1265133818765635584,\"updatorName\":null,\"sceneName\":null,\"pageContentInfo\":null,\"sceneStatus\":null,\"staticCodeList\":null}]},\"scenesSettingInfo\":null,\"pointSettingInfo\":null}", "pageSettingInfoJson": {"themeOpt": "2000", "quickLinkSettingInfo": null, "questionSettingInfo": {"display": true, "list": [{"statusCd": "00A", "creatorId": "1222787624235388928", "updatorId": "1222787624235388928", "createdTime": "2025-07-25 20:54:38", "updatedTime": "2025-07-25 20:54:38", "statusTime": null, "remark": null, "tenantCode": null, "experienceId": "1266015722658328576", "botId": "1266014058983452672", "content": "帮我整理一下会议的待办事项", "type": "request", "sceneId": null, "title": "帮我整理一下会议的待办事项", "pageContent": null, "pageTemplateJson": null, "tenantId": "1265133818765635584", "updatorName": null, "sceneName": null, "pageContentInfo": null, "sceneStatus": null, "staticCodeList": null}, {"statusCd": "00A", "creatorId": "1222787624235388928", "updatorId": "1222787624235388928", "createdTime": "2025-07-25 20:54:48", "updatedTime": "2025-07-25 20:54:48", "statusTime": null, "remark": null, "tenantCode": null, "experienceId": "1266015764618145792", "botId": "1266014058983452672", "content": "帮我总结一下会议的核心要点", "type": "request", "sceneId": null, "title": "帮我总结一下会议的核心要点", "pageContent": null, "pageTemplateJson": null, "tenantId": "1265133818765635584", "updatorName": null, "sceneName": null, "pageContentInfo": null, "sceneStatus": null, "staticCodeList": null}]}, "scenesSettingInfo": null, "pointSettingInfo": null}, "agentStrategy": null, "agentStrategyJson": null, "modelId": null, "modelName": null, "isFavor": false, "defaultSceneId": null, "experiences": {"request": [{"content": "帮我总结一下会议的核心要点", "type": "request"}, {"content": "帮我整理一下会议的待办事项", "type": "request"}]}, "scenes": null}, "stack": null, "errorInfos": null, "guidance": null}