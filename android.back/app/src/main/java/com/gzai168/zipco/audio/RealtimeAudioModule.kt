package com.gzai168.zipco.audio

import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule
import android.util.Log

class RealtimeAudioModule(private val reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    
    private val audioManager = AudioStreamManager()
    private val TAG = "RealtimeAudio"
    
    override fun getName(): String = "RealtimeAudio"
    
    @ReactMethod
    fun startRecording(config: ReadableMap, promise: Promise) {
        try {
            val sampleRate = if (config.hasKey("sampleRate")) config.getInt("sampleRate") else 16000
            val interval = if (config.hasKey("interval")) config.getInt("interval") else 250
            
            Log.d(TAG, "开始录音配置: sampleRate=$sampleRate, interval=$interval")
            
            audioManager.startRecording(
                sampleRate = sampleRate,
                interval = interval,
                onAudioData = { base64Data ->
                    sendEvent("onAudioData", base64Data)
                },
                onError = { error ->
                    Log.e(TAG, "录音错误: $error")
                    sendEvent("onError", error)
                }
            )
            
            promise.resolve(WritableNativeMap().apply {
                putBoolean("success", true)
                putString("message", "录音已启动")
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "启动录音失败", e)
            promise.reject("START_RECORDING_ERROR", "启动录音失败: ${e.message}", e)
        }
    }
    
    @ReactMethod
    fun stopRecording(promise: Promise) {
        try {
            Log.d(TAG, "停止录音")
            audioManager.stopRecording()
            
            promise.resolve(WritableNativeMap().apply {
                putBoolean("success", true)
                putString("message", "录音已停止")
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "停止录音失败", e)
            promise.reject("STOP_RECORDING_ERROR", "停止录音失败: ${e.message}", e)
        }
    }
    
    @ReactMethod
    fun isRecording(promise: Promise) {
        try {
            val recording = audioManager.isRecording()
            promise.resolve(recording)
        } catch (e: Exception) {
            promise.reject("CHECK_RECORDING_ERROR", "检查录音状态失败: ${e.message}", e)
        }
    }
    
    private fun sendEvent(eventName: String, data: Any) {
        try {
            reactContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                .emit(eventName, data)
        } catch (e: Exception) {
            Log.e(TAG, "发送事件失败: $eventName", e)
        }
    }
    
    override fun onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy()
        audioManager.release()
    }
} 