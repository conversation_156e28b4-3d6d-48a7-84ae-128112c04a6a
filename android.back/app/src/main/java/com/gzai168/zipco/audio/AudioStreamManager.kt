package com.gzai168.zipco.audio

import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.util.Base64
import android.util.Log
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.*

class AudioStreamManager {
    private var audioRecord: AudioRecord? = null
    private val isRecording = AtomicBoolean(false)
    private var recordingThread: Thread? = null
    private val TAG = "AudioStreamManager"
    
    fun startRecording(
        sampleRate: Int = 16000,
        interval: Int = 250,
        onAudioData: (String) -> Unit,
        onError: (String) -> Unit
    ) {
        try {
            Log.d(TAG, "开始初始化音频录制: sampleRate=$sampleRate, interval=$interval")
            
            // 停止之前的录音（如果有）
            stopRecording()
            
            // 计算缓冲区大小
            val bufferSize = AudioRecord.getMinBufferSize(
                sampleRate,
                AudioFormat.CHANNEL_IN_MONO,
                AudioFormat.ENCODING_PCM_16BIT
            )
            
            if (bufferSize == AudioRecord.ERROR || bufferSize == AudioRecord.ERROR_BAD_VALUE) {
                throw Exception("无法获取有效的缓冲区大小")
            }
            
            Log.d(TAG, "缓冲区大小: $bufferSize bytes")
            
            // 创建AudioRecord实例
            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,        // 麦克风输入
                sampleRate,                           // 采样率
                AudioFormat.CHANNEL_IN_MONO,          // 单声道
                AudioFormat.ENCODING_PCM_16BIT,       // 16位PCM编码
                bufferSize * 2                        // 缓冲区大小（加倍确保稳定）
            )
            
            // 检查AudioRecord状态
            if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                throw Exception("AudioRecord初始化失败")
            }
            
            // 开始录音
            audioRecord?.startRecording()
            
            if (audioRecord?.recordingState != AudioRecord.RECORDSTATE_RECORDING) {
                throw Exception("AudioRecord启动录音失败")
            }
            
            isRecording.set(true)
            Log.d(TAG, "AudioRecord成功启动")
            
            // 启动录音线程
            recordingThread = Thread({
                recordingLoop(bufferSize, interval, onAudioData, onError)
            }, "AudioRecordingThread")
            
            recordingThread?.start()
            Log.d(TAG, "录音线程已启动")
            
        } catch (e: Exception) {
            Log.e(TAG, "启动录音失败", e)
            cleanupResources()
            onError("启动录音失败: ${e.message}")
        }
    }
    
    private fun recordingLoop(
        bufferSize: Int,
        interval: Int,
        onAudioData: (String) -> Unit,
        onError: (String) -> Unit
    ) {
        try {
            val buffer = ByteArray(bufferSize)
            var lastEmitTime = System.currentTimeMillis()
            var audioDataBuffer = mutableListOf<Byte>()
            
            Log.d(TAG, "开始录音循环, 发送间隔: ${interval}ms")
            
            while (isRecording.get() && !Thread.currentThread().isInterrupted) {
                try {
                    val bytesRead = audioRecord?.read(buffer, 0, bufferSize) ?: 0
                    
                    if (bytesRead > 0) {
                        // 累积音频数据
                        for (i in 0 until bytesRead) {
                            audioDataBuffer.add(buffer[i])
                        }
                        
                        val currentTime = System.currentTimeMillis()
                        
                        // 按间隔发送数据
                        if (currentTime - lastEmitTime >= interval && audioDataBuffer.isNotEmpty()) {
                            val audioBytes = audioDataBuffer.toByteArray()
                            
                            // 计算音频电平（可选，用于调试）
                            val audioLevel = calculateAudioLevel(audioBytes)
                            
                            // 转换为Base64
                            val base64Data = Base64.encodeToString(audioBytes, Base64.NO_WRAP)
                            
                            Log.d(TAG, "发送音频数据: ${audioBytes.size} bytes, 电平: ${"%.2f".format(audioLevel)}")
                            
                            // 发送数据
                            onAudioData(base64Data)
                            
                            // 清空缓冲区并更新时间
                            audioDataBuffer.clear()
                            lastEmitTime = currentTime
                        }
                    } else if (bytesRead < 0) {
                        Log.w(TAG, "读取音频数据返回错误: $bytesRead")
                        break
                    }
                    
                } catch (e: Exception) {
                    if (isRecording.get()) {
                        Log.e(TAG, "录音循环中发生错误", e)
                        onError("录音过程中发生错误: ${e.message}")
                        break
                    }
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "录音循环异常", e)
            onError("录音循环异常: ${e.message}")
        } finally {
            Log.d(TAG, "录音循环结束")
        }
    }
    
    fun stopRecording() {
        try {
            Log.d(TAG, "停止录音")
            isRecording.set(false)
            
            // 中断并等待录音线程结束
            recordingThread?.interrupt()
            recordingThread?.join(1000) // 最多等待1秒
            recordingThread = null
            
            cleanupResources()
            Log.d(TAG, "录音已停止")
            
        } catch (e: Exception) {
            Log.e(TAG, "停止录音时发生错误", e)
        }
    }
    
    fun isRecording(): Boolean = isRecording.get()
    
    fun release() {
        Log.d(TAG, "释放音频资源")
        stopRecording()
    }
    
    private fun cleanupResources() {
        try {
            audioRecord?.let { record ->
                if (record.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                    record.stop()
                }
                if (record.state == AudioRecord.STATE_INITIALIZED) {
                    record.release()
                }
            }
            audioRecord = null
            Log.d(TAG, "音频资源已清理")
            
        } catch (e: Exception) {
            Log.e(TAG, "清理音频资源时发生错误", e)
        }
    }
    
    /**
     * 计算音频数据的电平（用于调试和监控）
     */
    private fun calculateAudioLevel(audioData: ByteArray): Double {
        if (audioData.isEmpty()) return 0.0
        
        var sum = 0.0
        val samples = audioData.size / 2 // 16位音频，每2字节一个样本
        
        for (i in 0 until samples * 2 step 2) {
            if (i + 1 < audioData.size) {
                // 转换为16位有符号整数
                val sample = ((audioData[i + 1].toInt() shl 8) or (audioData[i].toInt() and 0xFF)).toShort()
                sum += abs(sample.toDouble())
            }
        }
        
        val average = if (samples > 0) sum / samples else 0.0
        return average / 32767.0 // 归一化到0-1范围
    }
} 