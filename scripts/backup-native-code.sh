#!/bin/bash

# 🛡️ 原生代码备份和恢复脚本
# 防止 expo prebuild 覆盖自定义原生代码

set -e

BACKUP_DIR="./native-code-backup"

case "$1" in
  "backup")
    echo "🛡️ 正在备份自定义原生代码..."
    
    # 创建备份目录
    mkdir -p "$BACKUP_DIR"
    
    # 备份自定义音频模块
    if [ -d "android/app/src/main/java/com/gzai168/zipco/audio" ]; then
      echo "📁 备份音频模块..."
      rm -rf "$BACKUP_DIR/audio_module"
      cp -r "android/app/src/main/java/com/gzai168/zipco/audio" "$BACKUP_DIR/audio_module"
      echo "✅ 音频模块已备份"
    else
      echo "⚠️ 未找到音频模块目录"
    fi
    
    # 备份自定义浮窗模块
    if [ -d "android/app/src/main/java/com/gzai168/zipco/floating" ]; then
      echo "📁 备份浮窗模块..."
      rm -rf "$BACKUP_DIR/floating_module"
      cp -r "android/app/src/main/java/com/gzai168/zipco/floating" "$BACKUP_DIR/floating_module"
      echo "✅ 浮窗模块已备份"
    else
      echo "⚠️ 未找到浮窗模块目录"
    fi
    
    # 备份自定义定位模块
    if [ -d "android/app/src/main/java/com/gzai168/zipco/location" ]; then
      echo "📁 备份定位模块..."
      rm -rf "$BACKUP_DIR/location_module"
      cp -r "android/app/src/main/java/com/gzai168/zipco/location" "$BACKUP_DIR/location_module"
      echo "✅ 定位模块已备份"
    else
      echo "⚠️ 未找到定位模块目录"
    fi
    
    # 备份 MainApplication.kt 修改
    if [ -f "android/app/src/main/java/com/gzai168/zipco/MainApplication.kt" ]; then
      echo "📁 备份 MainApplication.kt..."
      cp "android/app/src/main/java/com/gzai168/zipco/MainApplication.kt" "$BACKUP_DIR/MainApplication.kt"
      echo "✅ MainApplication.kt 已备份"
    fi
    
    # 备份权限配置
    if [ -f "android/app/src/main/AndroidManifest.xml" ]; then
      echo "📁 备份 AndroidManifest.xml..."
      cp "android/app/src/main/AndroidManifest.xml" "$BACKUP_DIR/AndroidManifest.xml"
      echo "✅ AndroidManifest.xml 已备份"
    fi
    
    # 备份drawable资源文件
    if [ -d "android/app/src/main/res/drawable" ]; then
      echo "📁 备份drawable资源..."
      rm -rf "$BACKUP_DIR/drawable"
      cp -r "android/app/src/main/res/drawable" "$BACKUP_DIR/drawable"
      echo "✅ drawable资源已备份"
    else
      echo "⚠️ 未找到drawable目录"
    fi
    
    echo ""
    echo "✅ 备份完成！文件列表："
    ls -la "$BACKUP_DIR/"
    ;;
    
  "restore")
    echo "🔄 正在恢复自定义原生代码..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
      echo "❌ 备份目录不存在，请先运行备份"
      exit 1
    fi
    
    # 恢复音频模块
    if [ -d "$BACKUP_DIR/audio_module" ]; then
      echo "📁 恢复音频模块..."
      mkdir -p "android/app/src/main/java/com/gzai168/zipco/audio"
      cp -r "$BACKUP_DIR/audio_module"/* "android/app/src/main/java/com/gzai168/zipco/audio/"
      echo "✅ 音频模块已恢复"
    else
      echo "⚠️ 未找到音频模块备份"
    fi
    
    # 恢复音频服务（AudioRecordingService等）
    if [ -d "$BACKUP_DIR/audio_service" ]; then
      echo "📁 恢复音频服务..."
      mkdir -p "android/app/src/main/java/com/gzai168/zipco/audio"
      cp -r "$BACKUP_DIR/audio_service"/* "android/app/src/main/java/com/gzai168/zipco/audio/"
      echo "✅ 音频服务已恢复"
    else
      echo "⚠️ 未找到音频服务备份"
    fi
    
    # 恢复浮窗模块
    if [ -d "$BACKUP_DIR/floating_module" ]; then
      echo "📁 恢复浮窗模块..."
      mkdir -p "android/app/src/main/java/com/gzai168/zipco/floating"
      cp -r "$BACKUP_DIR/floating_module"/* "android/app/src/main/java/com/gzai168/zipco/floating/"
      echo "✅ 浮窗模块已恢复"
    else
      echo "⚠️ 未找到浮窗模块备份"
    fi
    
    # 恢复定位模块
    if [ -d "$BACKUP_DIR/location_module" ]; then
      echo "📁 恢复定位模块..."
      mkdir -p "android/app/src/main/java/com/gzai168/zipco/location"
      cp -r "$BACKUP_DIR/location_module"/* "android/app/src/main/java/com/gzai168/zipco/location/"
      echo "✅ 定位模块已恢复"
    else
      echo "⚠️ 未找到定位模块备份"
    fi
    
    # 恢复 MainApplication.kt
    if [ -f "$BACKUP_DIR/MainApplication.kt" ]; then
      echo "📁 恢复 MainApplication.kt..."
      cp "$BACKUP_DIR/MainApplication.kt" "android/app/src/main/java/com/gzai168/zipco/MainApplication.kt"
      echo "✅ MainApplication.kt 已恢复"
    else
      echo "⚠️ 未找到 MainApplication.kt 备份"
    fi
    
    # 恢复 AndroidManifest.xml
    if [ -f "$BACKUP_DIR/AndroidManifest.xml" ]; then
      echo "📁 恢复 AndroidManifest.xml..."
      cp "$BACKUP_DIR/AndroidManifest.xml" "android/app/src/main/AndroidManifest.xml"
      echo "✅ AndroidManifest.xml 已恢复"
    else
      echo "⚠️ 未找到 AndroidManifest.xml 备份"
    fi
    
    # 恢复drawable资源
    if [ -d "$BACKUP_DIR/drawable" ]; then
      echo "📁 恢复drawable资源..."
      mkdir -p "android/app/src/main/res/drawable"
      cp -r "$BACKUP_DIR/drawable"/* "android/app/src/main/res/drawable/"
      echo "✅ drawable资源已恢复"
    else
      echo "⚠️ 未找到drawable资源备份"
    fi
    
    echo "✅ 恢复完成！"
    ;;
    
  "check")
    echo "🔍 检查自定义原生代码状态..."
    
    # 检查音频模块
    if [ -d "android/app/src/main/java/com/gzai168/zipco/audio" ]; then
      echo "✅ 音频模块存在"
      echo "   文件列表:"
      ls -la "android/app/src/main/java/com/gzai168/zipco/audio/"
      
      # 专门检查AudioRecordingService
      if [ -f "android/app/src/main/java/com/gzai168/zipco/audio/AudioRecordingService.kt" ]; then
        echo "✅ AudioRecordingService.kt 存在"
      else
        echo "❌ AudioRecordingService.kt 缺失！"
      fi
    else
      echo "❌ 音频模块缺失！"
    fi
    
    # 检查浮窗模块
    if [ -d "android/app/src/main/java/com/gzai168/zipco/floating" ]; then
      echo "✅ 浮窗模块存在"
      echo "   文件列表:"
      ls -la "android/app/src/main/java/com/gzai168/zipco/floating/"
    else
      echo "❌ 浮窗模块缺失！"
    fi
    
    # 检查定位模块
    if [ -d "android/app/src/main/java/com/gzai168/zipco/location" ]; then
      echo "✅ 定位模块存在"
      echo "   文件列表:"
      ls -la "android/app/src/main/java/com/gzai168/zipco/location/"
    else
      echo "❌ 定位模块缺失！"
    fi
    
    # 检查 MainApplication.kt 中的自定义包
    if grep -q "RealtimeAudioPackage" "android/app/src/main/java/com/gzai168/zipco/MainApplication.kt" 2>/dev/null; then
      echo "✅ MainApplication.kt 包含自定义音频包"
    else
      echo "❌ MainApplication.kt 缺少自定义音频包配置！"
    fi
    
    if grep -q "FloatingWindowPackage" "android/app/src/main/java/com/gzai168/zipco/MainApplication.kt" 2>/dev/null; then
      echo "✅ MainApplication.kt 包含自定义浮窗包"
    else
      echo "❌ MainApplication.kt 缺少自定义浮窗包配置！"
    fi
    
    if grep -q "NativeLocationPackage" "android/app/src/main/java/com/gzai168/zipco/MainApplication.kt" 2>/dev/null; then
      echo "✅ MainApplication.kt 包含自定义定位包"
    else
      echo "❌ MainApplication.kt 缺少自定义定位包配置！"
    fi
    
    # 检查权限
    if grep -q "android.permission.RECORD_AUDIO" "android/app/src/main/AndroidManifest.xml" 2>/dev/null; then
      echo "✅ 录音权限已配置"
    else
      echo "❌ 录音权限缺失！"
    fi
    
    if grep -q "android.permission.SYSTEM_ALERT_WINDOW" "android/app/src/main/AndroidManifest.xml" 2>/dev/null; then
      echo "✅ 浮窗权限已配置"
    else
      echo "❌ 浮窗权限缺失！"
    fi
    
    # 检查备份文件状态
    echo ""
    echo "📁 备份文件状态:"
    if [ -d "$BACKUP_DIR/audio_module" ]; then
      echo "✅ 音频模块备份存在"
    else
      echo "❌ 音频模块备份不存在"
    fi
    
    if [ -d "$BACKUP_DIR/audio_service" ]; then
      echo "✅ 音频服务备份存在"
    else
      echo "❌ 音频服务备份不存在"
    fi
    
    if [ -d "$BACKUP_DIR/floating_module" ]; then
      echo "✅ 浮窗模块备份存在"
    else
      echo "❌ 浮窗模块备份不存在"
    fi
    
    if [ -f "$BACKUP_DIR/MainApplication.kt" ]; then
      echo "✅ MainApplication.kt备份存在"
    else
      echo "❌ MainApplication.kt备份不存在"
    fi
    
    if [ -f "$BACKUP_DIR/AndroidManifest.xml" ]; then
      echo "✅ AndroidManifest.xml备份存在"
    else
      echo "❌ AndroidManifest.xml备份不存在"
    fi
    
    if [ -d "$BACKUP_DIR/location_module" ]; then
      echo "✅ 定位模块备份存在"
    else
      echo "❌ 定位模块备份不存在"
    fi
    ;;
    
  "create-plugin")
    echo "⚙️ 创建 Expo Config Plugin（推荐的长期解决方案）..."
    
    # 创建插件目录
    mkdir -p "plugins"
    
    cat > "plugins/withRealtimeAudio.js" << 'EOF'
const { withAndroidMainApplication, withAndroidManifest } = require('@expo/config-plugins');
const fs = require('fs');
const path = require('path');

function withRealtimeAudio(config) {
  // 添加录音权限
  config = withAndroidManifest(config, (config) => {
    const manifest = config.modResults;
    
    // 确保权限存在
    if (!manifest['uses-permission']) {
      manifest['uses-permission'] = [];
    }
    
    const hasRecordPermission = manifest['uses-permission'].some(
      permission => permission.$['android:name'] === 'android.permission.RECORD_AUDIO'
    );
    
    if (!hasRecordPermission) {
      manifest['uses-permission'].push({
        $: { 'android:name': 'android.permission.RECORD_AUDIO' }
      });
    }
    
    return config;
  });
  
  // 修改 MainApplication
  config = withAndroidMainApplication(config, (config) => {
    const mainApplication = config.modResults;
    
    // 添加导入
    if (!mainApplication.contents.includes('import com.gzai168.zipco.audio.RealtimeAudioPackage')) {
      mainApplication.contents = mainApplication.contents.replace(
        'import expo.modules.ReactNativeHostWrapper',
        'import expo.modules.ReactNativeHostWrapper\nimport com.gzai168.zipco.audio.RealtimeAudioPackage'
      );
    }
    
    // 添加包到 getPackages
    if (!mainApplication.contents.includes('packages.add(RealtimeAudioPackage())')) {
      mainApplication.contents = mainApplication.contents.replace(
        'val packages = PackageList(this).packages.toMutableList()',
        'val packages = PackageList(this).packages.toMutableList()\n            packages.add(RealtimeAudioPackage())'
      );
    }
    
    return config;
  });
  
  return config;
}

module.exports = withRealtimeAudio;
EOF
    
    echo "✅ Expo Config Plugin 已创建: plugins/withRealtimeAudio.js"
    echo ""
    echo "📝 现在需要在 app.json 中添加插件："
    echo '  "plugins": ['
    echo '    "./plugins/withRealtimeAudio"'
    echo '  ]'
    ;;
    
    
  "help"|*)
    echo "🛡️ 原生代码备份工具使用说明:"
    echo ""
    echo "基本操作:"
    echo "  backup           - 备份当前的自定义原生代码（覆盖式备份）"
    echo "  restore          - 恢复备份的原生代码"
    echo "  check            - 检查自定义代码和备份状态"
    echo ""
    echo "高级功能:"
    echo "  create-plugin    - 创建 Expo Config Plugin（推荐）"
    echo ""
    echo "⚠️ 重要提醒:"
    echo "- 在运行 expo prebuild 之前，务必先运行 backup"
    echo "- prebuild 后立即运行 restore 恢复自定义代码"
    echo "- 使用git进行版本控制，备份只保留最新版本"
    echo "- 建议使用 create-plugin 创建配置插件，实现自动化"
    echo ""
    echo "使用示例:"
    echo "  ./scripts/backup-native-code.sh backup"
    echo "  expo prebuild  # 危险操作"
    echo "  ./scripts/backup-native-code.sh restore"
    ;;
esac
