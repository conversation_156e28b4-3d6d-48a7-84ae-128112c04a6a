#!/bin/bash

echo "🔧 修复应用图标..."

# 复制图标
echo "📋 复制 logo.png 到所需的图标文件..."
cp assets/logo.png assets/icon.png
cp assets/logo.png assets/adaptive-icon.png

# 清理
echo "🧹 清理旧的构建文件..."
rm -rf android
npx expo start -c &
EXPO_PID=$!
sleep 5
kill $EXPO_PID 2>/dev/null || true

# 重新构建
echo "🏗️  重新生成 Android 项目..."
npx expo prebuild --platform android --clear

# 构建 APK
echo "📦 构建新的 APK..."
cd android
./gradlew assembleDebug
cd ..

# 显示结果
APK_PATH="android/app/build/outputs/apk/debug/app-debug.apk"
if [ -f "$APK_PATH" ]; then
    echo "✅ 图标修复完成！"
    echo "📱 APK 位置: $APK_PATH"
    echo ""
    echo "下一步操作："
    echo "1. 卸载旧版本: adb uninstall com.gzai168.zipco"
    echo "2. 安装新版本: adb install $APK_PATH"
else
    echo "❌ 构建失败"
fi
