#!/bin/bash

# Android APK 构建脚本
# 使用方法: ./scripts/build-android.sh [debug|release|eas]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 构建类型
BUILD_TYPE=${1:-debug}

print_info "开始构建 Android APK (${BUILD_TYPE})"

# 检查必要的工具
if ! command_exists node; then
    print_error "Node.js 未安装"
    exit 1
fi

if ! command_exists npm; then
    print_error "npm 未安装"
    exit 1
fi

# 清理缓存
print_info "清理缓存..."
npx expo start -c --no-dev --minify &
EXPO_PID=$!
sleep 5
kill $EXPO_PID 2>/dev/null || true

case $BUILD_TYPE in
    "debug")
        print_info "构建 Debug APK..."
        
        # 检查是否已经有 android 目录
        if [ ! -d "android" ]; then
            print_info "生成 Android 项目..."
            npx expo prebuild --platform android
        fi
        
        cd android
        ./gradlew assembleDebug
        cd ..
        
        APK_PATH="android/app/build/outputs/apk/debug/app-debug.apk"
        if [ -f "$APK_PATH" ]; then
            print_info "✅ Debug APK 构建成功！"
            print_info "📱 APK 位置: $APK_PATH"
            print_info "📦 APK 大小: $(du -h "$APK_PATH" | cut -f1)"
        else
            print_error "构建失败，找不到 APK 文件"
            exit 1
        fi
        ;;
        
    "release")
        print_info "构建 Release APK..."
        
        # 检查是否有签名文件
        if [ ! -f "zipco-release.keystore" ]; then
            print_warning "未找到签名文件 zipco-release.keystore"
            print_info "请先生成签名文件，参考 docs/android-build-guide.md"
            exit 1
        fi
        
        # 检查是否已经有 android 目录
        if [ ! -d "android" ]; then
            print_info "生成 Android 项目..."
            npx expo prebuild --platform android
        fi
        
        cd android
        ./gradlew assembleRelease
        cd ..
        
        APK_PATH="android/app/build/outputs/apk/release/app-release.apk"
        if [ -f "$APK_PATH" ]; then
            print_info "✅ Release APK 构建成功！"
            print_info "📱 APK 位置: $APK_PATH"
            print_info "📦 APK 大小: $(du -h "$APK_PATH" | cut -f1)"
            
            # 复制到项目根目录
            cp "$APK_PATH" "zipco-release-$(date +%Y%m%d-%H%M%S).apk"
            print_info "📋 已复制到: zipco-release-$(date +%Y%m%d-%H%M%S).apk"
        else
            print_error "构建失败，找不到 APK 文件"
            exit 1
        fi
        ;;
        
    "eas")
        print_info "使用 EAS Build 构建..."
        
        # 检查 eas-cli 是否安装
        if ! command_exists eas; then
            print_warning "EAS CLI 未安装，正在安装..."
            npm install -g eas-cli
        fi
        
        # 检查是否登录
        if ! eas whoami >/dev/null 2>&1; then
            print_info "请登录 Expo 账号..."
            eas login
        fi
        
        # 选择构建配置
        print_info "选择构建配置:"
        echo "1) preview - 测试版 APK"
        echo "2) production - 生产版 APK"
        read -p "请选择 (1/2): " choice
        
        case $choice in
            1)
                eas build --platform android --profile preview --local
                ;;
            2)
                eas build --platform android --profile production --local
                ;;
            *)
                print_error "无效的选择"
                exit 1
                ;;
        esac
        ;;
        
    *)
        print_error "无效的构建类型: $BUILD_TYPE"
        echo "使用方法: $0 [debug|release|eas]"
        exit 1
        ;;
esac

print_info "构建完成！"

# 提示后续操作
echo ""
print_info "后续操作提示:"
echo "1. 安装到设备: adb install <apk文件路径>"
echo "2. 查看签名: keytool -printcert -jarfile <apk文件路径>"
echo "3. 查看包信息: aapt dump badging <apk文件路径>"
