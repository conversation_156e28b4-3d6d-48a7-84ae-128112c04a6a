#!/bin/bash

# 🔧 React Native 真机调试工具集
# 使用方法: ./scripts/debug-tools.sh [命令]

set -e

SCREENSHOT_DIR="./debug-screenshots"
mkdir -p "$SCREENSHOT_DIR"

case "$1" in
  "screenshot"|"ss")
    echo "📱 正在截取手机屏幕..."
    timestamp=$(date +"%Y%m%d_%H%M%S")
    filename="screenshot_${timestamp}.png"
    
    # 方法1: 直接保存到电脑（推荐）
    adb exec-out screencap -p > "$SCREENSHOT_DIR/$filename"
    echo "✅ 截图已保存: $SCREENSHOT_DIR/$filename"
    
    # 自动打开截图（macOS）
    if [[ "$OSTYPE" == "darwin"* ]]; then
      open "$SCREENSHOT_DIR/$filename"
    fi
    ;;
    
  "record")
    echo "🎥 开始录制手机屏幕（最长3分钟）..."
    timestamp=$(date +"%Y%m%d_%H%M%S")
    filename="recording_${timestamp}.mp4"
    
    echo "按 Ctrl+C 停止录制"
    adb shell screenrecord "/sdcard/$filename" &
    record_pid=$!
    
    # 等待用户停止
    trap "kill $record_pid 2>/dev/null; adb pull /sdcard/$filename $SCREENSHOT_DIR/ && adb shell rm /sdcard/$filename && echo '✅ 录制已保存: $SCREENSHOT_DIR/$filename'" INT
    wait $record_pid
    ;;
    
  "devices")
    echo "📱 当前连接的设备:"
    adb devices -l
    ;;
    
  "logcat")
    echo "📋 开始查看应用日志（过滤React Native）..."
    echo "按 Ctrl+C 停止"
    adb logcat | grep -E "(ReactNative|RealtimeAudio|AudioStreamManager)"
    ;;
    
  "install")
    echo "📦 安装调试APK..."
    apk_path="android/app/build/outputs/apk/debug/app-debug.apk"
    if [ -f "$apk_path" ]; then
      adb install -r "$apk_path"
      echo "✅ APK安装完成"
    else
      echo "❌ APK文件不存在，请先运行 yarn android"
    fi
    ;;
    
  "mirror")
    echo "🖥️ 启动屏幕镜像（需要安装scrcpy）..."
    if command -v scrcpy &> /dev/null; then
      scrcpy --stay-awake --turn-screen-off
    else
      echo "❌ scrcpy未安装"
      echo "安装方法："
      echo "  macOS: brew install scrcpy"
      echo "  Windows: scoop install scrcpy"
      echo "  Linux: apt install scrcpy"
    fi
    ;;
    
  "reverse")
    echo "🔄 设置端口转发（用于Metro连接）..."
    adb reverse tcp:8081 tcp:8081
    echo "✅ 端口转发已设置: device:8081 -> computer:8081"
    ;;
    
  "clear-cache")
    echo "🧹 清理应用缓存..."
    adb shell pm clear com.gzai168.zipco
    echo "✅ 应用缓存已清理"
    ;;
    
  "restart-app")
    echo "🔄 重启应用..."
    adb shell am force-stop com.gzai168.zipco
    adb shell monkey -p com.gzai168.zipco -c android.intent.category.LAUNCHER 1
    echo "✅ 应用已重启"
    ;;
    
  "help"|*)
    echo "🔧 React Native 调试工具使用说明:"
    echo ""
    echo "截图和录制:"
    echo "  screenshot, ss    - 截取手机屏幕"
    echo "  record           - 录制手机屏幕"
    echo "  mirror           - 启动屏幕镜像（需要scrcpy）"
    echo ""
    echo "设备管理:"
    echo "  devices          - 查看连接的设备"
    echo "  reverse          - 设置端口转发"
    echo "  install          - 安装调试APK"
    echo ""
    echo "应用调试:"
    echo "  logcat           - 查看应用日志"
    echo "  clear-cache      - 清理应用缓存"
    echo "  restart-app      - 重启应用"
    echo ""
    echo "使用示例:"
    echo "  ./scripts/debug-tools.sh screenshot"
    echo "  ./scripts/debug-tools.sh record"
    echo "  ./scripts/debug-tools.sh mirror"
    ;;
esac 