# 胶囊协调系统待解决问题

## 📋 当前状态
- ✅ **应用内胶囊正常工作** - RN胶囊在APP内正常显示和交互
- ❌ **原生胶囊未显示** - APP切换到后台时，原生胶囊没有出现

## 🚧 已完成的架构改进

### 1. 核心组件实现
- ✅ `CapsulePositionManager.kt` - 位置管理器，支持SharedPreferences持久化
- ✅ `CapsuleCoordinator.kt` - 中央协调器，管理胶囊互斥显示和位置同步
- ✅ `FloatingWindowManager.kt` - 扩展版原生浮窗管理器，集成协调器支持
- ✅ `FloatingWindowModule.kt` - 扩展版RN桥接模块，提供新API
- ✅ `RecordingCapsule.tsx` - 扩展版RN胶囊，支持位置同步
- ✅ `useFloatingWindow.ts` - 更新版Hook，使用新的胶囊协调系统

### 2. 新增功能特性
- ✅ 位置同步机制 - SharedPreferences + 坐标转换
- ✅ APP生命周期监听 - ActivityLifecycleCallbacks
- ✅ 点击唤醒功能 - Intent启动机制
- ✅ 事件通信系统 - DeviceEventEmitter
- ✅ 向后兼容 - 旧API重定向到新API

### 3. 编译问题修复
- ✅ 添加缺失的Log导入
- ✅ 修复变量初始化问题
- ✅ 解决变量名冲突 (tag -> logTag)

## ❌ 待解决问题

### 问题描述
**APP切换到后台时，原生胶囊没有显示**

### 可能原因分析

#### 1. RecordingScreen集成问题
- 📍 位置：`src/screens/recording/RecordingScreen.tsx:268-271`
- ❓ 问题：可能startFloatingRecording()调用时机不对
- 🔧 检查：录音开始时是否正确调用了胶囊协调系统

#### 2. 生命周期监听问题
- 📍 位置：`CapsuleCoordinator.kt:ActivityLifecycleCallbacks`
- ❓ 问题：可能APP前后台状态检测不准确
- 🔧 检查：onActivityStopped/onActivityStarted是否正确触发

#### 3. 原生胶囊显示逻辑
- 📍 位置：`CapsuleCoordinator.kt:showNativeCapsule()`
- ❓ 问题：可能FloatingWindowManager未正确显示浮窗
- 🔧 检查：权限、WindowManager、布局参数

#### 4. 模块初始化问题
- 📍 位置：`FloatingWindowModule.kt:initializeCapsuleCoordinator()`
- ❓ 问题：可能CapsuleCoordinator初始化失败
- 🔧 检查：应用启动时的初始化流程

#### 5. 事件通信问题
- 📍 位置：`CapsuleCoordinator.kt:sendEventToRN()`
- ❓ 问题：可能FloatingWindowModule引用为null
- 🔧 检查：双向引用设置是否正确

## 🔍 调试建议

### 1. 日志检查
```bash
# 查看关键日志标签
adb logcat | grep -E "(CapsuleCoordinator|FloatingWindowManager|FloatingWindowModule)"
```

### 2. 初始化验证
```javascript
// 在RecordingScreen中添加调试代码
console.log('胶囊协调系统支持:', floatingWindowSupported);
console.log('录音状态:', isFloatingRecordingActive);
```

### 3. 权限检查
```kotlin
// 在CapsuleCoordinator中添加权限检查日志
Log.d(tag, "浮窗权限状态: ${floatingWindowManager?.hasOverlayPermission()}")
```

### 4. 状态跟踪
```kotlin
// 在onAppBackgrounded中添加详细日志
Log.d(tag, "APP进入后台 - 录音状态: ${isRecordingActive.get()}, 原生胶囊状态: ${isNativeCapsuleShowing.get()}")
```

## 🛠️ 修复步骤建议

### Step 1: 验证基础功能
1. 确认FloatingWindowModule正确初始化
2. 确认CapsuleCoordinator.setFloatingWindowModule()被调用
3. 确认权限已授予

### Step 2: 检查生命周期
1. 验证ActivityLifecycleCallbacks注册成功
2. 测试APP前后台切换是否触发回调
3. 确认isAppInForeground状态正确

### Step 3: 调试显示逻辑
1. 检查showNativeCapsule()中的FloatingWindowManager.showFloatingWindow()调用
2. 验证currentRecordingData不为null
3. 检查WindowManager.addView()是否成功

### Step 4: 测试事件通信
1. 验证sendEventToRN()中的FloatingWindowModule引用
2. 测试DeviceEventEmitter事件是否正确发送
3. 确认RN端事件监听器正常工作

## 📚 相关文件清单

### 原生代码
- `native-code-backup/floating_module/CapsuleCoordinator.kt`
- `native-code-backup/floating_module/CapsulePositionManager.kt`
- `native-code-backup/floating_module/FloatingWindowManager.kt`
- `native-code-backup/floating_module/FloatingWindowModule.kt`
- `native-code-backup/floating_module/INTEGRATION_GUIDE.md`

### RN代码
- `src/hooks/useFloatingWindow.ts`
- `src/components/recording/RecordingCapsule.tsx`
- `src/screens/recording/RecordingScreen.tsx`

### 恢复脚本
- `scripts/backup-native-code.sh`

## 🎯 期望最终效果

1. **APP前台** → 显示RN胶囊，隐藏原生胶囊
2. **APP后台** → 隐藏RN胶囊，显示原生胶囊
3. **位置同步** → 拖拽任一胶囊，另一个胶囊位置自动同步
4. **点击唤醒** → 点击原生胶囊，APP回到前台并显示RN胶囊
5. **状态一致** → 录音时长、状态在两个胶囊间保持同步

## 📝 备注
- 当前应用内功能正常，说明基础架构没问题
- 问题集中在APP后台时的原生胶囊显示逻辑
- 优先检查生命周期监听和初始化流程