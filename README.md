# 办公助手 - 移动端应用

## 📚 项目文档

### 开发指南

- [Android原生代码管理指南](./docs/android-native-code-management.md) - Expo项目Android目录git追踪策略和最佳实践
- [Android构建指南](./docs/android-build-guide.md) - Android平台构建配置和流程
- [调试指南](./docs/debug-vs-release.md) - Debug vs Release构建差异说明
- [快速构建指南](./docs/quick-build-guide.md) - 项目快速构建和部署流程

### 功能实现

- [产品需求文档](./docs/product-requirements.md) - 详细的产品功能需求和规格
- [设计文档](./docs/design.md) - UI/UX设计规范和组件说明
- [任务管理](./docs/tasks.md) - 开发任务和进度追踪

### 技术文档

- [讯飞语音识别集成](./docs/xunfei.md) - 语音转文字功能实现详解
- [实时音频录制修复](./docs/real-audio-fix-summary.md) - 录音功能问题解决方案
- [转录连接修复](./docs/transcription-connection-fix.md) - 语音转录服务连接问题修复

### 项目状态

- [完成情况总结](./docs/completion-summary.md) - 项目完成度和里程碑
- [项目状态](./docs/project-status.md) - 当前开发状态和计划

## 🚀 快速开始

### 环境要求

- Node.js 18+
- Yarn
- Expo CLI
- Android Studio (Android开发)
- Xcode (iOS开发)

### 安装依赖

```bash
yarn install
```

### 启动开发服务器

```bash
yarn start
```

### 平台构建

```bash
# Android
yarn android

# iOS
yarn ios
```

## 📱 功能特性

### 核心功能

- 🎙️ **智能录音** - 高质量音频录制和实时转写
- 📝 **AI文档处理** - 智能文档生成和编辑
- 🔍 **知识管理** - 文档搜索和分类管理
- ⚙️ **个性化设置** - 用户偏好和应用配置

### 技术特性

- 📱 React Native + Expo开发框架
- 🎵 实时音频处理和转录
- 🤖 AI智能分析和内容生成
- 📊 本地数据存储和同步
- 🔐 用户认证和数据安全

## 🛠️ 开发规范

### 代码规范

- 使用TypeScript进行开发
- 遵循ESLint和Prettier配置
- 组件化开发模式
- Git提交规范

### 项目结构

```
src/
├── components/     # 可复用组件
├── screens/        # 页面组件
├── hooks/          # 自定义Hooks
├── services/       # 业务服务
├── stores/         # 状态管理
├── styles/         # 样式定义
├── types/          # 类型定义
└── utils/          # 工具函数
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交变更 (`git commit -m 'Add some AmazingFeature'`)
4. 推送分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者：开发团队
- 邮箱：<EMAIL>
- 项目链接：[https://github.com/yourorg/office-assistant-hybrid](https://github.com/yourorg/office-assistant-hybrid)
