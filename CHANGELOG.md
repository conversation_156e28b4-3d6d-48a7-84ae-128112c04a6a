# 变更日志

## [Android 定位问题修复] - 2025-07-28

### 🚀 重大修复

#### 解决 Android 设备定位失败问题

- **问题根因**: 中国大陆 Android 设备缺少 Google Play Services，导致 Expo Location 模块无法工作
- **错误信息**: `LocationServices.API is not available on this device`
- **解决方案**: 实现自定义原生定位模块，直接使用 Android LocationManager API

### 🔧 技术实现

#### 新增原生定位模块

- `NativeLocationModule.kt`: 核心 Android 定位模块
- `NativeLocationPackage.kt`: React Native 包装器
- `NativeLocationService.ts`: JavaScript 端服务封装
- `MainApplication.kt`: 注册原生模块到应用

#### 智能定位策略

- **多级回退**: 原生定位 → Expo Location → 模拟位置
- **多种提供者**: GPS、网络、被动定位
- **配置优化**: 低精度(8s) → 中精度(12s) → 高精度(15s)
- **超时控制**: 防止无限等待，确保用户体验

### 📊 性能特性

- **高成功率**: 支持所有 Android 设备，无 Google Play Services 依赖
- **快速定位**: 网络定位通常 1-3 秒完成
- **省电优化**: 优先使用网络定位，减少 GPS 使用
- **智能缓存**: 优先获取最后已知位置

### 🐛 问题修复

- 修复 `LocationServices.API is not available` 错误
- 修复 `CONNECTION_SUSPENDED` 连接中断
- 修复 `SERVICE_INVALID` 服务无效错误
- 解决中国大陆设备 100% 定位失败问题

### 📝 文档更新

- 新增 `docs/location-fix-solution.md` 详细解决方案文档
- 新增 `docs/location-testing-guide.md` 完整测试指南
- 更新项目架构，增加原生定位支持

### 🎯 兼容性保证

- **iOS 设备**: 继续使用 Expo Location（原生支持良好）
- **Android + Google Play**: 可选择 Expo Location 或原生模块
- **Android 无 Google Play**: 自动使用原生定位模块
- **向后兼容**: 无需修改现有业务代码

---

## [音频管道优化] - 2025-07-25

### 🚀 重大改进

#### 音频处理管道完全重构

- **移除Float32Array转换链**: 原生PCM数据直接传输给讯飞ASR，消除精度损失
- **修复AudioRecord配置**: 采用4倍缓冲区，确保连续音频数据流
- **移除人工静音数据**: 不再发送人工制造的静音数据，避免干扰真实音频
- **单一连接管理**: 解决多连接冲突，确保只有一个讯飞ASR连接

### 🔧 技术改动

#### 原生层 (Kotlin)

- `AudioStreamManager.kt`: 修复缓冲区配置不一致问题
- `AudioStreamManager.kt`: 移除人工静音填充逻辑
- `RealtimeAudioModule.kt`: 优化PCM数据桥接

#### JavaScript层

- `XunfeiRealtimeASR.ts`: 移除人工静音数据发送逻辑
- `XunfeiRealtimeASR.ts`: 删除64行未使用的解析代码
- `useRealtimeAudioRecorder.ts`: 移除复杂Float32Array转换
- `RecordingModal.tsx`: 移除独立ASR连接，改为接收props
- `RecordingScreen.tsx`: 作为唯一ASR连接管理者
- `useRealtimeTranscription.ts`: 简化配置和连接逻辑

### 📊 性能提升

- **零转换损失**: 原生PCM直接传输，无中间格式转换
- **内存优化**: 减少Float32Array对象创建
- **CPU负载降低**: 移除复杂音频处理计算
- **连接稳定性**: 单一连接避免10800错误

### 🐛 问题修复

- 修复 `10800: over max connect limit` 连接数超限错误
- 修复 `37005: Client idle timeout` 客户端空闲超时
- 修复AudioRecord频繁返回0字节的问题
- 移除人工静音数据对真实音频的干扰

### 📝 文档

- 新增 `docs/audio-pipeline-optimization.md` 详细改造文档
- 更新 `CHANGELOG.md` 记录本次重大改进

### 🎯 影响范围

- **性能**: 显著提升音频识别准确率和系统性能
- **稳定性**: 解决连接冲突和音频流中断问题
- **代码质量**: 移除256行冗余代码，简化架构
- **用户体验**: 实现真正的实时连续音频流识别

---

_本次改造实现了用户要求的"最简单、最有效"音频处理方案，彻底优化了音频管道架构。_
